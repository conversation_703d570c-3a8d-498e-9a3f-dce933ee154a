<x-studentLayout>
    <div class="full-width-container px-4 py-4">
        <div class="p-8 bg-white border border-gray-200 shadow-xl rounded-1xl">
            {{-- Title + Navigation --}}
            <div class="flex items-center justify-between mb-6">
                <h1 class="text-2xl font-bold text-orange-700">GOING OUT LOGS MONITOR</h1>
                <div class="flex items-center space-x-4">

                    <form action="{{ route('goingout.monitor') }}" method="GET" class="flex items-center space-x-2">
                        <input type="date" name="date" value="{{ $selectedDate }}"
                            class="px-3 py-2 text-sm border border-gray-300 rounded-sm focus:outline-none focus:ring-2 focus:ring-orange-500">
                        <button type="submit"
                            class="px-4 py-2 text-sm font-semibold text-white bg-orange-500 rounded-sm hover:bg-orange-600">
                            Filter
                        </button>
                    </form>

                    <a href="{{ route('educator.dashboard') }}"
                        class="inline-flex items-center text-sm font-medium text-blue-600 hover:underline">
                        <i data-feather="arrow-left" class="w-5 h-5 mr-1"></i> Back to Dashboard
                    </a>
                </div>
            </div>

            {{-- Search and Filters --}}
            <div class="mb-6">
                {{-- Single Row: Search + Filters --}}
                <div class="flex flex-col lg:flex-row gap-4 items-end">
                    {{-- Search Bar (Left Side) --}}
                    <div class="flex-1 lg:max-w-md">
                        <label for="studentSearch" class="block text-sm font-medium text-gray-700 mb-1">Search Students</label>
                        <div class="relative">
                            <input type="text" id="studentSearch" placeholder="Search by ID, First Name, or Last Name..."
                                   class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 pr-8">
                            <button type="button" id="clearSearch" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 hidden">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    {{-- Batch Filter --}}
                    <div class="flex-1 lg:max-w-xs">
                        <label for="batchFilter" class="block text-sm font-medium text-gray-700 mb-1">Filter by Batch</label>
                        <select id="batchFilter"
                            class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                            <option value="">All Batches</option>
                            @foreach ($batches as $batch)
                                <option value="{{ $batch }}">{{ $batch }}</option>
                            @endforeach
                        </select>
                    </div>

                    {{-- Group Filter --}}
                    <div class="flex-1 lg:max-w-xs">
                        <label for="groupFilter" class="block text-sm font-medium text-gray-700 mb-1">Filter by Group</label>
                        <select id="groupFilter"
                            class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                            <option value="">All Groups</option>
                            @foreach ($groups as $group)
                                <option value="{{ $group }}">{{ $group }}</option>
                            @endforeach
                        </select>
                    </div>

                    {{-- Status Filter --}}
                    <div class="flex-1 lg:max-w-xs">
                        <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-1">Filter by Status</label>
                        <select id="statusFilter"
                            class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                            <option value="">All Students</option>
                            <option value="not-logged-in">Not Logged In Yet</option>
                            <option value="late">Late Students</option>
                            <option value="absent">Not Going Out Students</option>
                        </select>
                    </div>
               </div>

                {{-- Search Results Info --}}
                <div id="searchResults" class="hidden mt-3">
                    <div class="text-sm text-gray-600">
                        <span id="searchCount">0</span> student(s) found
                    </div>
                </div>
            </div>

            {{-- Table --}}
            <div class="relative w-full overflow-hidden border border-gray-200 rounded-lg">
                <div class="w-full overflow-x-auto table-container">
                    <div class="overflow-y-auto max-h-[calc(100vh-200px)] table-container">
                        <table class="w-full min-w-full text-sm text-left text-gray-700">
                            <thead class="sticky top-0 z-10 text-xs font-semibold tracking-wider uppercase bg-gray-100">
                                <tr>
                                    <th class="px-6 py-3 text-black">Student ID</th>
                                    <th class="px-6 py-3 text-black">Student Name</th>
                                    <th class="px-6 py-3 text-black">Batch</th>
                                    <th class="px-6 py-3 text-black">Group</th>
                                    <th class="px-6 py-3 text-black">Date</th>
                                     <th class="px-6 py-3 text-black">Session</th>
                                    <th class="px-6 py-3 text-black">Destination</th>
                                    <th class="px-6 py-3 text-black">Purpose</th>
                                    <th class="px-6 py-3 text-black">Log Out Time</th>
                                    <th class="px-6 py-3 text-black">Log Out Remark</th>
                                    <th class="px-6 py-3 text-black">Log Out Consideration</th>
                                    <th class="px-6 py-3 text-black">Log In Time</th>
                                    <th class="px-6 py-3 text-black">Log In Remark</th>
                                    <th class="px-6 py-3 text-black">Log In Consideration</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200">
                                @foreach ($goingOutLogs as $index => $log)
                                    @php
                                        // Let JavaScript handle all color coding dynamically
                                        $rowClass = 'hover:bg-gray-50';
                                    @endphp
                                    <tr class="{{ $rowClass }}"
                                        data-batch="{{ $log->studentDetail->batch }}"
                                        data-group="{{ $log->studentDetail->group }}"
                                        data-student-id="{{ $log->studentDetail->student_id }}"
                                        data-student-fname="{{ strtolower($log->studentDetail->user->user_fname) }}"
                                        data-student-lname="{{ strtolower($log->studentDetail->user->user_lname) }}"
                                        data-student-fullname="{{ strtolower($log->studentDetail->user->user_fname . ' ' . $log->studentDetail->user->user_lname) }}">
                                        <td class="px-6 py-4">{{ $log->studentDetail->student_id }}</td>
                                        <td class="px-6 py-4">{{ $log->studentDetail->user->user_fname }}
                                            {{ $log->studentDetail->user->user_lname }}</td>
                                        <td class="px-6 py-4">{{ $log->studentDetail->batch }}</td>
                                        <td class="px-6 py-4">{{ $log->studentDetail->group }}</td>
                                        <td class="px-6 py-4">
                                            {{ \Carbon\Carbon::parse($log->going_out_date)->format('F j, Y') }}</td>
                                             <td class="px-6 py-4">
                                            <span class="px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
                                                Session {{ $log->session_number ?? 1 }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4">{{ $log->destination ?? '—' }}</td>
                                        <td class="px-6 py-4">{{ $log->purpose ?? '—' }}</td>
                                        <td class="px-6 py-4">{{ $log->formatted_time_out }}</td>
                                        <td class="px-6 py-4">
                                            <span
                                                class="{{ $log->time_out_remark === 'Late' ? 'text-red-600 font-bold' : ($log->time_out_remark === 'On Time' ? 'text-blue-600 font-bold' : ($log->time_out_remark === 'Early' ? 'text-green-600 font-bold' : '')) }}">
                                                {{ $log->time_out_remark ?? '—' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4">
                                            {{-- Log Out Consideration - VIEW ONLY for Educator --}}
                                            @if($log->time_out_consideration)
                                                <div class="p-3 bg-gray-50 rounded-lg border">
                                                    <div class="font-semibold text-sm {{ $log->time_out_consideration === 'Excused' ? 'text-green-600' : (($log->time_out_consideration === 'Absent' || $log->time_out_consideration === 'Not going out') ? 'text-gray-600' : 'text-red-600') }}">
                                                        {{ $log->time_out_consideration === 'Absent' ? 'Not going out' : $log->time_out_consideration }}
                                                    </div>
                                                    @if($log->time_out_reason)
                                                        <div class="text-xs text-gray-600 mt-1 italic">{{ $log->time_out_reason }}</div>
                                                    @endif
                                                    <div class="text-xs text-blue-600 mt-1 font-medium">
                                                        Set by: {{ $log->time_out_monitor_name ?: 'Monitor' }}
                                                    </div>
                                                </div>
                                            @else
                                                <span class="text-gray-500 italic">No consideration set</span>
                                            @endif
                                        </td>
                                        <td class="px-6 py-4">{{ $log->formatted_time_in }}</td>
                                        <td class="px-6 py-4">
                                            <span
                                                class="{{ $log->time_in_remark === 'Late' ? 'text-red-600 font-bold' : ($log->time_in_remark === 'On Time' ? 'text-blue-600 font-bold' : ($log->time_in_remark === 'Early' ? 'text-green-600 font-bold' : '')) }}">
                                                {{ $log->time_in_remark ?? '—' }}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4">
                                            {{-- Log In Consideration - VIEW ONLY for Educator --}}
                                            @if($log->educator_consideration)
                                                <div class="p-3 bg-gray-50 rounded-lg border">
                                                    <div class="font-semibold text-sm {{ $log->educator_consideration === 'Excused' ? 'text-green-600' : (($log->educator_consideration === 'Absent' || $log->educator_consideration === 'Not going out') ? 'text-gray-600' : 'text-red-600') }}">
                                                        {{ $log->educator_consideration === 'Absent' ? 'Not going out' : $log->educator_consideration }}
                                                    </div>
                                                    @if($log->time_in_reason)
                                                        <div class="text-xs text-gray-600 mt-1 italic">{{ $log->time_in_reason }}</div>
                                                    @endif
                                                    <div class="text-xs text-blue-600 mt-1 font-medium">
                                                        Set by: {{ $log->time_in_monitor_name ?: 'Monitor' }}
                                                    </div>
                                                </div>
                                            @else
                                                <span class="text-gray-500 italic">No consideration set</span>
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Custom CSS for Full Width --}}
    <style>
        /* Override parent layout constraints for full width */
        .full-width-container {
            width: 100vw;
            max-width: none;
            margin-left: calc(-50vw + 50%);
            margin-right: calc(-50vw + 50%);
        }

        /* Ensure table cells don't wrap unnecessarily */
        .table-cell-nowrap {
            white-space: nowrap;
        }

        /* Small light gray scrollbars for all elements */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 3px;
            transition: background 0.2s ease;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }

        ::-webkit-scrollbar-corner {
            background: #f8f9fa;
        }

        /* Firefox scrollbar styling */
        * {
            scrollbar-width: thin;
            scrollbar-color: #d1d5db #f8f9fa;
        }

        /* Latest Activity Color Coding */
        .bg-orange-150 {
            background-color: #fed7aa;
        }
        .hover\:bg-orange-150:hover {
            background-color: #fdba74;
        }
        .bg-gray-250 {
            background-color: #e5e7eb;
        }
        .hover\:bg-gray-250:hover {
            background-color: #d1d5db;
        }

        /* Search functionality styles */
        .search-hidden {
            display: none !important;
        }
    </style>

    {{-- Feather Icons --}}
    <script src="https://cdn.jsdelivr.net/npm/feather-icons/dist/feather.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Feather Icons
            feather.replace();

            // Filter and search functionality
            const batchFilter = document.getElementById('batchFilter');
            const groupFilter = document.getElementById('groupFilter');
               const statusFilter = document.getElementById('statusFilter');
            const studentSearch = document.getElementById('studentSearch');
            const clearSearch = document.getElementById('clearSearch');
            const searchResults = document.getElementById('searchResults');
            const searchCount = document.getElementById('searchCount');
            const tableRows = document.querySelectorAll('tbody tr');

            // Set initial values from URL parameters
            const urlParams = new URLSearchParams(window.location.search);
            const batchParam = urlParams.get('batch');
            const groupParam = urlParams.get('group');

            if (batchParam) {
                batchFilter.value = batchParam;
                updateGroupFilter(batchParam);
            }
            if (groupParam) {
                groupFilter.value = groupParam;
            }

            // Disable group filter initially if no batch is selected
            if (!batchParam) {
                groupFilter.disabled = true;
            }

            // Search functionality
            function performSearch() {
                const searchTerm = studentSearch.value.toLowerCase().trim();

                if (searchTerm === '') {
                    clearSearch.classList.add('hidden');
                    searchResults.classList.add('hidden');

                    // Reset all rows to visible when search is empty
                    tableRows.forEach(row => {
                        row.classList.remove('search-hidden');
                        row.style.display = '';
                    });

                    applyFilters();
                    return;
                }

                clearSearch.classList.remove('hidden');
                searchResults.classList.remove('hidden');

                let matchCount = 0;
                let firstMatch = null;

                tableRows.forEach(row => {
                    const studentId = row.getAttribute('data-student-id') || '';
                    const firstName = row.getAttribute('data-student-fname') || '';
                    const lastName = row.getAttribute('data-student-lname') || '';
                    const fullName = row.getAttribute('data-student-fullname') || '';

                    const isMatch = studentId.toLowerCase().includes(searchTerm) ||
                                   firstName.includes(searchTerm) ||
                                   lastName.includes(searchTerm) ||
                                   fullName.includes(searchTerm);

                    if (isMatch) {
                        matchCount++;
                        if (!firstMatch) firstMatch = row;
                        row.classList.remove('search-hidden');
                    } else {
                        row.classList.add('search-hidden');
                    }
                });

                searchCount.textContent = matchCount;

                // Scroll to first match
                if (firstMatch) {
                    firstMatch.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }

                // Apply filters after search
                applyFilters();
            }

            function clearSearchFunction() {
                studentSearch.value = '';
                clearSearch.classList.add('hidden');
                searchResults.classList.add('hidden');

                // Remove search-hidden class and reset all rows to visible
                tableRows.forEach(row => {
                    row.classList.remove('search-hidden');
                    // Reset row display to empty string to show all rows
                    row.style.display = '';
                });

                // Apply filters only for batch/group, not search
                applyFilters();
            }

            function updateGroupFilter(selectedBatch) {
                // Clear and disable group filter if no batch is selected
                if (!selectedBatch) {
                    groupFilter.value = '';
                    groupFilter.disabled = true;
                    return;
                }

                // Enable group filter
                groupFilter.disabled = false;

                // Get unique groups for the selected batch
                const availableGroups = new Set();
                tableRows.forEach(row => {
                    if (row.getAttribute('data-batch') === selectedBatch) {
                        availableGroups.add(row.getAttribute('data-group'));
                    }
                });

                // Update group filter options
                groupFilter.innerHTML = '<option value="">All Groups</option>';
                Array.from(availableGroups).sort().forEach(group => {
                    const option = document.createElement('option');
                    option.value = group;
                    option.textContent = group;
                    if (group === groupParam) {
                        option.selected = true;
                    }
                    groupFilter.appendChild(option);
                });
            }

            function applyFilters() {
                const selectedBatch = batchFilter.value;
                const selectedGroup = groupFilter.value;
                const selectedStatus = statusFilter.value;
                const hasSearchTerm = studentSearch.value.trim() !== '';

                // Update URL with current filters
                const url = new URL(window.location.href);
                if (selectedBatch) {
                    url.searchParams.set('batch', selectedBatch);
                } else {
                    url.searchParams.delete('batch');
                }
                if (selectedGroup) {
                    url.searchParams.set('group', selectedGroup);
                } else {
                    url.searchParams.delete('group');
                }
                window.history.replaceState({}, '', url);

                // Filter and reapply color coding
                let visibleRows = [];
                tableRows.forEach(row => {
                    const rowBatch = row.getAttribute('data-batch');
                    const rowGroup = row.getAttribute('data-group');

                    let shouldShow = true;

                    if (selectedBatch) {
                        shouldShow = rowBatch === selectedBatch;
                        if (selectedGroup) {
                            shouldShow = shouldShow && (rowGroup === selectedGroup);
                        }
                    }

                       // Apply status filter
                    if (selectedStatus && shouldShow) {
                        shouldShow = shouldShow && checkStudentStatusGoingOut(row, selectedStatus);
                    }

                    // Only apply search filter if there's an active search term
                    if (hasSearchTerm) {
                        shouldShow = shouldShow && !row.classList.contains('search-hidden');
                    }

                    row.style.display = shouldShow ? '' : 'none';

                    if (shouldShow) {
                        visibleRows.push(row);
                    }
                });

                // First, remove all color classes from ALL rows (visible and hidden)
                tableRows.forEach(row => {
                    row.className = row.className.replace(/bg-orange-100|hover:bg-orange-150|border-l-4|border-orange-500|bg-gray-200|hover:bg-gray-250|border-gray-500|bg-yellow-800|bg-opacity-20|hover:bg-yellow-800|hover:bg-opacity-30|border-yellow-800/g, '').trim();

                    // Add base classes
                    if (!row.className.includes('hover:bg-gray-50')) {
                        row.className += ' hover:bg-gray-50';
                    }
                });

                // Filter visible rows that have actual activity (time_out or time_in)
                let activeVisibleRows = visibleRows.filter(row => {
                    const timeOutCell = row.cells[5]; // Log Out Time column
                    const timeInCell = row.cells[7]; // Log In Time column

                    // Check for actual time values using timestamp pattern detection
                    const timeOutText = timeOutCell ? timeOutCell.textContent.trim() : '';
                    const timeInText = timeInCell ? timeInCell.textContent.trim() : '';

                    // Use regex to detect actual timestamp patterns (e.g., "2:30 PM", "14:30", "02:30:45")
                    const timePattern = /\d{1,2}:\d{2}(\s*(AM|PM|am|pm))?/;

                    // Only consider it as activity if it contains actual timestamp
                    const hasTimeOut = timeOutText &&
                                      timeOutText !== '—' &&
                                      timeOutText !== '' &&
                                      timePattern.test(timeOutText);

                    const hasTimeIn = timeInText &&
                                     timeInText !== '—' &&
                                     timeInText !== '' &&
                                     timePattern.test(timeInText);

                    return hasTimeOut || hasTimeIn;
                });

                // Apply color coding ONLY to active visible rows
                activeVisibleRows.forEach((row, activeIndex) => {
                    if (activeIndex === 0) {
                        row.className += ' bg-orange-100 hover:bg-orange-100 border-l-4 border-orange-500'; // 1st latest - Orange
                    } else if (activeIndex === 1 && activeVisibleRows.length >= 2) {
                        row.className += ' bg-gray-200 hover:bg-gray-200 border-l-4 border-gray-500'; // 2nd latest - Silver
                    } else if (activeIndex === 2 && activeVisibleRows.length >= 3) {
                        row.className += '  bg-gray-100 hover:bg-gray-100 border-l-4 border-gray-500'; // 3rd latest - Brown
                    }
                });
            }

            // Search event listeners
            studentSearch.addEventListener('input', performSearch);
            clearSearch.addEventListener('click', clearSearchFunction);

            // When batch changes, update group filter and apply filters
            batchFilter.addEventListener('change', function() {
                const selectedBatch = this.value;
                updateGroupFilter(selectedBatch);
                applyFilters();
            });

            // When group changes, apply filters
            groupFilter.addEventListener('change', applyFilters);

              // When status changes, apply filters
            statusFilter.addEventListener('change', applyFilters);

            // Function to check student status for going-out filtering
            function checkStudentStatusGoingOut(row, statusFilter) {
                const timeOutCell = row.cells[8]; // Log Out Time column
                const timeInCell = row.cells[11]; // Log In Time column
                const timeOutRemarkCell = row.cells[9]; // Log Out Remarks column
                const timeInRemarkCell = row.cells[12]; // Log In Remarks column
                const timeOutConsiderationCell = row.cells[10]; // Log Out Consideration column
                const timeInConsiderationCell = row.cells[13]; // Log In Consideration column

                const timeOutText = timeOutCell ? timeOutCell.textContent.trim() : '';
                const timeInText = timeInCell ? timeInCell.textContent.trim() : '';
                const timeOutRemark = timeOutRemarkCell ? timeOutRemarkCell.textContent.trim() : '';
                const timeInRemark = timeInRemarkCell ? timeInRemarkCell.textContent.trim() : '';
                const timeOutConsideration = timeOutConsiderationCell ? timeOutConsiderationCell.textContent.trim() : '';
                const timeInConsideration = timeInConsiderationCell ? timeInConsiderationCell.textContent.trim() : '';

                // Use regex to detect actual timestamp patterns
                const timePattern = /\d{1,2}:\d{2}(\s*(AM|PM|am|pm))?/;
                const hasTimeOut = timeOutText && timePattern.test(timeOutText) && timeOutText !== '—';
                const hasTimeIn = timeInText && timePattern.test(timeInText) && timeInText !== '—';

                switch (statusFilter) {
                    case 'not-logged-in':
                        // Students who haven't logged in yet (no time_in)
                        return !hasTimeIn;

                    case 'late':
                        // Students who are late (either log out or log in)
                        return (timeOutRemark === 'Late') || (timeInRemark === 'Late');

                    case 'absent':
                        // Students who are marked as absent
                        return timeOutConsideration.includes('Absent') || timeInConsideration.includes('Absent');

                    default:
                        return true;
                }
            }

            // Apply initial filters (this will also apply initial colors)
            applyFilters();
        });

        function validateConsideration(event, hasTimeIn, timeInRemark) {
            const form = event.target;
            const errorDiv = form.nextElementSibling;
            const select = form.querySelector('select');
            const selectedValue = select.value;

            if (!hasTimeIn) {
                event.preventDefault();
                errorDiv.textContent = "Cannot set consideration: Student must log time in first";
                errorDiv.classList.remove('hidden');
                return false;
            }

            if (timeInRemark !== 'Late') {
                event.preventDefault();
                errorDiv.textContent = "Cannot set consideration: Student must be late to set any consideration";
                errorDiv.classList.remove('hidden');
                return false;
            }

            errorDiv.classList.add('hidden');
            return true;
        }

        function handleTimeOutConsiderationSubmit(event, logId, hasTimeOut, timeOutRemark) {
            event.preventDefault();
            console.log('Time Out Form submission started', { logId, hasTimeOut, timeOutRemark });

            // Validate first
            if (!validateTimeOutConsideration(event, hasTimeOut, timeOutRemark)) {
                console.log('Time Out Validation failed');
                return false;
            }

            const form = event.target;
            const formData = new FormData(form);
            const errorDiv = document.getElementById(`timeout-error-message-${logId}`);
            const successDiv = document.getElementById(`timeout-success-message-${logId}`);
            const submitButton = form.querySelector('button[type="submit"]');
            const select = form.querySelector('select');

            // Disable the form while submitting
            submitButton.disabled = true;
            select.disabled = true;

            // Clear previous messages
            errorDiv.classList.add('hidden');
            successDiv.classList.add('hidden');

            // Get CSRF token from meta tag
            const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': token,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    successDiv.textContent = data.message;
                    successDiv.classList.remove('hidden');
                    submitButton.disabled = true;
                    select.disabled = true;
                    select.value = formData.get('educator_consideration');
                } else {
                    errorDiv.textContent = data.message || 'An error occurred while updating the consideration.';
                    errorDiv.classList.remove('hidden');
                    submitButton.disabled = false;
                    select.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                errorDiv.textContent = 'An error occurred while updating the consideration.';
                errorDiv.classList.remove('hidden');
                submitButton.disabled = false;
                select.disabled = false;
            });

            return false;
        }

        function handleTimeInConsiderationSubmit(event, logId, hasTimeIn, timeInRemark) {
            event.preventDefault();
            console.log('Time In Form submission started', { logId, hasTimeIn, timeInRemark });

            // Validate first
            if (!validateConsideration(event, hasTimeIn, timeInRemark)) {
                console.log('Time In Validation failed');
                return false;
            }

            const form = event.target;
            const formData = new FormData(form);
            const errorDiv = document.getElementById(`timein-error-message-${logId}`);
            const successDiv = document.getElementById(`timein-success-message-${logId}`);
            const submitButton = form.querySelector('button[type="submit"]');
            const select = form.querySelector('select');

            // Disable the form while submitting
            submitButton.disabled = true;
            select.disabled = true;

            // Clear previous messages
            errorDiv.classList.add('hidden');
            successDiv.classList.add('hidden');

            // Get CSRF token from meta tag
            const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

            fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': token,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    successDiv.textContent = data.message;
                    successDiv.classList.remove('hidden');
                    submitButton.disabled = true;
                    select.disabled = true;
                    select.value = formData.get('educator_consideration');
                } else {
                    errorDiv.textContent = data.message || 'An error occurred while updating the consideration.';
                    errorDiv.classList.remove('hidden');
                    submitButton.disabled = false;
                    select.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                errorDiv.textContent = 'An error occurred while updating the consideration.';
                errorDiv.classList.remove('hidden');
                submitButton.disabled = false;
                select.disabled = false;
            });

            return false;
        }

        function validateTimeOutConsideration(event, hasTimeOut, timeOutRemark) {
            const form = event.target;
            const errorDiv = form.nextElementSibling;
            const select = form.querySelector('select');

            if (!hasTimeOut) {
                event.preventDefault();
                errorDiv.textContent = "Cannot set consideration: Student must log time out first";
                errorDiv.classList.remove('hidden');
                return false;
            }

            if (timeOutRemark !== 'Late') {
                event.preventDefault();
                errorDiv.textContent = "Cannot set consideration: Student must be late to set any consideration";
                errorDiv.classList.remove('hidden');
                return false;
            }

            errorDiv.classList.add('hidden');
            return true;
        }

        // Horizontal scroll function
        function scrollTable(direction) {
            const tableContainer = document.querySelector('.overflow-x-auto');
            const scrollAmount = 300; // Adjust this value to control scroll distance

            if (direction === 'left') {
                tableContainer.scrollLeft -= scrollAmount;
            } else {
                tableContainer.scrollLeft += scrollAmount;
            }
        }
    </script>
</x-studentLayout>
