<?php

use App\Console\Commands\PopulateAcademicLogData;
use App\Console\Commands\PopulateGoingOutData;
use App\Console\Commands\ScheduleDeletion;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;


Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

Schedule::call(function () {
    (new PopulateGoingOutData())->handle();
})->daily();

Schedule::call(function () {
    (new PopulateAcademicLogData())->handle();
})->daily();

Schedule::call(function () {
    (new ScheduleDeletion())->handle();
});
