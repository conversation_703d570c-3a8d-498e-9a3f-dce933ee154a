<?php

namespace App\Console\Commands;

use App\Models\StudentDetail;
use App\Models\Academic;
use Illuminate\Console\Command;

class PopulateAcademicLogData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:populate-academiclog-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Fetch all students
        $students = StudentDetail::all();

        // Get today's date
        $date = now()->format('Y-m-d');

        foreach ($students as $student) {
            $data[] = [
                'student_id' => $student->student_id,
                'academic_date' => $date,
                'time_out' => null,
                'time_in' => null,
                'time_out_remark' => null,
                'time_in_remark' => null,
            ];
        }

        Academic::insert($data);
        $this->info('Academic Log data populated successfully for ' . $date);
    }
}
