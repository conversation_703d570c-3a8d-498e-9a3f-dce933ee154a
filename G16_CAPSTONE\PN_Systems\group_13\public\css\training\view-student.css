.view-student-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.view-student-container h1 {
    text-align: center;
    margin-bottom: 2rem;
    color: #333;
}

.student-details {
    margin-bottom: 2rem;
}

.detail-row {
    display: flex;
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    flex: 1;
    font-weight: bold;
    color: #555;
}

.detail-value {
    flex: 2;
    color: #333;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.btn {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.2s;
}

.btn-warning {
    background-color: #ff9933;
    color: #000;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.btn-secondary {
    background-color: #22bbea;
    color: #fff;
}

.btn-secondary:hover {
    background-color: #5a6268;
} 