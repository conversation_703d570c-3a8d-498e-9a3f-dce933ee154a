<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Room Task History - Tasking Hub System</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <style>
        body{
            font-family: 'Poppins', sans-serif;
            margin: 0;
            background: #f6f8fa;
        }
        header {
            font-family: 'Poppins', sans-serif;
            background-color: #22BBEA;
            color: white;
            padding: 20px;
            display: flex;
            align-items: center;
        }
        .logo {
            font-family: 'Poppins', sans-serif;
            margin-left: 0;
        }
        .logo img {
            width: 270px; 
            height: auto;
            margin-left: 0;
        }
        .header-right {
            font-family: 'Poppins', sans-serif;
            flex: 1;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            font-size: 18px;
            font-weight: 500;
        }
        .container-fluid {
            font-family: 'Poppins', sans-serif;
            display: flex;
            min-height: 100vh;
            min-width: 1200px;
        }
        .sidebar {
            font-family: 'Poppins', sans-serif;
            border-right: 3px solid #22BBEA;
            background: #f8f9fa;
            min-width: 230px;
            max-width: 260px;
            padding-top: 20px;
            padding-bottom: 30px;
            height: 200vh;
        }
        .sidebar ul {
            font-family: 'Poppins', sans-serif;
            list-style: none;
            padding-left: 0;
            margin: 0;
        }
        .sidebar ul li {
            font-family: 'Poppins', sans-serif;
            margin: 15px 0;
            border-radius: 5px;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        .sidebar ul li a {
            font-family: 'Poppins', sans-serif;
            text-decoration: none;
            color: black;
            display: flex;
            align-items: center;
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 18px;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        .sidebar ul li:hover {
            font-family: 'Poppins', sans-serif;
            background: #fa5408;
            color: white;
            max-width: 100%;
        }
        .sidebar ul li:hover a {
            font-family: 'Poppins', sans-serif;
            color: white;
        }
        .sidebar-icon {
            width: 30px;
            height: 30px;
            margin-right: 8px;
            vertical-align: middle;
        }
        .content {
            font-family: 'Poppins', sans-serif;
            flex: 1;
            padding: 0;
            background: #f6f8fa;
        }
        /* --- Restore table/content design --- */
        .main-content-wrapper {
            font-family: 'Poppins', sans-serif;
            padding: 40px 30px 30px 30px;
            max-width: 1400px;
            margin: 0 auto;
        }
        .main-content-wrapper h1 {
            font-family: 'Poppins', sans-serif;
            font-size: 2.2rem;
            font-weight: 700;
            color: #222;
            margin-bottom: 28px;
        }
        .main-content-wrapper form {
            font-family: 'Poppins', sans-serif;
            margin-bottom: 32px;
        }
        .main-content-wrapper .flex {
            font-family: 'Poppins', sans-serif;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .main-content-wrapper .flex-1 {
            font-family: 'Poppins', sans-serif;
            flex: 1;
        }
        .main-content-wrapper input[type="text"] {
            font-family: 'Poppins', sans-serif;
            font-size: 1rem;
            padding: 10px 14px;
            border-radius: 6px;
            border: 1px solid #ccc;
            width: 100%;
        }
        .main-content-wrapper select {
            font-family: 'Poppins', sans-serif;
            font-size: 1rem;
            padding: 10px 14px;
            border-radius: 6px;
            border: none;
            min-width: 120px;
            background-color: #22BBEA;
            color: white;
        }
        .main-content-wrapper button,
        .main-content-wrapper a#clearButton {
            font-family: 'Poppins', sans-serif;
            font-size: 1rem;
            padding: 10px 18px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            margin-left: 2px;
        }
        .main-content-wrapper button#searchButton {
            font-family: 'Poppins', sans-serif;
            background-color: #119416;
            color: white;
        }
        .main-content-wrapper a#clearButton {
            font-family: 'Poppins', sans-serif;
            background-color: #e3342f;
            color: white;
            text-decoration: none;
        }
        .main-content-wrapper .bg-blue-50 {
            font-family: 'Poppins', sans-serif;
            background: #eaf6fb;
        }
        .main-content-wrapper .border-blue-200 {
            font-family: 'Poppins', sans-serif;
            border-color: #b6e0f7;
        }
        .main-content-wrapper .rounded {
            font-family: 'Poppins', sans-serif;
            border-radius: 10px;
        }
        .main-content-wrapper .mb-6 {
            font-family: 'Poppins', sans-serif;
            margin-bottom: 1.5rem;
        }
        .main-content-wrapper .p-4 {
            font-family: 'Poppins', sans-serif;
            padding: 1.25rem;
        }
        .main-content-wrapper .text-blue-800 {
            font-family: 'Poppins', sans-serif;
            color: #155e75;
        }
        .main-content-wrapper .text-blue-700 {
            font-family: 'Poppins', sans-serif;
            color: #1d4e89;
        }
        .main-content-wrapper .overflow-x-auto {
            font-family: 'Poppins', sans-serif;
            overflow-x: auto;
        }
        .main-content-wrapper table {
            font-family: 'Poppins', sans-serif;
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(34,187,234,0.07);
        }
        .main-content-wrapper th, .main-content-wrapper td {
            font-family: 'Poppins', sans-serif;
            border: 1px solid #e2e8f0;
            padding: 12px 10px;
        }
        .main-content-wrapper th {
            font-family: 'Poppins', sans-serif;
            background:rgb(74, 114, 247);
            color: white;
            font-weight: 600;
            text-align: center;
        }
        .main-content-wrapper tr.bg-white:hover {
            font-family: 'Poppins', sans-serif;
            background: #f1f5f9;
        }
        .main-content-wrapper .w-6 {
            font-family: 'Poppins', sans-serif;
            width: 24px;
        }
        .main-content-wrapper .h-6 {
            font-family: 'Poppins', sans-serif;
            height: 24px;
        }
        .main-content-wrapper .bg-green-500 {
            font-family: 'Poppins', sans-serif;
            background: #08a821;
        }
        .main-content-wrapper .bg-red-500 {
            font-family: 'Poppins', sans-serif;
            background: #e61515;
        }
        .main-content-wrapper .rounded-full {
            font-family: 'Poppins', sans-serif;
            border-radius: 9999px;
        }
        .main-content-wrapper .flex.items-center.justify-center.mx-auto {
            font-family: 'Poppins', sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: auto;
            margin-right: auto;
        }
        .main-content-wrapper .text-white {
            font-family: 'Poppins', sans-serif;
            color: #fff;
        }
        .main-content-wrapper .text-gray-400 {
            font-family: 'Poppins', sans-serif;
            color: #9ca3af;
        }
        .main-content-wrapper .text-gray-800 {
            font-family: 'Poppins', sans-serif;
            color: #222;
        }
        .main-content-wrapper .text-gray-700 {
            font-family: 'Poppins', sans-serif;
            color: #444;
        }
        .main-content-wrapper .text-gray-50 {
            font-family: 'Poppins', sans-serif;
            color: #f9fafb;
        }
        .main-content-wrapper .text-center {
            font-family: 'Poppins', sans-serif;
            text-align: center;
        }
        .main-content-wrapper .py-8 {
            font-family: 'Poppins', sans-serif;
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
        .main-content-wrapper .mb-2 {
            font-family: 'Poppins', sans-serif;
            margin-bottom: 0.5rem;
        }
        .main-content-wrapper .mb-4 {
            font-family: 'Poppins', sans-serif;
            margin-bottom: 1rem;
        }
        .main-content-wrapper .mb-6 {
            font-family: 'Poppins', sans-serif;
            margin-bottom: 1.5rem;
        }
        .main-content-wrapper .mt-6 {
            font-family: 'Poppins', sans-serif;
            margin-top: 1.5rem;
        }
        .main-content-wrapper .p-6 {
            font-family: 'Poppins', sans-serif;
            padding: 1.5rem;
        }
        @media (max-width: 1200px) {
            .main-content-wrapper {
                font-family: 'Poppins', sans-serif;
                padding: 20px 5vw 20px 5vw;
                max-width: 100vw;
            }
        }
        @media (max-width: 900px) {
            .logo img { width: 100px; }
            .sidebar { font-family: 'Poppins', sans-serif; min-width: 60px; max-width: 80px; padding-top: 10px; }
            .sidebar ul li a {
                font-family: 'Poppins', sans-serif;
                font-size: 13px;
                padding: 8px 8px;
            }
            .sidebar-icon {
                /* ...existing code... */
            }
            .main-content-wrapper { font-family: 'Poppins', sans-serif; padding: 8px; }
            .main-content-wrapper h1 { font-family: 'Poppins', sans-serif; font-size: 1.2rem; }
            .main-content-wrapper th, .main-content-wrapper td { font-family: 'Poppins', sans-serif; padding: 6px 4px; font-size: 12px; }
        }
        @media (max-width: 700px) {
            .container-fluid {
                font-family: 'Poppins', sans-serif;
                flex-direction: column;
            }
            .sidebar {
                font-family: 'Poppins', sans-serif;
                width: 100vw;
                min-width: 0;
                max-width: 100vw;
                height: auto;
                border-right: none;
                border-bottom: 3px solid #22BBEA;
                flex-direction: row;
                padding: 8px 0 8px 0;
                align-items: center;
                justify-content: flex-start;
                overflow-x: auto;
            }
            .sidebar ul {
                font-family: 'Poppins', sans-serif;
                display: flex;
                flex-direction: row;
                width: 100vw;
                justify-content: flex-start;
                align-items: center;
            }
            .sidebar ul li {
                font-family: 'Poppins', sans-serif;
                margin: 0 6px;
            }
            .sidebar ul li a {
                font-family: 'Poppins', sans-serif;
                padding: 6px 6px;
                font-size: 12px;
            }
            .main-content-wrapper {
                font-family: 'Poppins', sans-serif;
                padding: 4vw 2vw;
            }
            .main-content-wrapper table, .main-content-wrapper thead, .main-content-wrapper tbody, .main-content-wrapper th, .main-content-wrapper td, .main-content-wrapper tr {
                font-family: 'Poppins', sans-serif;
                display: block;
            }
            .main-content-wrapper thead { 
                font-family: 'Poppins', sans-serif;
                display: none;
            }
            .main-content-wrapper tr {
                font-family: 'Poppins', sans-serif;
                margin-bottom: 1.2em;
                border-bottom: 2px solid #e2e8f0;
            }
            .main-content-wrapper td {
                font-family: 'Poppins', sans-serif;
                border: none;
                position: relative;
                padding-left: 50%;
                min-height: 32px;
                text-align: left !important;
                font-size: 13px;
            }
            .main-content-wrapper td:before {
                font-family: 'Poppins', sans-serif;
                position: absolute;
                top: 0;
                left: 8px;
                width: 45%;
                white-space: nowrap;
                font-weight: bold;
                color: #22BBEA;
                content: attr(data-label);
            }
            .roomSearchInput{
                font-family: 'Poppins', sans-serif;
            }
        }
    </style>
</head>
<body>
  <header>
    <div class="logo">
      <img src="<?php echo e(asset('images/pnlogo-header.png')); ?>" alt="PN Logo">
    </div>
    <div class="header-right">
      <!-- Example: You can add user info or logout here if needed -->
    </div>
  </header>
  <div class="container-fluid">
    <nav class="sidebar">
      <ul class="nav flex-column">
        <li class="nav-item">
          <a href="<?php echo e(route('dashboard')); ?>" class="nav-link sidebar-link">
            <img src="<?php echo e(asset('images/dashboard.png')); ?>" class="sidebar-icon">Dashboard
          </a>
        </li>
        <li class="nav-item">
          <a href="<?php echo e(route('roomtask')); ?>" class="nav-link">
            <img src="<?php echo e(asset('images/checklist.png')); ?>" class="sidebar-icon">Room Tasks
          </a>
        </li>
        <li class="nav-item">
          <a href="#" class="nav-link">
            <img src="<?php echo e(asset('images/assign.png')); ?>" class="sidebar-icon">General Tasks
          </a>
        </li>
        <li class="nav-item">
          <a href="<?php echo e(route('task.history')); ?>" class="nav-link"> 
            <img src="<?php echo e(asset('images/history.png')); ?>" class="sidebar-icon">Room Task History
          </a>
        </li>
        <li class="nav-item">
          <li class="nav-item"><a href="<?php echo e(route('reports.index')); ?>" class="nav-link">
          <img src="<?php echo e(asset('images/complaint.png')); ?>" class="sidebar-icon">Reports</a></li>
        </li>
        <li class="nav-item">
          <a href="#" class="nav-link">
            <img src="<?php echo e(asset('images/log-out.png')); ?>" class="sidebar-icon">Log Out
          </a>
        </li>
      </ul>
    </nav>
    <div class="content">
      <div class="main-content-wrapper" style="padding: 32px 10px 24px 10px; max-width: 1400px;">
        <!-- Move info box here, above the flex row -->
         <div style="flex: 1; margin-left: 0;">
            <!-- Search/filter form -->
            <form method="GET" action="<?php echo e(route('task.history')); ?>" class="mb-6">
              <div class="flex items-center gap-4" style="display: flex; align-items: center; gap: 12px; width: 100%;">
                <div class="relative flex-1" style="flex: 1; display: flex; align-items: center; position: relative;">
                  <input id="roomSearchInput" name="room" type="text" value="" class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="Enter Room Number" style="padding-left: 2.2rem; min-width: 180px; height: 26px;"/>
                  <span style="position: absolute; left: 14px; top: 50%; transform: translateY(-50%); color: #9ca3af; pointer-events: none;">
                    <!-- Flaticon magnifying glass SVG icon -->
                    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="#22BBEA" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><line x1="21" y1="21" x2="16.65" y2="16.65"/></svg>
                  </span>
                </div>
                <select id="monthSelect" name="month" style="height: 44px;">
                  <?php for($i = 1; $i <= 12; $i++): ?>
                    <option value="<?php echo e($i); ?>" <?php echo e((isset($month) && $month == $i) ? 'selected' : ''); ?>>
                      <?php echo e(date('F', mktime(0, 0, 0, $i, 1))); ?>

                    </option>
                  <?php endfor; ?>
                </select>
                <select id="yearSelect" name="year" style="height: 44px;">
                  <?php for($i = 2023; $i <= 2040; $i++): ?>
                    <option value="<?php echo e($i); ?>" <?php echo e((isset($year) && $year == $i) ? 'selected' : ''); ?>><?php echo e($i); ?></option>
                  <?php endfor; ?>
                </select>
                <select id="weekSelect" name="week" style="height: 44px;">
                  <?php for($i = 1; $i <= 5; $i++): ?>
                    <option value="<?php echo e($i); ?>" <?php echo e((isset($week) && $week == $i) ? 'selected' : ''); ?>>Week <?php echo e($i); ?></option>
                  <?php endfor; ?>
                </select>
                <button type="submit" id="searchButton" style="height: 44px; display: flex; align-items: center;">Search</button>
                <a href="<?php echo e(route('task.history')); ?>" id="clearButton" style="height: 25px; display: flex; align-items: center;">Clear</a>
              </div>
            </form>
        <?php
          $noHistory = empty($studentNames);
        ?>
        <?php if(!$noHistory): ?>
        <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded" id="historyInfoBox" style="margin-left:0; margin-right:0;">
          <h2 class="text-xl font-semibold text-blue-800 mb-2">
            Task History for Room <?php echo e($room ?? '?'); ?> – 
            Week <?php echo e($week ?? '?'); ?> of 
            <?php echo e(isset($month) ? (is_numeric($month) ? date('F', mktime(0,0,0,$month,1)) : ucfirst($month)) : '?'); ?> 
            <?php echo e($year ?? '?'); ?>

          </h2>
          <p class="text-blue-700">
            Below is the checklist history for Room <?php echo e($room ?? '?'); ?> covering all assigned tasks during 
            <b>Week <?php echo e($week ?? '?'); ?> of 
            <?php echo e(isset($month) ? (is_numeric($month) ? date('F', mktime(0,0,0,$month,1)) : ucfirst($month)) : '?'); ?> 
            <?php echo e($year ?? '?'); ?></b>.
            This section reflects all submitted records for the selected week.
          </p>
        </div>
        <?php endif; ?>
        <div style="display: flex; flex-direction: row; gap: 0; align-items: flex-start;">
          <?php if(!empty($studentNames)): ?>
          <div style="min-width: 200px; max-width: 240px; height: 63vh;; background: #f8f9fa; border: 2px solid #22BBEA; border-radius: 12px; padding: 18px 20px 18px 20px; box-shadow: 0 2px 8px rgba(34,187,234,0.07); margin-right: 8px; align-self: flex-start; display: flex; flex-direction: column; justify-content: flex-start; margin-top: 3px;">
            <div style="font-size: 1.15em; font-weight: 700; color:rgb(0, 0, 0); margin-bottom: 20px;">Room Occupants for Room <?php echo e($room ?? '?'); ?></div>
            <div style="display: flex; flex-direction: column; gap: 8px;">
              <?php $__currentLoopData = $studentNames; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div style="font-size: 1.05em; color: #222; background: #eaf6fb; border-radius: 6px; padding: 6px 14px;">
                  <?php echo e($name); ?>

                </div>
              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
          </div>
          <?php endif; ?>

          <div style="flex: 1; margin-left: 0;">
            <!-- Task History Table -->
            <div id="taskHistoryTable" class="overflow-x-auto" style="max-width: 1100px;">
              <table style="width:100%; min-width: 900px; font-size: 1.05em;">
                <thead>
                  <tr>
                    <th style="width: 120px; font-size:1em; text-align:center;"></th>
                    <?php
                      // Dynamically get all unique assigned areas for the week from the matrix, in the order you want
                      $desiredOrder = [
                        'Trash Bin', 'Bathroom', 'Comfort Room', 'Table', 'Floor', 'Mirror and Sink', 'Beds', 'Storage and Organization'
                      ];
                      $allAreas = [];
                      if (isset($dayMap) && isset($studentNames) && isset($matrix)) {
                        foreach ($desiredOrder as $area) {
                          foreach ($dayMap as $day) {
                            foreach ($studentNames as $student) {
                              $cells = $matrix[$student][$day] ?? [];
                              foreach ($cells as $cell) {
                                $cellArea = trim($cell['area'] ?? '');
                                if (strcasecmp($cellArea, $area) === 0 && !in_array($area, $allAreas, true)) {
                                  $allAreas[] = $area;
                                }
                              }
                            }
                          }
                        }
                        // Add any extra areas not in desiredOrder
                        foreach ($dayMap as $day) {
                          foreach ($studentNames as $student) {
                            $cells = $matrix[$student][$day] ?? [];
                            foreach ($cells as $cell) {
                              $cellArea = trim($cell['area'] ?? '');
                              if ($cellArea && !in_array($cellArea, $allAreas, true)) {
                                $allAreas[] = $cellArea;
                              }
                            }
                          }
                        }
                      }
                    ?>
                    <?php $__currentLoopData = $allAreas; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $area): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <th style="padding:7px 4px; font-size:1em;"><?php echo e($area); ?></th>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  </tr>
                </thead>
                <tbody>
                  <?php if(empty($studentNames)): ?>
                    <tr>
                      <td colspan="<?php echo e(1 + count($allAreas)); ?>" class="text-center text-red-400 py-8" style="width: 100%;  font-size:1.1em; margin-left:0; margin-right:0;"> 
                        <div style="color:#e3342f; font-weight:600; margin-bottom:8px; width:100%;">
                          Complete first the checklist so that you can view the 1 week history.
                        </div>
                        <div style="color:#444; width:100%;">
                          If you want to view the incomplete history you can view it in the <b>Room Checklist</b>.
                        </div>
                      </td>
                    </tr>
                  <?php else: ?>
                    <?php
                      $areaDescriptions = [
                        'Floor' => 'Floor is swept and mopped, no dust or spills.',
                        'Comfort Room' => 'CR is clean, no foul smell or trash.',
                        'Bathroom' => 'Bathroom is clean, no stains or trash.',
                        'Mirror and Sink' => 'Mirror is clean and clear. Sink brushed properly.',
                        'Trash Bin' => 'No trash overflowing. Waste segregation observed.',
                        'Table' => 'Shared table is organized, no plates or cups.',
                        'Beds' => 'Rooms do not emit any smell. Beds are neatly made.',
                        'Storage and Organization' => 'Clothes are folded and stored properly.'
                      ];
                    ?>
                    
                    <tr>
                      <td style="background:#f3f8fd; color:#1d4e89; font-size:0.97em; font-weight:600; text-align:center; padding:6px 4px;">Description</td>
                      <?php $__currentLoopData = $allAreas; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $areaName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td style="background:#f3f8fd; color:#444; font-size:0.97em; text-align:left; padding:6px 4px;">
                          <?php echo e($areaDescriptions[$areaName] ?? ''); ?>

                        </td>
                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tr>
                    
                    <tr>
                      <td style="background:rgb(34, 81, 234); color:white; font-size:0.97em; font-weight:600; text-align:center; padding:7px 4px;">Day</td>
                      <?php $__currentLoopData = $allAreas; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $areaName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <td style="background:#f9fafb;"></td>
                      <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tr>
                    <?php $__currentLoopData = $dayMap; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                      <tr>
                        <td style="text-align: center; vertical-align: middle; padding:6px 4px;"><?php echo e($day); ?></td>
                        <?php $__currentLoopData = $allAreas; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $areaName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                          <?php
                            $status = null;
                            // For 'Beds' and 'Storage and Organization', get status from any student
                            if (in_array($areaName, ['Beds', 'Storage and Organization'])) {
                              foreach ($studentNames as $student) {
                                $cells = $matrix[$student][$day] ?? [];
                                foreach ($cells as $cell) {
                                  if ($cell['area'] === $areaName) {
                                    $status = $cell['status'] ?? null;
                                    break 2;
                                  }
                                }
                              }
                            } else {
                              foreach ($studentNames as $student) {
                                $cells = $matrix[$student][$day] ?? [];
                                foreach ($cells as $cell) {
                                  if ($cell['area'] === $areaName) {
                                    $status = $cell['status'] ?? null;
                                    break 2;
                                  }
                                }
                              }
                            }
                            if ($status === null || $status === '') {
                              $status = 'checked';
                            }
                          ?>
                          <td style="height: 36px; min-width: 60px; max-width: 90px; text-align: center; vertical-align: middle; padding:6px 4px;">
                            
                            <?php if($status === 'checked'): ?>
                              <div style="width:23px;height:23px;margin:auto;background:#08a821;border-radius:3px;display:flex;align-items:center;justify-content:center;">
                                <i class="ri-check-line" style="color:#fff;font-size:0.95em;"></i>
                              </div>
                            <?php elseif($status === 'wrong'): ?>
                              <div style="width:23px;height:23px;margin:auto;background:#e61515;border-radius:3px;display:flex;align-items:center;justify-content:center;">
                                <i class="ri-close-line" style="color:#fff;font-size:0.95em;"></i>
                              </div>
                            <?php endif; ?>
                          </td>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                      </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                  <?php endif; ?>
                </tbody>
              </table>
            </div>

            <!-- Remarks / Legend Section -->
            <div class="mt-6" style="max-width: 1400px; background-color:rgb(238, 215, 215); border-left: 5px solid rgb(219, 4, 4); padding: 22px 32px 18px 32px; border-radius: 12px; box-shadow: 0 1px 4px rgba(0,0,0,0.05); margin-bottom: 0px; margin-left: 0px; margin: right 0;">
              <h2 style="margin-top: 0; margin-bottom: 18px; font-size: 1.25rem; font-weight: 700; color:rgb(0, 2, 8); letter-spacing:0.5px;">
                📋 Task History – Remarks
              </h2>
              <?php if(!empty($studentNames)): ?>
                <?php
                  $overallWrong = 0; 
                  foreach ($dayMap as $day) {  
                    foreach ($allAreas as $areaName) {
                      $status = null;
                      // For 'Beds' and 'Storage and Organization', get status from any student
                      if (in_array($areaName, ['Beds', 'Storage and Organization'])) {
                        foreach ($studentNames as $student) {
                          $cells = $matrix[$student][$day] ?? [];
                          foreach ($cells as $cell) {
                            if ($cell['area'] === $areaName) {
                              $status = $cell['status'] ?? null;
                              break 2;
                            }
                          }
                        }
                      } else {
                        // For other areas, find the student assigned for this area on this day
                        foreach ($studentNames as $student) {
                          $cells = $matrix[$student][$day] ?? [];
                          foreach ($cells as $cell) {
                            if ($cell['area'] === $areaName) {
                              $status = $cell['status'] ?? null;
                              break 2;
                            }
                          }
                        }
                      }
                      if ($status === 'wrong') {
                        $overallWrong++;
                      }
                    }
                  }
                  if ($overallWrong <= 3) {
                    $overallRemark = '<span style="color:#119416;font-weight:600;"><img src="' . asset('images/check-mark.png') . '" alt="Satisfactory" style="width:24px;height:24px;vertical-align:-5px;margin-right:3px;"> Satisfactory</span>';
                  } elseif ($overallWrong <= 6) {
                    $overallRemark = '<span style="color:#e6a700;font-weight:600;"><img src="' . asset('images/warning.png') . '" alt="Needs Improvement" style="width:24px;height:24px;vertical-align:-5px;margin-right:3px;"> Needs Improvement</span>';
                  } else {
                    $overallRemark = '<span style="color:#e3342f;font-weight:600;"><img src="' . asset('images/no.png') . '" alt="Unsatisfactory" style="width:24px;height:24px;vertical-align:-5px;margin-right:3px;"> For Consequence</span>';
                  }
                ?>
                <div style="margin-bottom: 18px; display:flex; align-items:center; gap:18px;">
                  <span style="font-size:1.1em;font-weight:600;color:#222;">
                    Total Wrong Marks: <span style="color:darkred;"><?php echo e($overallWrong); ?></span>
                  </span>
                  <span style="font-size:1.1em;"><?php echo $overallRemark; ?></span>
                </div>
              <?php endif; ?>
              <div style="margin-bottom: 14px;">
                <div style="display:flex;align-items:center;gap:28px;">
                  <span style="font-size:1.1em;">
                    <img src="<?php echo e(asset('images/check-mark.png')); ?>" alt="Satisfactory" style="width:28px;height:28px;vertical-align:-6px;margin-right:4px;">
                    0–3 wrong marks <b>= Satisfactory</b>
                  </span>
                  <span style="font-size:1.1em;">
                    <img src="<?php echo e(asset('images/warning.png')); ?>" alt="Needs Improvement" style="width:28px;height:28px;vertical-align:-6px;margin-right:4px;">
                    4–6 wrong marks <b>= Needs Improvement</b> 
                  </span>
                  <span style="font-size:1.1em;">
                    <img src="<?php echo e(asset('images/no.png')); ?>" alt="For Consequence" style="width:26px;height:26px;vertical-align:-6px;margin-right:4px;">
                    7+ wrong marks <b>= For Consequence</b>
                  </span>
                </div>
              </div>
              <div style="background:rgb(245, 107, 107);border-radius:6px;padding:20px 14px;font-size:0.98em;color:black;">
                <b>Note:</b> If students receive <b>7 or more wrong marks repeatedly</b>, they may be given a consequence<b> depending on the nature of the infraction.</b> 
              </div>
            </div> 

            
            <?php if(!empty($feedbacksByDay)): ?> 
            <div class="feedbacks-wrapper" style="max-width: 1400px; background-color:rgb(183, 222, 235); border-left: 5px solid rgb(43, 174, 250); padding: 22px 32px 18px 32px; border-radius: 12px; box-shadow: 0 1px 4px rgba(0,0,0,0.05); margin-bottom: 0px; margin-left: 0px; margin-right: 0;">
                <h2 style="margin-top: 0; margin-bottom: 18px; font-size: 1.25rem; font-weight: 700; color:rgb(0, 2, 8); letter-spacing:0.5px;">
                    📝 Feedbacks for this week
                </h2>
                <?php $__currentLoopData = $dayMap; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <?php if(isset($feedbacksByDay[$day]) && count($feedbacksByDay[$day])): ?>
                        <div class="feedback-history-day" style="margin-bottom: 24px;">
                            <h4 style="margin-bottom: 8px;"><?php echo e($day); ?></h4>
                            <?php $__currentLoopData = $feedbacksByDay[$day]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feedback): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="feedback-history-card" style="border:1px solid #ddd; border-radius:8px; margin-bottom:12px; padding:12px; background: #fff;">
                                    <div>
                                        <strong>Feedback:</strong>
                                        <div style="background:whitesmoke; border-radius:6px; padding:8px 12px; margin:6px 0;">
                                            <?php echo e($feedback->feedback); ?>

                                        </div>
                                    </div>
                                    <?php if($feedback->photo_paths): ?>
                                        <div style="margin-top:8px;">
                                            <strong>Photos:</strong>
                                            <div style="display:flex; gap:10px; flex-wrap:wrap;">
                                                <?php $__currentLoopData = json_decode($feedback->photo_paths, true); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $img): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <img src="<?php echo e(asset('storage/' . $img)); ?>" style="width:100px; height:75px; object-fit:cover; border-radius:6px; border:1px solid #dbe4f3;">
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <div style="font-size:12px; color:#888; margin-top:6px;">
                                        Uploaded: 
                                        <?php echo e(\Carbon\Carbon::parse($feedback->created_at)->timezone('Asia/Manila')->format('F j, Y · h:i A')); ?>

                                        <br>
                                        Day: <b><?php echo e($feedback->day); ?></b> | Year: <b><?php echo e($feedback->year); ?></b>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php endif; ?>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <?php endif; ?>
          </div>
        </div>
        <!-- ...existing code... -->
      </div>
    </div>
  </div>
</body>
</html><?php /**PATH C:\PN_Systems\G16_CAPSTONE\resources\views/task-history.blade.php ENDPATH**/ ?>