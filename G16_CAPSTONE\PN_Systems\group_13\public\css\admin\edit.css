.edit-user-container {
    max-width: 900px;
    margin: 50px auto;
    padding: 40px;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.page-title {
    font-size: 24px;
    margin-bottom: 30px;
    font-weight: 600;
    color: #333;
}

.highlight {
    color: #ff9933;
    font-weight: 400;
}

.edit-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-row {
    display: flex;
    flex-direction: column;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

label {
    font-weight: 500;
    margin-bottom: 6px;
    color: #444;
}

input,
select {
    padding: 10px 12px;
    font-size: 15px;
    border: 1px solid #ccc;
    border-radius: 6px;
    background-color: #fafafa;
}

input:disabled {
    background-color: #eee;
    color: #777;
    cursor: not-allowed;
}

.note {
    font-size: 0.85em;
    color: #777;
    margin-top: 5px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.btn {
    padding: 10px 18px;
    border-radius: 6px;
    font-size: 15px;
    cursor: pointer;
    border: none;
    transition: 0.3s ease;
    text-decoration: none;
}

.btn-primary {
    background-color: #28a745;
    color: white;
}

.btn-primary:hover {
    background-color: #218838;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}
