body {
    background: #f8f9fa;
    font-family: Pop<PERSON>s, sans-serif;
    margin: 0;
    padding: 0;
    color: #2d3748;
    line-height: 1.5;
}

.page-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header-section {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.header-section h2 {
    font-size: 24px;
    color: #333;
    margin: 0;
}

.form-container {
    background: white;
    padding: 24px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="email"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-group input[type="text"]:focus,
.form-group input[type="number"]:focus,
.form-group input[type="email"]:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Error states */
.form-control.is-invalid,
.was-validated .form-control:invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='none' stroke='%23dc3545' viewBox='0 0 12 12'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid:focus,
.was-validated .form-control:invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.form-group input[type="text"]:focus,
.form-group input[type="number"]:focus,
.form-group .term-input:focus {
    border-color: #22bbea;
    outline: none;
    box-shadow: 0 0 0 2px rgba(34, 187, 234, 0.2);
}

/* Term input groups */
.term-input-group {
    display: flex;
    margin-bottom: 10px;
    align-items: center;
    gap: 10px;
}

.term-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    height: 40px;
}

.remove-term {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    width: 32px;
    height: 32px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    line-height: 1;
    flex-shrink: 0;
}

.remove-term:hover {
    background: #c82333;
}

#add-term {
    margin-top: 8px;
}

.checkbox-group {
    display: flex;
    gap: 16px;
}

.grade-range-selector {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-group label {
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.input-group input[type="number"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    height: 40px;
}

.radio-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.grade-info {
    margin-top: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 4px;
}

.grade-info div {
    margin-bottom: 8px;
    font-size: 14px;
}

.grade-info div:last-child {
    margin-bottom: 0;
}

.grade-info span {
    font-weight: 500;
    font-family: monospace;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.subjects-section {
    margin-top: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
}

.subjects-section h3 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 18px;
}

.subject-row {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
    align-items: center;
}

.subject-row input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.btn-remove {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #dc3545;
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}

.btn-add {
    background: #22bbea;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s;
}

.btn-add:hover {
    background: #1aa8d1;
}

/* Error messages */
.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 4px;
    display: block;
    font-weight: 400;
}

/* Alert messages */
.alert {
    position: relative;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: 0.25rem;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert ul {
    margin-bottom: 0;
    padding-left: 1.5rem;
}

.alert ul li {
    margin-bottom: 0.25rem;
}

.alert ul li:last-child {
    margin-bottom: 0;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert ul {
    margin-bottom: 0;
    padding-left: 20px;
}

/* Form groups */
.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 15px;
}

.classes-section {
    margin-top: 24px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 4px;
}

.classes-section h3 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 18px;
}

.class-row {
    margin-bottom: 12px;
}

.class-header {
    display: flex;
    gap: 12px;
    align-items: center;
}

.class-header input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.btn-select-students {
    background: #22bbea;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.students-container {
    margin-top: 12px;
}

.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
}

.btn-submit {
    background: #22bbea;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer !important;
    font-size: 14px;
    width: auto;
    min-width: 120px;
    display: inline-block;
    text-align: center;
    text-decoration: none;
    opacity: 1 !important;
    pointer-events: auto !important;
}

.btn-submit:hover {
    background: #149dc7;
    text-decoration: none;
    opacity: 1 !important;
}

.btn-submit:active {
    background: #22bbea;
    text-decoration: none;
    opacity: 1 !important;
}

/* .btn-cancel {
    background: #ff9933;
    color: white;
    padding: 10px 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    text-decoration: none;
    text-align: center;
} */

.btn-cancel:hover {
    background:rgb(232, 117, 3);
    color: #000;
    text-decoration: none;
    text-align: center;
}

.error-message {
    color: #dc3545;
    font-size: 12px;
    margin-top: 4px;
}

/* Student Conflict Alert Styles */
.student-conflicts-alert {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-left: 4px solid #dc3545;
    border-radius: 8px;
    padding: 0;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.1);
}

.alert-header {
    background: #dc3545;
    color: white;
    padding: 12px 16px;
    border-radius: 4px 4px 0 0;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.alert-header i {
    font-size: 16px;
}

.alert-body {
    padding: 16px;
}

.alert-body p {
    margin: 0 0 12px 0;
    color: #721c24;
    font-weight: 500;
}

.conflict-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.conflict-list li {
    background: white;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 10px 12px;
    margin-bottom: 8px;
    color: #721c24;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.conflict-list li:last-child {
    margin-bottom: 0;
}

.conflict-list li i {
    color: #dc3545;
    font-size: 14px;
    flex-shrink: 0;
}

.alert-footer {
    background: #f1f3f4;
    padding: 10px 16px;
    border-radius: 0 0 4px 4px;
    border-top: 1px solid #f5c6cb;
}

.alert-footer small {
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    line-height: 1.4;
}

.alert-footer i {
    color: #17a2b8;
    font-size: 12px;
    flex-shrink: 0;
}

.alert {
    padding: 12px 16px;
    margin-bottom: 16px;
    border-radius: 4px;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

@media (max-width: 768px) {
    .page-container {
        padding: 16px;
    }
    
    .subject-row {
        flex-direction: column;
        gap: 8px;
    }
    
    .checkbox-group {
        flex-direction: column;
        gap: 8px;
    }
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
}

.modal-header {
    padding: 16px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close-modal {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 16px;
    overflow-y: auto;
}

.batch-filter {
    margin-bottom: 16px;
}

.batch-filter select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.students-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.student-item {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.student-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.modal-footer {
    padding: 16px;
    border-top: 1px solid #ddd;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.btn-save {
    background:#22bbea;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
}

.btn-cancel {
    background: #ff9933;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
}

.selected-students {
    padding: 8px;
    background: #e9ecef;
    border-radius: 4px;
    font-size: 14px;
    color: #666;
}

.styled-batch-select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    background: #fff;
    color: #333;
    margin-left: 8px;
    min-width: 120px;
}

.styled-batch-select:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

#studentModal {
    display: none;
}
