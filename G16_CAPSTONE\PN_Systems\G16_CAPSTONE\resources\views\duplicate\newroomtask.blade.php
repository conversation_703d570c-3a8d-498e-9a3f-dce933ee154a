<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Room Checklist - Tasking Hub System</title>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet"/>
  <link rel="stylesheet" href="{{ asset('css/roomtask.css') }}" />
  <link rel="icon" href="{{ asset('favicon.ico') }}" type="image/x-icon">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body>
  <header>
    <div class="logo">
      <img src="{{ asset('images/pnlogo-header.png') }}" alt="PN Logo">
    </div>
  </header>
  <div class="container-fluid">
  <div class="row">
    <nav class="col-md-2 d-none d-md-block sidebar bg-light">
      <ul class="nav flex-column">
       <li class="nav-item"><a href="{{ route('dashboard') }}" class="nav-link sidebar-link">
           <img src="{{ asset('images/dashboard.png')}}" class="sidebar-icon">Dashboard </a>
        </li>
        <li class="nav-item"><a href="{{ route('roomtask') }}" class="nav-link">
           <img src="{{ asset('images/checklist.png')}}" class="sidebar-icon">Room Tasks</a>
          </li>
        <li class="nav-item"><a href="{{ route('generalTask') }}" class="nav-link">
          <img src="{{ asset('images/assign.png')}}" class="sidebar-icon">General Tasks</a>
        </li>
        <li class="nav-item"><a href="{{ route('task.history') }}" class="nav-link">
           <img src="{{ asset('images/history.png')}}" class="sidebar-icon">Room Task History</a>
        </li>
      <li class="nav-item"><a href="{{ route('reports.index') }}" class="nav-link">
          <img src="{{ asset('images/complaint.png')}}" class="sidebar-icon">Reports</a></li>
        <li class="nav-item"><a href="#" class="nav-link" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
              <img src="{{ asset('images/log-out.png')}}" class="sidebar-icon">Log Out
            </a>
      </ul>
    </nav>

    <div class="content">
      <h1 class="page-title">ROOM TASK ASSIGNMENTS and CHECKLIST</h1>

      @if($selectedRoom)
        <div class="room-details">
          <h2>Room: {{ $selectedRoom }}</h2>
          <div class="time-selection">
            <select id="monthSelect" class="time-select">
              <option value="">Select Month</option>
              @for($i = 1; $i <= 12; $i++)
                <option value="{{ $i }}">{{ date('F', mktime(0, 0, 0, $i, 1)) }}</option>
              @endfor
            </select>
            <select id="yearSelect" class="time-select">
              <option value="">Select Year</option>
              @for($i = 2025; $i <= 2040; $i++)
                <option value="{{ $i }}">{{ $i }}</option>
              @endfor
            </select>
            <select id="weekSelect" class="time-select">
              <option value="">Select Week</option>
            </select>
          </div>
        </div>

        <div class="day-nav">
          <button id="prevDayButton">&laquo;</button>
          <span id="currentDay">{{ $currentDay }}</span>
          <button id="nextDayButton">&raquo;</button>
        </div>

        <table class="table table-bordered align-middle">
  <thead class="table-black">
    <tr>
      <th>Student Name</th>
      <th>Assigned Area</th>
      <th>Description</th>
      <th>Status</th>
    </tr>
  </thead>
  <tbody id="taskTableBody">
    @php
      $dayData = $tasksByDay[$currentDay] ?? null;
      $isCompleted = $dayData['isCompleted'] ?? false;
      $roomTasks = $dayData && isset($dayData['tasks'][$selectedRoom]) ? $dayData['tasks'][$selectedRoom] : [];
    @endphp
    @if($roomTasks && count($roomTasks))
      @foreach($roomTasks as $task)
        <tr data-task-id="{{ $task['id'] ?? '' }}">
          <td>{{ $task['name'] ?? 'N/A' }}</td>
          <td>{{ $task['area'] ?? 'N/A' }}</td>
          <td>{{ $task['desc'] ?? 'N/A' }}</td>
          <td style="text-align:center;">
            @if($isCompleted)
              @if(($task['display_status'] ?? '') === 'checked')
                <button class="status-btn check-btn active" style="opacity: 1; background-color: #08a821; color: white; border-color: #08a821;" disabled>
                  <i class="fas fa-check"></i>
                </button>
              @elseif(($task['display_status'] ?? '') === 'wrong')
                <button class="status-btn wrong-btn active" style="opacity: 1; background-color: #e61515; color: white; border-color: #e61515;" disabled>
                  <i class="fas fa-times"></i>
                </button>
              @else
                <!-- Show empty cell if no status -->
                <span>-</span>
              @endif
            @else
              <div class="status-buttons">
                <button class="status-btn check-btn" onclick="updateStatus(this, '{{ $task['id'] ?? '' }}', 'checked')">
                  <i class="fas fa-check"></i>
                </button>
                <button class="status-btn wrong-btn" onclick="updateStatus(this, '{{ $task['id'] ?? '' }}', 'wrong')">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            @endif
          </td>
        </tr>
      @endforeach
    @else
      <tr><td colspan="4" style="text-align:center;">No tasks assigned for this room on {{ $currentDay }}.</td></tr>
    @endif
  </tbody>
</table>
        <div class="task-buttons">
        <button id="addTaskButton" onclick="openForm('add')">Add Task</button>
          <button id="editTaskButton">Edit Task</button>
          <button id="markAllCompleted" class="mark-all-btn" onclick="markAllCompleted()">Mark Day as Completed</button>
        </div>

        <!-- Task Form -->
        <div id="taskFormContainer" class="hidden">
          <h3 id="formTitle">Add Task</h3>
          <form id="taskForm" onsubmit="saveTask(event)">
            <input type="hidden" id="taskId" name="taskId" value="" />
            <div id="taskNameContainer">
              <input type="text" id="taskName" name="taskName" placeholder="Enter name" required />
            </div>

            <label for="taskArea">Area:</label>
            <input type="text" id="taskArea" name="taskArea" placeholder="Enter area" required />

            <label for="taskDesc">Description:</label>
            <textarea id="taskDesc" name="taskDesc" placeholder="Enter description" rows="4" required></textarea>

            <div class="form-buttons">
              <button type="submit">Save Task</button>
              <button type="cancel" onclick="closeForm()">Cancel</button>
            </div>
          </form>
        </div>

        <div class="feedback-section">
          <h3>Task Feedback</h3>
          <form method="POST" action="{{ route('submit.feedback') }}" enctype="multipart/form-data" id="feedbackForm">
            @csrf
            <input type="hidden" name="room_number" value="{{ $selectedRoom }}">
            <input type="hidden" name="day" id="feedbackDay" value="{{ $currentDay }}">
            <input type="hidden" name="week" id="feedbackWeek" value="">
            <input type="hidden" name="month" id="feedbackMonth" value="">
            <input type="hidden" name="year" id="feedbackYear" value="">
            <textarea name="feedback" rows="4" placeholder="Write your feedback here..." required></textarea><br />
            <div class="custom-file-input">
              <label for="feedback_file">Choose Files (Maximum 3 Photos)</label>
              <input type="file" id="feedback_file" name="feedback_files[]" multiple accept="image/*" onchange="validateFiles(this)" />
              <div class="file-info">
              <span class="file-name" id="fileName">No files chosen</span>
                <div id="fileError" class="error-message" style="color: red; display: none;">Maximum 3 photos allowed.</div>
              </div>
            </div>
            <button type="submit" id="submitFeedback">Submit Feedback</button>
          </form>
          <!-- Feedback display container -->
          <div id="submittedFeedbacks">
            @foreach($feedbacks as $feedback)
              <div class="feedback-card" data-photo-paths="{{ json_encode($feedback->photos ?? []) }}">
                <div style="background:whitesmoke; border-radius:8px; padding:12px 16px; margin-bottom:8px;">
                  {{ $feedback->feedback }}
                </div>
                <div>
                  @foreach($feedback->photos as $photo)
                    <img src="/storage/{{ $photo }}" style="width:80px;height:60px;object-fit:cover;border-radius:6px;border:1px solid #dbe4f3;">
                  @endforeach
                </div>
                <div style="font-size:12px;color:#888;margin-top:4px;">
                  Submitted: {{ $feedback->created_at->format('Y-m-d H:i') }}
                </div>
                @if(empty($isStudent) || !$isStudent)
                  <div style="margin-top:8px;">
                    <button class="edit-feedback-btn btn btn-warning btn-sm" data-id="{{ $feedback->id }}">Edit</button>
                    <button class="delete-feedback-btn btn btn-danger btn-sm" data-id="{{ $feedback->id }}">Delete</button>
                  </div>
                @endif
              </div>
            @endforeach
          </div>
        </div>
      @else
        <div class="room-selection">
          <h2>Please select a room from the dashboard to view its tasks.</h2>
          <a href="{{ route('dashboard') }}" class="btn-back">Back to Dashboard</a>
        </div>
      @endif
    </div>
  </div>

  <!-- Pass PHP variables to JavaScript -->
  <script>
   
    // Use week order: Sunday to Saturday (from PHP)
    const days = @json($daysOfWeek);
    let currentDayIndex = {{ array_search($currentDay, $daysOfWeek) }};
    const currentDayElement = document.getElementById("currentDay");
    const taskTableBody = document.getElementById("taskTableBody");
    const markAllCompletedBtn = document.getElementById("markAllCompleted");
    let weekDayCompletionStatus = @json($weekDayCompletionStatus ?? []);
    let currentWeek = document.getElementById('weekSelect').value;
    let currentMonth = document.getElementById('monthSelect').value;
    let currentYear = document.getElementById('yearSelect').value;
    let editTaskIndex = null;
    window.selectedRoom = @json($selectedRoom);

    function getWeekDates(year, month, weekNumber) {
        // Get the first day of the current month
        const firstDay = new Date(year, month - 1, 1);
        const firstDayOfWeek = firstDay.getDay(); // 0 = Sunday, 1 = Monday, etc.

        // Calculate the start date of the week
        let startDate = new Date(year, month - 1, 1);
        
        // For Week 1, we need to find the previous Sunday
        if (weekNumber === 1) {
            // If the first day of month is not Sunday, go back to previous Sunday
            if (firstDayOfWeek !== 0) {
                startDate.setDate(startDate.getDate() - firstDayOfWeek);
            }
        } else {
            // For subsequent weeks, start from the day after the previous week's Saturday
            const previousWeekStart = new Date(year, month - 1, 1);
            previousWeekStart.setDate(previousWeekStart.getDate() - firstDayOfWeek + ((weekNumber - 1) * 7));
            startDate = previousWeekStart;
        }

        // Calculate end date (Saturday)
        const endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + 6);

        return { startDate, endDate };
    }

    function updateWeeks() {
        const monthSelect = document.getElementById('monthSelect');
        const yearSelect = document.getElementById('yearSelect');
        const weekSelect = document.getElementById('weekSelect');
        
        const month = parseInt(monthSelect.value);
        const year = parseInt(yearSelect.value);
        
        if (month && year) {
            const firstDay = new Date(year, month - 1, 1);
            const lastDay = new Date(year, month, 0);
            const firstDayOfWeek = firstDay.getDay();
            const daysInMonth = lastDay.getDate();
            
            // Calculate total weeks that include days from this month
            const totalDays = daysInMonth + firstDayOfWeek;
            const weeksInMonth = Math.ceil(totalDays / 7);
            
            // Clear and add default option
            weekSelect.innerHTML = '<option value="">Select Week</option>';
            
            // Show weeks based on actual calendar weeks
            for (let i = 1; i <= weeksInMonth; i++) {
                const { startDate, endDate } = getWeekDates(year, month, i);
                
                // Format dates
                const startDateStr = startDate.toLocaleDateString('en-US', { 
                    month: 'short', 
                    day: 'numeric' 
                });
                const endDateStr = endDate.toLocaleDateString('en-US', { 
                    month: 'short', 
                    day: 'numeric' 
                });
                
                const option = document.createElement('option');
                option.value = i;
                option.textContent = `Week ${i} (${startDateStr} - ${endDateStr})`;
                weekSelect.appendChild(option);
            }
        } else {
            weekSelect.innerHTML = '<option value="">Select Week</option>';
        }
    }

    // Add this helper function before DOMContentLoaded
function checkWeekCompletionAndAlert(room, week, month, year) {
    // This function checks if all 7 days are completed for the selected room/week/month/year
    // and shows a popup if not completed.
    fetch('/api/check-week-completion', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            room: room,
            week: week,
            month: month,
            year: year
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data && data.completed === false) {
            alert("Checklist for this room is not yet completed for the week.");
        }
    });
}

    // Add event listeners when the DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
      // Add click handlers for the task buttons
      document.getElementById('addTaskButton').addEventListener('click', function() {
        openForm('add');
      });

      document.getElementById('editTaskButton').addEventListener('click', function() {
        openForm('edit');
      });



      document.getElementById('monthSelect').addEventListener('change', function() {
        updateWeeks();
        loadTasksForCurrentDay();
        // Check completion for the new selection
        const room = window.selectedRoom;
        const week = document.getElementById('weekSelect').value;
        const month = document.getElementById('monthSelect').value;
        const year = document.getElementById('yearSelect').value;
        if (room && week && month && year) {
            checkWeekCompletionAndAlert(room, week, month, year);
        }
      });
      document.getElementById('yearSelect').addEventListener('change', function() {
        updateWeeks();
        loadTasksForCurrentDay();
        const room = window.selectedRoom;
        const week = document.getElementById('weekSelect').value;
        const month = document.getElementById('monthSelect').value;
        const year = document.getElementById('yearSelect').value;
        if (room && week && month && year) {
            checkWeekCompletionAndAlert(room, week, month, year);
        }
      });
      document.getElementById('weekSelect').addEventListener('change', function() {
        loadTasksForCurrentDay();
        const room = window.selectedRoom;
        const week = document.getElementById('weekSelect').value;
        const month = document.getElementById('monthSelect').value;
        const year = document.getElementById('yearSelect').value;
        if (room && week && month && year) {
            checkWeekCompletionAndAlert(room, week, month, year);
        }
      });

      // Set default values and trigger initial update
      const monthSelect = document.getElementById('monthSelect');
      const yearSelect = document.getElementById('yearSelect');
      const weekSelect = document.getElementById('weekSelect');

      monthSelect.value = new Date().getMonth() + 1;
      yearSelect.value = new Date().getFullYear();
      updateWeeks();

      monthSelect.addEventListener('change', function() {
    updateWeeks();
    loadTasksForCurrentDay();
    });
    yearSelect.addEventListener('change', function() {
    updateWeeks();
    loadTasksForCurrentDay();
      });
    weekSelect.addEventListener('change', function() {
    loadTasksForCurrentDay();
      });
    });

    // Unified function to change day by offset and reload data, always using Sunday-Saturday order
    function changeDayByOffset(offset) {
      // Always use the days array for navigation order (Sunday-Saturday)
      currentDayIndex = (currentDayIndex + offset + days.length) % days.length;
      currentDayElement.textContent = days[currentDayIndex];

      // Debug: Log the navigation to verify correct order
      console.log('Day navigation:', {
        offset: offset,
        newIndex: currentDayIndex,
        newDay: days[currentDayIndex],
        daysArray: days
      });

      loadTasksForCurrentDay();
      onFeedbackSelectionChange();
    }

    // Attach navigation listeners only once, always using days[] order
    document.getElementById('prevDayButton').onclick = function() {
      changeDayByOffset(-1);
    };
    document.getElementById('nextDayButton').onclick = function() {
      changeDayByOffset(1);
    };

    // If user changes the day manually (e.g., by code), keep currentDayIndex in sync
    function setDayByName(dayName) {
      const idx = days.indexOf(dayName);
      if (idx !== -1) {
        currentDayIndex = idx;
        currentDayElement.textContent = days[currentDayIndex];
        loadTasksForCurrentDay();
        onFeedbackSelectionChange();
      }
    }

    function openForm(mode) {
      currentMode = mode;
      const formContainer = document.getElementById('taskFormContainer');
      const title = document.getElementById('formTitle');
      const taskNameContainer = document.getElementById('taskNameContainer');
      const form = document.getElementById('taskForm');

      if (!formContainer || !title || !taskNameContainer || !form) {
        console.error('Required form elements not found');
        return;
      }

      if (mode === 'add') {
        title.textContent = 'Add Task';
        taskNameContainer.innerHTML = `
          <label for="taskName">Name:</label>
          <input type="text" id="taskName" name="taskName" placeholder="Enter name" required />
          <input type="hidden" id="taskId" name="taskId" value="" />
        `;
        document.getElementById('taskArea').value = '';
        document.getElementById('taskDesc').value = '';
        form.reset();
      } else if (mode === 'edit') {
        title.textContent = 'Edit Task';
        const currentDay = document.getElementById('currentDay').textContent;
        const room = '{{ $selectedRoom }}';

        // Get tasks from the table if possible, fallback to tasksByDay
        let tasks = [];
        const rows = document.querySelectorAll('#taskTableBody tr[data-task-id]');
        if (rows.length > 0) {
          rows.forEach((row, idx) => {
            if (row.querySelector('td') && row.querySelectorAll('td').length >= 3) {
              tasks.push({
                name: row.children[0].textContent.trim(),
                area: row.children[1].textContent.trim(),
                desc: row.children[2].textContent.trim(),
                id: row.getAttribute('data-task-id') || idx
              });
            }
          });
        }
        if (tasks.length === 0 && tasksByDay[currentDay] && tasksByDay[currentDay][room]) {
          tasks = tasksByDay[currentDay][room];
        }

        taskNameContainer.innerHTML = `
          <label for="taskNameDropdown">Select Task to Edit:</label>
          <select id="taskNameDropdown" name="taskNameDropdown" required>
            ${tasks.map((task, index) => `
              <option value="${index}" data-task-id="${task.id}">${task.name} - ${task.area}</option>
            `).join('')}
          </select>
          <label for="taskName">Change Name To:</label>
          <input type="text" id="taskName" name="taskName" placeholder="Enter new name" required />
          <input type="hidden" id="taskId" name="taskId" value="" />
        `;

        // Remove previous status display if present
        const prevStatus = document.getElementById('editStatusDisplay');
        if (prevStatus) prevStatus.remove();

        // Load the first task by default
        loadSelectedTask(0);

        // Add change event to dropdown
        const nameDropdown = document.getElementById('taskNameDropdown');
        if (nameDropdown) {
          nameDropdown.addEventListener('change', function() {
            loadSelectedTask(this.value);
          });
        }
      }

      formContainer.classList.remove('hidden');
    }

    function loadSelectedTask(index) {
  const currentDay = document.getElementById('currentDay').textContent;
  const room = '{{ $selectedRoom }}';

  // Try to get tasks from the table if possible, fallback to tasksByDay
  let tasks = [];
  const rows = document.querySelectorAll('#taskTableBody tr[data-task-id]');
  if (rows.length > 0) {
    rows.forEach((row, idx) => {
      if (row.querySelector('td') && row.querySelectorAll('td').length >= 3) {
        tasks.push({
          name: row.children[0].textContent.trim(),
          area: row.children[1].textContent.trim(),
          desc: row.children[2].textContent.trim(),
          id: row.getAttribute('data-task-id') || idx
        });
      }
    });
  }
  if (tasks.length === 0 && tasksByDay[currentDay] && tasksByDay[currentDay][room]) {
    tasks = tasksByDay[currentDay][room];
  }

  const task = tasks[index];
  if (task) {
    document.getElementById('taskName').value = task.name;
    document.getElementById('taskArea').value = task.area;
    document.getElementById('taskDesc').value = task.desc;
    document.getElementById('taskId').value = task.id || '';
    editTaskIndex = index;

    // Remove status display if present (for edit form, status is not shown)
    const prevStatus = document.getElementById('editStatusDisplay');
    if (prevStatus) prevStatus.remove();
  }
    }

    function closeForm() {
      const formContainer = document.getElementById('taskFormContainer');
      const form = document.getElementById('taskForm');
      if (formContainer && form) {
        formContainer.classList.add('hidden');
        form.reset();
      }
    }

    function saveTask(event) {
      event.preventDefault();

      const currentDay = document.getElementById('currentDay').textContent;
      const room = '{{ $selectedRoom }}';
      const name = document.getElementById('taskName').value;
      const area = document.getElementById('taskArea').value;
      const desc = document.getElementById('taskDesc').value;
      const taskId = document.getElementById('taskId') ? document.getElementById('taskId').value : null;

      // Validate inputs
      if (!name || !area || !desc) {
        alert('Please fill in all fields');
        return;
      }

      fetch('/save-task', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
          name,
          area,
          desc,
          day: currentDay,
          room,
          mode: currentMode,
          taskIndex: editTaskIndex,
          taskId: currentMode === 'edit' ? taskId : undefined
        })
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then (data => {
        if (data.success) {
          // Only update tasksByDay for add mode (optional, but not needed for edit)
          if (currentMode === 'add') {
            if (!tasksByDay[currentDay]) {
              tasksByDay[currentDay] = {};
            }
            if (!tasksByDay[currentDay][room]) {
              tasksByDay[currentDay][room] = [];
            }
            tasksByDay[currentDay][room].push({
              name,
              area,
              desc,
              status: 'not yet'
            });
          }
          // For edit, do not update tasksByDay directly (let loadTasksForCurrentDay handle it)
          loadTasksForCurrentDay();
          closeForm();
          alert('Task saved successfully!');
        } else {
          throw new Error(data.message || 'Failed to save task');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while saving the task: ' + error.message);
      })
    }

    function updateStatus(button, taskId, status) {
        const currentDay = document.getElementById('currentDay').textContent;
        const currentWeek = document.getElementById('weekSelect').value;
        const currentMonth = document.getElementById('monthSelect').value;
        const currentYear = document.getElementById('yearSelect').value;
        const room = window.selectedRoom;

        // Create a unique key for this specific day
        const dayKey = `${currentYear}-${currentMonth}-${currentWeek}-${currentDay}`;

        // Check if the day is already completed
        if (weekDayCompletionStatus[dayKey]) {
            return false;
        }

        // Toggle active class for the clicked button
        const isActive = button.classList.contains('active');
        
        // Get the parent row and both buttons
        const row = button.closest('tr');
        const checkBtn = row.querySelector('.check-btn');
        const wrongBtn = row.querySelector('.wrong-btn');
        
        // Remove active class from both buttons
        checkBtn.classList.remove('active');
        wrongBtn.classList.remove('active');
        
        // If the button wasn't active before, make it active now
        // If it was active, we're toggling it off
        let newStatus = 'not yet';
        if (!isActive) {
            button.classList.add('active');
            newStatus = status;
        }
        
        console.log(`Updating task ${taskId} status to: ${newStatus}`);

        // Send the status update to the server
        fetch('/update-task-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                taskId: taskId,
                status: newStatus,
                day: currentDay,
                week: currentWeek,
                month: currentMonth,
                year: currentYear,
                dayKey: dayKey
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Task status updated successfully:', data);
                
                // Check if all tasks have a status and update the "Mark All Completed" button
                const allTasks = document.querySelectorAll('#taskTableBody tr[data-task-id]');
                const allTasksHaveStatus = Array.from(allTasks).every(row => {
                    const checkActive = row.querySelector('.check-btn.active');
                    const wrongActive = row.querySelector('.wrong-btn.active');
                    return checkActive || wrongActive;
                });
                
                const markAllBtn = document.getElementById('markAllCompleted');
                markAllBtn.disabled = !allTasksHaveStatus;
                markAllBtn.style.opacity = allTasksHaveStatus ? '1' : '0.5';
                markAllBtn.style.cursor = allTasksHaveStatus ? 'pointer' : 'not-allowed';
            } else {
                console.error('Failed to update task status:', data.message);
                // Revert UI changes if the server update failed
                loadTasksForCurrentDay();
            }
        })
        .catch(error => {
            console.error('Error updating task status:', error);
            // Revert UI changes if there was an error
            loadTasksForCurrentDay();
        });
    }

    function editTask(taskId) {
        // Here you can implement the edit functionality
        // For example, show a modal with the task details
        alert('Edit functionality will be implemented here');
    }
    
    function loadTasksForCurrentDay() {
        const currentDay = document.getElementById('currentDay').textContent;
        const room = window.selectedRoom;
        currentWeek = document.getElementById('weekSelect').value;
        currentMonth = document.getElementById('monthSelect').value;
        currentYear = document.getElementById('yearSelect').value;
        
        if (!currentWeek || !currentMonth || !currentYear) {
            taskTableBody.innerHTML = `<tr><td colspan="4" style="text-align:center;">Please select week, month, and year.</td></tr>`;
            return;
        }
        
        // Create a unique key for this specific day
        const dayKey = `${currentYear}-${currentMonth}-${currentWeek}-${currentDay}`;
        
        // Show loading indicator
        taskTableBody.innerHTML = `<tr><td colspan="4" style="text-align:center;">Loading tasks...</td></tr>`;
        
        // Fetch the current task statuses from the server
        fetch('/get-task-statuses', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                day: currentDay,
                room: room,
                week: currentWeek,
                month: currentMonth,
                year: currentYear
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const tasks = data.tasks;
                const isCompleted = data.completed;
                
                // Update the completion status
                weekDayCompletionStatus[dayKey] = isCompleted;
                
                if (tasks.length > 0) {
                    renderTasks(tasks, isCompleted, dayKey);
                } else {
                    taskTableBody.innerHTML = `<tr><td colspan="4" style="text-align:center;">No tasks assigned for this room on ${currentDay}.</td></tr>`;
                }
            } else {
                throw new Error(data.message || 'Failed to load tasks');
            }
        })
        .catch(error => {
            console.error('Error loading tasks:', error);
            taskTableBody.innerHTML = `<tr><td colspan="4" style="text-align:center;">Error loading tasks: ${error.message}</td></tr>`;
        });
    }

    // Helper function to render tasks with their statuses
    function renderTasks(tasks, isDayCompleted, dayKey) {
        taskTableBody.innerHTML = tasks.map(task => {
            const taskId = (task && typeof task === 'object' && 'id' in task) ? (task.id ?? '') : '';
            const taskName = (task && typeof task === 'object' && 'name' in task) ? (task.name ?? 'N/A') : 'N/A';
            const taskArea = (task && typeof task === 'object' && 'area' in task) ? (task.area ?? 'N/A') : 'N/A';
            const taskDesc = (task && typeof task === 'object' && 'desc' in task) ? (task.desc ?? 'N/A') : 'N/A';
            const taskStatus = (task && typeof task === 'object' && 'status' in task) ? (task.status ?? '') : '';

            const isChecked = taskStatus === 'checked';
            const isWrong = taskStatus === 'wrong';

            let statusCell = '';
            if (isDayCompleted) {
                if (isChecked) {
                    statusCell = `<button class="status-btn check-btn active" style="opacity: 1; background-color: #08a821; color: white; border-color: #08a821;" disabled>
                                <i class="fas fa-check"></i>
                              </button>`;
                } else if (isWrong) {
                    statusCell = `<button class="status-btn wrong-btn active" style="opacity: 1; background-color: #e61515; color: white; border-color: #e61515;" disabled>
                                <i class="fas fa-times"></i>
                              </button>`;
                } else {
                    statusCell = `<span>-</span>`;
                }
            } else {
                statusCell = `<div class="status-buttons">
                            <button class="status-btn check-btn" onclick="updateStatus(this, '${taskId}', 'checked')">
                                <i class="fas fa-check"></i>
                            </button>
                            <button class="status-btn wrong-btn" onclick="updateStatus(this, '${taskId}', 'wrong')">
                                <i class="fas fa-times"></i>
                            </button>
                          </div>`;
            }

            return `
                <tr data-task-id="${taskId}">
                    <td>${taskName}</td>
                    <td>${taskArea}</td>
                    <td>${taskDesc}</td>
                    <td style="text-align:center;">${statusCell}</td>
                </tr>
            `;
        }).join('');
        
        // Update the "Mark All Completed" button
        updateMarkAllCompletedButton(isDayCompleted, tasks);
    }

    // Helper function to update the "Mark All Completed" button
    function updateMarkAllCompletedButton(isDayCompleted, tasks) {
        const markAllBtn = document.getElementById('markAllCompleted');
        if (isDayCompleted) {
            markAllBtn.disabled = true;
            markAllBtn.style.opacity = '0.5';
            markAllBtn.style.cursor = 'not-allowed';
        } else {
            const allTasksHaveStatus = tasks.every(task => {
                const status = task.status || 'not yet';
                return status === 'checked' || status === 'wrong';
            });
            
            markAllBtn.disabled = !allTasksHaveStatus;
            markAllBtn.style.opacity = allTasksHaveStatus ? '1' : '0.5';
            markAllBtn.style.cursor = allTasksHaveStatus ? 'pointer' : 'not-allowed';
        }
    }

    function markAllCompleted() {
        const currentDay = document.getElementById('currentDay').textContent;
        const room = window.selectedRoom;
        const currentWeek = document.getElementById('weekSelect').value;
        const currentMonth = document.getElementById('monthSelect').value;
        const currentYear = document.getElementById('yearSelect').value;
        
        // Create a unique key for this specific day
        const dayKey = `${currentYear}-${currentMonth}-${currentWeek}-${currentDay}`;
        
        // Check if all tasks have a status
        const taskRows = document.querySelectorAll('#taskTableBody tr[data-task-id]');
        const allTasksHaveStatus = Array.from(taskRows).every(row => {
            const checkActive = row.querySelector('.check-btn.active');
            const wrongActive = row.querySelector('.wrong-btn.active');
            return checkActive || wrongActive;
        });
        
        if (!allTasksHaveStatus) {
            alert('Please set a status (checked or wrong) for all tasks before marking the day as completed.');
            return;
        }
        
        // Show confirmation dialog
        const confirmed = confirm('Are you sure you want to mark all tasks for this day as completed?');
        if (!confirmed) {
            return;
        }
        
        // Get all tasks for the current day with their current status
        const tasks = Array.from(taskRows).map(row => {
            const taskId = row.getAttribute('data-task-id');
            const checkBtn = row.querySelector('.check-btn');
            const wrongBtn = row.querySelector('.wrong-btn');
            
            let status = 'not yet';
            if (checkBtn && checkBtn.classList.contains('active')) {
                status = 'checked';
            } else if (wrongBtn && wrongBtn.classList.contains('active')) {
                status = 'wrong';
            }
            
            console.log(`Task ${taskId} will be marked as: ${status}`);
            
            return {
                id: taskId,
                status: status
            };
        });
        
        // Send the request to mark the day as completed
        fetch('/mark-day-complete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
            },
            body: JSON.stringify({
                day: currentDay,
                room: room,
                week: currentWeek,
                month: currentMonth,
                year: currentYear,
                dayKey: dayKey,
                tasks: tasks
            })
        })
         .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Day marked as completed successfully:', data);
                
                // Update the UI to reflect the completed status
                weekDayCompletionStatus[dayKey] = true;
                
                // Reload the tasks to show the updated status
                loadTasksForCurrentDay();
                
                alert('Day marked as completed successfully!');
              } else {
                throw new Error(data.message || 'Failed to mark day as completed');
              }
            })
            .catch(error => {
              console.error('Error marking day as completed:', error);
              alert('Error: ' + error.message);
            });
    }

    function validateFiles(input) {
  const maxFiles = 3;
  const files = input.files;
  const errorDiv = document.getElementById('fileError');
  const submitButton = document.getElementById('submitFeedback');
  const fileNameSpan = document.getElementById('fileName');

  // Display file names and thumbnails
  if (files.length > 0) {
    const fileNames = Array.from(files).map(file => file.name);
    // Show file names
    fileNameSpan.textContent = 'Selected files: ' + fileNames.join(', ');

    // Remove any previous thumbnails
    let thumbContainer = document.getElementById('selectedFileThumbs');
    if (thumbContainer) thumbContainer.remove();

    // Create a container for thumbnails
    thumbContainer = document.createElement('div');
    thumbContainer.id = 'selectedFileThumbs';
    thumbContainer.style.display = 'flex';
    thumbContainer.style.gap = '8px';
    thumbContainer.style.marginTop = '6px';

    Array.from(files).forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = function(e) {
          const img = document.createElement('img');
          img.src = e.target.result;
          img.style.width = '48px';
          img.style.height = '36px';
          img.style.objectFit = 'cover';
          img.style.borderRadius = '4px';
          img.style.border = '1px solid #dbe4f3';
          thumbContainer.appendChild(img);
        };
        reader.readAsDataURL(file);
      }
    });

    // Insert thumbnails after fileNameSpan
    fileNameSpan.parentNode.appendChild(thumbContainer);
  } else {
    fileNameSpan.textContent = 'No files chosen';
    const thumbContainer = document.getElementById('selectedFileThumbs');
    if (thumbContainer) thumbContainer.remove();
  }

  // Validate maximum files
  if (files.length > maxFiles) {
    errorDiv.textContent = 'Maximum 3 photos allowed.';
    errorDiv.style.display = 'block';
    submitButton.disabled = true;
    return false;
  }

  // Validate file types
  const validTypes = ['image/jpeg', 'image/png', 'image/jpg'];
  const invalidFiles = Array.from(files).filter(file => !validTypes.includes(file.type));

  if (invalidFiles.length > 0) {
    errorDiv.textContent = 'Please select only image files (JPG, PNG)';
    errorDiv.style.display = 'block';
    submitButton.disabled = true;
    return false;
  }

  errorDiv.style.display = 'none';
  submitButton.disabled = false;
  return true;
}

    // Add form submission validation
    document.getElementById('feedbackForm').addEventListener('submit', function(e) {
      // Always sync hidden fields first!
      syncFeedbackFormFields();

      const fileInput = document.getElementById('feedback_file');
      if (!validateFiles(fileInput)) {
        e.preventDefault();
        return;
      }
      // Validate required hidden fields
      const day = document.getElementById('feedbackDay').value;
      const week = document.getElementById('feedbackWeek').value;
      const month = document.getElementById('feedbackMonth').value;
      const year = document.getElementById('feedbackYear').value;
      if (!day || !week || !month || !year) {
        e.preventDefault();
        alert('Please select week, month, and year before submitting feedback.');
        return;
      }
      e.preventDefault(); // Prevent default form submission

      const form = this;
      const formData = new FormData(form);

      // Disable submit button to prevent double submit
      const submitBtn = document.getElementById('submitFeedback');
      submitBtn.disabled = true;
      submitBtn.textContent = 'Submitting...';

      fetch(form.action, {
        method: 'POST',
        headers: {
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
          'Accept': 'application/json'
        },
        body: formData
      })
      .then(res => {
        // If validation fails, try to parse error and show message
        if (!res.ok) {
          return res.json().then(err => { throw err; });
        }
        return res.json ? res.json() : res.text();
      })
      .then(data => {
        loadFeedbacks();
        form.reset();
        document.getElementById('fileName').textContent = 'No files chosen';
        submitBtn.disabled = false;
        submitBtn.textContent = 'Submit Feedback';
      })
      .catch(err => {
        let msg = 'Failed to submit feedback.';
        if (err && err.errors) {
          msg += '\n' + Object.values(err.errors).map(arr => arr.join(', ')).join('\n');
        }
        alert(msg);
        submitBtn.disabled = false;
        submitBtn.textContent = 'Submit Feedback';
      });
    });




    function disableStatusSelects() {
      const statusSelects = document.querySelectorAll('.status-select');
      statusSelects.forEach(select => {
        select.disabled = true;
      });
      
      // Disable buttons
      document.getElementById('markAllCompleted').disabled = true;
      document.getElementById('markDayComplete').disabled = true;
      document.getElementById('markAllCompleted').style.opacity = '0.5';
      document.getElementById('markDayComplete').style.opacity = '0.5';
    }

    window.tasksByDay = @json($tasksByDay);
    window.daysOfWeek = @json($daysOfWeek);
    window.currentDayIndex = {{ array_search($currentDay, $daysOfWeek) }};
    window.dayCompletionStatus = @json($dayCompletionStatus ?? []);
    window.selectedRoom = @json($selectedRoom);

    // --- FEEDBACK SECTION LOGIC ---

    // Helper: get current feedback selection values
    function getFeedbackSelection() {
      // Compute the correct day number for the selected week/month/year and day name
      const week = document.getElementById('weekSelect').value;
      const month = document.getElementById('monthSelect').value;
      const year = document.getElementById('yearSelect').value;
      const dayName = document.getElementById('currentDay').textContent;
      let dayNumber = getDayNumberForSelection(year, month, week, dayName);
      return {
        room_number: window.selectedRoom,
        day: dayNumber,
        week: week,
        month: month,
        year: year
      };
    }

    // Compute the day number (1-31) for the selected week/month/year and day name
    function getDayNumberForSelection(year, month, week, dayName) {
      if (!year || !month || !week || !dayName) return '';
      // Find the first day of the month
      const firstOfMonth = new Date(year, month - 1, 1);
      // Determine week start (Sunday or Monday) based on dayMap
      const dayMap = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
      let firstWeekday = new Date(firstOfMonth);
      // If week starts on Sunday
      if (dayMap[0] === "Sunday") {
        while (firstWeekday.getDay() !== 0) {
          firstWeekday.setDate(firstWeekday.getDate() - 1);
        }
      } else {
        // Default to Monday
        while (firstWeekday.getDay() !== 1) {
          firstWeekday.setDate(firstWeekday.getDate() - 1);
        }
      }
      // Calculate the start of the requested week
      const weekStart = new Date(firstWeekday);
      weekStart.setDate(weekStart.getDate() + (week - 1) * 7);
      // Map day name to offset
      const dayOffsets = {
        "Sunday": 0, "Monday": 1, "Tuesday": 2, "Wednesday": 3,
        "Thursday": 4, "Friday": 5, "Saturday": 6
      };
      if (!(dayName in dayOffsets)) return '';
      const date = new Date(weekStart);
      date.setDate(date.getDate() + dayOffsets[dayName]);
      // Always return the day number, even if outside selected month
      return date.getDate();
    }

    // Set hidden feedback form fields to match current selection
    function syncFeedbackFormFields() {
      const sel = getFeedbackSelection();
      document.getElementById('feedbackDay').value = sel.day;
      document.getElementById('feedbackWeek').value = sel.week;
      document.getElementById('feedbackMonth').value = sel.month;
      document.getElementById('feedbackYear').value = sel.year;
    }

    // Fetch and display feedbacks for current selection
    function loadFeedbacks() {
      const sel = getFeedbackSelection();
      const feedbackForm = document.getElementById('feedbackForm');
      const feedbacksContainer = document.getElementById('submittedFeedbacks');
      if (!sel.room_number || !sel.day || !sel.week || !sel.month || !sel.year) {
        feedbacksContainer.innerHTML = '';
        feedbackForm.style.display = '';
        return;
      }
      fetch(`/feedbacks?room_number=${encodeURIComponent(sel.room_number)}&day=${encodeURIComponent(sel.day)}&week=${encodeURIComponent(sel.week)}&month=${encodeURIComponent(sel.month)}&year=${encodeURIComponent(sel.year)}`, {
        headers: { 'X-Requested-With': 'XMLHttpRequest' }
      })
      .then(res => res.text())
      .then(html => {
        // Only show the first feedback if multiple are returned
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;
        const cards = tempDiv.querySelectorAll('.feedback-card');
        if (cards.length > 1) {
          // Remove all but the first feedback card
          for (let i = 1; i < cards.length; i++) {
            cards[i].remove();
          }
        }
        feedbacksContainer.innerHTML = tempDiv.innerHTML;
        const hasFeedback = tempDiv.querySelector('.feedback-card') !== null;
        if (hasFeedback) {
          feedbackForm.style.display = 'none';
        } else {
          feedbackForm.style.display = '';
        }
      })
      .catch(() => {
        feedbacksContainer.innerHTML = '<div style="color:red;">Failed to load feedbacks.</div>';
        feedbackForm.style.display = '';
      });
    }

    // On feedback form submit, reload feedbacks after successful submission
    document.getElementById('feedbackForm').addEventListener('submit', function(e) {
      // Always sync hidden fields first!
      syncFeedbackFormFields();

      const fileInput = document.getElementById('feedback_file');
      if (!validateFiles(fileInput)) {
        e.preventDefault();
        return;
      }
      // Validate required hidden fields
      const day = document.getElementById('feedbackDay').value;
      const week = document.getElementById('feedbackWeek').value;
      const month = document.getElementById('feedbackMonth').value;
      const year = document.getElementById('feedbackYear').value;
      if (!day || !week || !month || !year) {
        e.preventDefault();
        alert('Please select week, month, and year before submitting feedback.');
        return;
      }
      e.preventDefault(); // Prevent default form submission

      const form = this;
      const formData = new FormData(form);

      // Disable submit button to prevent double submit
      const submitBtn = document.getElementById('submitFeedback');
      submitBtn.disabled = true;
      submitBtn.textContent = 'Submitting...';

      fetch(form.action, {
        method: 'POST',
        headers: {
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
          'Accept': 'application/json'
        },
        body: formData
      })
      .then(res => {
        // If validation fails, try to parse error and show message
        if (!res.ok) {
          return res.json().then(err => { throw err; });
        }
        return res.json ? res.json() : res.text();
      })
      .then(data => {
        loadFeedbacks();
        form.reset();
        document.getElementById('fileName').textContent = 'No files chosen';
        submitBtn.disabled = false;
        submitBtn.textContent = 'Submit Feedback';
      })
      .catch(err => {
        let msg = 'Failed to submit feedback.';
        if (err && err.errors) {
          msg += '\n' + Object.values(err.errors).map(arr => arr.join(', ')).join('\n');
        }
        alert(msg);
        submitBtn.disabled = false;
        submitBtn.textContent = 'Submit Feedback';
      });
    });

    // On day/week/month/year change, sync feedback form fields and reload feedbacks
    function onFeedbackSelectionChange() {
      syncFeedbackFormFields();
      loadFeedbacks();
    }

    // Attach listeners to day/week/month/year selectors
    document.getElementById('weekSelect').addEventListener('change', onFeedbackSelectionChange);
    document.getElementById('monthSelect').addEventListener('change', onFeedbackSelectionChange);
    document.getElementById('yearSelect').addEventListener('change', onFeedbackSelectionChange);


    // Also update feedback form fields when day changes via code
    function setDayByName(dayName) {
      // ...existing code...
      syncFeedbackFormFields();
      loadFeedbacks();
    }

    // On page load, sync and load feedbacks
    document.addEventListener('DOMContentLoaded', function() {
      syncFeedbackFormFields();
      loadFeedbacks();
    });

    // Feedback edit/delete handlers
    document.addEventListener('click', function(e) {
      // Edit Feedback
      if (e.target.classList.contains('edit-feedback-btn')) {
        e.preventDefault();
        const card = e.target.closest('.feedback-card');
        const feedbackId = e.target.getAttribute('data-id');
        const commentDiv = card.querySelector('div[style*="background:whitesmoke"]');
        const oldComment = commentDiv ? commentDiv.textContent.trim() : '';
        const photoPaths = card.getAttribute('data-photo-paths') ? JSON.parse(card.getAttribute('data-photo-paths')) : [];
        // Improved edit form layout
        let formHtml = `
          <form class="edit-feedback-form" enctype="multipart/form-data" data-id="${feedbackId}" style="margin-bottom:10px; background:#f4f8fc; border-radius:10px; padding:18px 18px 10px 18px; box-shadow:0 1px 4px #e5e9f2;">
            <div style="margin-bottom:12px;">
              <textarea name="feedback" rows="4" required style="width:100%;margin-bottom:10px;border-radius:6px;border:1px solid #b6c2d2;padding:8px;resize:vertical;">${oldComment.replace(/"/g, '&quot;')}</textarea>
            </div>
            <div style="margin-bottom:12px;">
              <label style="font-weight:500;display:block;margin-bottom:4px;">Current Photos:</label>
              <div style="display:flex;gap:10px;flex-wrap:wrap;">
                ${photoPaths.map(img => `
                  <div style="position:relative;display:inline-block;">
                    <img src="/storage/${img}" style="width:80px;height:60px;object-fit:cover;border-radius:6px;border:1px solid #dbe4f3;">
                    <button type="button" class="remove-photo-btn" data-path="${img}" style="position:absolute;top:2px;right:2px;background:#e22a1d;color:#fff;border:none;border-radius:50%;width:20px;height:20px;line-height:18px;font-size:14px;cursor:pointer;">&times;</button>
                  </div>
                `).join('')}
              </div>
            </div>
            <div style="margin-bottom:18px;">
              <label style="font-weight:500;">Add More Photos (max 3):</label>
              <input type="file" name="feedback_files[]" multiple accept="image/*" style="margin-left:8px;" />
            </div>
            <div style="display:flex;gap:12px;justify-content:flex-end;">
              <button type="submit" class="btn btn-success btn-sm" style="padding:6px 22px;font-size:15px;border-radius:6px;display:flex;align-items:center;min-width:100px;">
                <i class="fa fa-save" style="margin-right:6px;"></i>Save
              </button>
              <button type="button" class="btn btn-secondary btn-sm cancel-edit-feedback" style="padding:6px 22px;font-size:15px;border-radius:6px;display:flex;align-items:center;min-width:80px;">
                <i class="fa fa-times" style="margin-right:3px;"></i>Cancel
              </button>
            </div>
          </form>
        `;
        card.querySelector('div[style*="background:whitesmoke"]').parentNode.insertAdjacentHTML('beforebegin', formHtml);
        card.querySelector('div[style*="background:whitesmoke"]').style.display = 'none';
        e.target.style.display = 'none';
        card.querySelector('.delete-feedback-btn').style.display = 'none';
      }

      // Delete Feedback
      if (e.target.classList.contains('delete-feedback-btn')) {
        e.preventDefault();
        if (!confirm('Are you sure you want to delete this feedback?')) return;
        const feedbackId = e.target.getAttribute('data-id');
        fetch('/feedback/delete', {
          method: 'POST',
          headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ id: feedbackId })
        })
        .then(res => res.json())
        .then(data => {
          if (data.success) {
            loadFeedbacks();
          } else {
            alert('Failed to delete feedback.');
          }
        });
      }

      // Remove photo in edit form
      if (e.target.classList.contains('remove-photo-btn')) {
        e.preventDefault();
        e.target.parentNode.remove();
      }

      // Cancel edit
      if (e.target.classList.contains('cancel-edit-feedback')) {
        e.preventDefault();
        loadFeedbacks();
      }
    });

    // Handle edit feedback form submit (AJAX)
    document.addEventListener('submit', function(e) {
      if (e.target.classList.contains('edit-feedback-form')) {
        e.preventDefault();
        const form = e.target;
        const feedbackId = form.getAttribute('data-id');
        const formData = new FormData(form);
        formData.append('id', feedbackId);
        // Collect removed photos
        const removedPhotos = [];
        form.querySelectorAll('.remove-photo-btn').forEach(btn => {
          removedPhotos.push(btn.getAttribute('data-path'));
        });
        removedPhotos.forEach(path => formData.append('remove_photos[]', path));
        fetch('/feedback/edit', {
          method: 'POST',
          headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
          },
          body: formData
        })
        .then(res => res.json())
        .then(data => {
          if (data.success) {
            loadFeedbacks();
          } else {
            alert('Failed to update feedback.');
          }
        });
      }
    });
  </script>
</body>
</html>
</html>
