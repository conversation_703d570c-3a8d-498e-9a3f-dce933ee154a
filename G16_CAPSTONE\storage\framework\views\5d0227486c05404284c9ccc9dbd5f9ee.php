<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
  <title>General Task Assignment Dashboard</title>

  <!-- External CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">

  <style>
    /* ===== BASE STYLES ===== */
    body {
      background: #eef2f7;
      font-family: "Poppins", sans-serif;
      margin: 0;
      padding: 0;
    }



    .row {
      font-family: 'Poppins', sans-serif;
      font-size: 20px;
      font-weight: 450; 
    }

    /* ===== HEADER STYLES ===== */
    header {
      background-color: #22BBEA;
      color: white;
      padding: 25px;
      text-align: center;
    }
   header {
      background-color: #22BBEA;
      color: white;
      padding: 20px 30px;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 1000;
    }

    .header-title {
      color: white !important;
      font-weight: 700;
      font-size: 2.5rem;
      margin-bottom: 20px;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .logo {
      margin-left: none;
    }

     .logo img {
      width: 240px;
      height: auto;
      margin-left: 0; /* Changed from 150px to 0 */
      display: block; /* Changed from flex to block */
    } 
    /* ===== NAVIGATION STYLES ===== */
    .nav-buttons {
      margin-top: 15px;
    }

    .nav-btn {
      background-color: rgba(255,255,255,0.2);
      color: white !important;
      border: 2px solid rgba(255,255,255,0.3);
      padding: 10px 25px;
      border-radius: 25px;
      font-weight: 600;
      text-decoration: none;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }

    .nav-btn:hover {
      background-color: rgba(255,255,255,0.3);
      border-color: rgba(255,255,255,0.6);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      color: white !important;
    }

    .nav-btn.active {
      background-color: white;
      color: #22BBEA !important;
      border-color: white;
      font-weight: 700;
    }

    .nav-btn.active:hover {
      background-color: #f8f9fa;
      color: #22BBEA !important;
    }

    /* ===== SIDEBAR STYLES ===== */
    .sidebar {
      border-right: 3px solid #22BBEA;
      margin-top: 130px; /* Adjusted to account for fixed header */ 
    }

    .sidebar-icon {
      width: 30px;
      height: 30px;
      margin-right: 8px;
      vertical-align: middle;
    }

    .sidebar h3 {
      margin-top: 0;
    }

    .sidebar ul {
      list-style: none;
      padding-left: 0;
    }

    .sidebar ul li {
      margin: 15px 0;
    }

    .sidebar ul li a {
      text-decoration: none;
      color: black;
    }

    .sidebar ul li:hover {
      background: #fa5408;
      color: white;
      max-width: 100%;
      border-radius: 5px;
      transition: background-color 0.3s ease, color 0.3s ease;
    }

    .sidebar ul li:hover a {
      color: white;
      transition: color 0.3s ease;
    }

    /* ===== CONTENT STYLES ===== */
    .content {
      flex: 1;
      padding: 30px;
      margin-top: 200px; /* Adjusted to account for fixed header */
      font-display: center;
    }

    
    .dashboard-title {
      font-weight: 700;
      color: #333;
    }
    .category-card {
      position: relative;
      border: none;
      border-radius: 20px;
      padding: 25px;
      transition: all 0.4s ease;
      color: #333;
      box-shadow: 0 8px 25px rgba(0,0,0,0.08);
      cursor: pointer;
      user-select: none;
      max-height: 300px;
      overflow-y: auto;
      backdrop-filter: blur(10px);
    }

    /* Individual Pastel Colors for Each Category */
    .category-card:nth-child(1) {
      background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 50%, #f48fb1 100%);
      border: 2px solid #f8bbd9;
    }

    .category-card:nth-child(2) {
      background: linear-gradient(135deg, #e3f2fd 0%, #90caf9 50%, #64b5f6 100%);
      border: 2px solid #90caf9;
    }

    .category-card:nth-child(3) {
      background: linear-gradient(135deg, #e8f5e8 0%, #a5d6a7 50%, #81c784 100%);
      border: 2px solid #a5d6a7;
    }

    .category-card:nth-child(4) {
      background: linear-gradient(135deg, #fff8e1 0%, #ffcc02 50%, #ffc107 100%);
      border: 2px solid #ffcc02;
    }

    .category-card:nth-child(5) {
      background: linear-gradient(135deg, #f3e5f5 0%, #ce93d8 50%, #ba68c8 100%);
      border: 2px solid #ce93d8;
    }

    .category-card:nth-child(6) {
      background: linear-gradient(135deg, #fff3e0 0%, #ffab91 50%, #ff8a65 100%);
      border: 2px solid #ffab91;
    }
    .category-label {
      font-size: 22px;
      font-weight: 800;
      color: #2c3e50;
      margin-bottom: 15px;
      padding-bottom: 8px;
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
      position: relative;
    }

    .category-label::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background: linear-gradient(90deg, rgba(255,255,255,0.8), rgba(255,255,255,0.4), rgba(255,255,255,0.8));
      border-radius: 2px;
    }
    .task-list {
      list-style-type: none;
      padding-left: 0;
      margin-bottom: 0;
      font-size: 14px;
      max-height: 180px;
      overflow-y: auto;
    }
    .task-list li {
      padding: 3px 0;
      border-bottom: 1px solid #ccc3;
    }
    .btn-custom {
      border-radius: 30px;
      padding: 8px 20px;
      font-weight: 500;
    }
    .table-container {
      background: white;
      padding: 20px;
      border-radius: 15px;
      box-shadow: 0 0 15px rgba(0,0,0,0.05);
    }
    .student-row td {
      vertical-align: middle;
    }
    .category-card:hover {
      transform: translateY(-12px) scale(1.03);
      box-shadow: 0 15px 40px rgba(0,0,0,0.2);
    }

    /* Enhanced hover effects for each category */
    .category-card:nth-child(1):hover {
      box-shadow: 0 15px 40px rgba(244, 143, 177, 0.4);
    }

    .category-card:nth-child(2):hover {
      box-shadow: 0 15px 40px rgba(100, 181, 246, 0.4);
    }

    .category-card:nth-child(3):hover {
      box-shadow: 0 15px 40px rgba(129, 199, 132, 0.4);
    }

    .category-card:nth-child(4):hover {
      box-shadow: 0 15px 40px rgba(255, 204, 2, 0.4);
    }

    .category-card:nth-child(5):hover {
      box-shadow: 0 15px 40px rgba(186, 104, 200, 0.4);
    }

    .category-card:nth-child(6):hover {
      box-shadow: 0 15px 40px rgba(255, 171, 145, 0.4);
    }
    nav.navbar-custom {
      background-color: #0d6efd;
      color: white;
    }
    nav.navbar-custom .navbar-brand,
    nav.navbar-custom .nav-link {
      color: white;
      font-weight: 600;
    }
    nav.navbar-custom .nav-link:hover {
      color: #ffd43b;
    }

    /* Centered Form Modal */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.4);
      display: none;
      align-items: center;
      justify-content: center;
      z-index: 1000;
    }
    .modal-content {
      background: white;
      padding: 25px;
      border-radius: 15px;
      width: 1400px;
      max-width: 95vw;
      margin: 0 auto;
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
      position: relative;
      left: 50%;
      transform: translateX(-50%);
    }
    .close-btn {
      position: absolute;
      top: 12px;
      right: 15px;
      background: #dc3545;
      border: none;
      color: white;
      font-weight: 700;
      font-size: 18px;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      line-height: 22px;
      cursor: pointer;
      user-select: none;
      transition: background-color 0.3s ease;
    }
    .close-btn:hover {
      background: #a71d2a;
    }

    /* Student Assignment Modal */
    #studentAssignModal {
      display: none;
      align-items: center;
      justify-content: center;
    }
    #studentAssignModal .modal-content {
      width: 1400px !important;
      max-width: 98vw;
      min-height: 420px;
      transition: width 0.3s, min-height 0.3s;
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: stretch;
      font-size: 1.15rem;
    }
    #studentAssignModal .modal-content .fw-bold {
      font-size: 1.3rem;
    }
    #studentAssignModal .modal-content .coordinator-label {
      font-weight: 500;
      margin-bottom: 8px;
      font-size: 1.1rem;
      color: #444;
      text-align: left;
    }
    #studentAssignModal .modal-content .coordinator-name {
      font-weight: 400;
      font-size: 1.15rem;
      margin-left: 3px;
      color: #222;
    }
    #studentAssignModal .modal-content ul.list-group-flush li {
      font-size: 1.08rem;
      padding: 8px 0;
    }

    /* Task Assignment History Modal */
    #historyModal.show {
      display: flex !important;
      align-items: center;
      justify-content: center;
    }
    #historyModal .modal-dialog {
      margin: 1.75rem auto;
      max-width: 95%;
      width: 95%;
    }
    #historyModal .modal-content {
      width: 100%;
      max-width: 2000px;
      position: relative;
      margin: 0 auto;
    }

    /* Add spacing to history modal table cells */
    #historyModal .table-bordered td {
      padding: 15px !important;
    }

    #historyModal .table-sm td {
      padding: 8px 12px !important;
      border: 1px solid #dee2e6 !important;
    }

    #historyModal .table-sm th {
      padding: 10px 12px !important;
      border: 1px solid #dee2e6 !important;
    }

    /* Timeline Styles */
    .timeline-item {
      position: relative;
      padding-left: 30px;
      margin-bottom: 20px;
    }
    .timeline-item .dot {
      position: absolute;
      left: 0;
      top: 5px;
      width: 10px;
      height: 10px;
      border-radius: 50%;
    }
    .dot { width: 12px; height: 12px; border-radius: 50%; display: inline-block; }
    .dot-blue { background: #0d6efd; }
    .dot-pink { background: #e83e8c; }
    .dot-green { background: #28a745; }
    .timeline-item { background: #f8f9fa; border-radius: 10px; padding: 12px 16px; }

    /* Custom container width for maximum space utilization */
    .container-fluid {
      --bs-gutter-x: 1rem;
      --bs-gutter-y: 0;
      max-width: 100vw;
      width: 100%;
      padding-right: calc(var(--bs-gutter-x) * .5);
      padding-left: calc(var(--bs-gutter-x) * .5);
      margin: 0;
    }

    /* Optimize main content spacing */
    main.col-md-10 {
      padding: 15px !important;
      margin: 0;
    }

    /* Optimize row and column spacing */
    .row {
      --bs-gutter-x: 1rem;
      --bs-gutter-y: 1rem;
      margin: 0;
    }

    .col-lg-4, .col-md-6 {
      padding-left: 8px;
      padding-right: 8px;
    }

    /* Reduce unnecessary margins */
    .dashboard-title {
      margin-bottom: 10px !important;
    }

    .text-muted.mb-4 {
      margin-bottom: 20px !important;
    }

    /* Edit Button with Blue Line Styling */
    .btn-edit-blue {
      background: linear-gradient(135deg, #007bff, #0056b3);
      border: 2px solid #007bff;
      color: white;
      padding: 8px 20px;
      border-radius: 8px;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .btn-edit-blue:before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s;
    }

    .btn-edit-blue:hover {
      background: linear-gradient(135deg, #0056b3, #004085);
      border-color: #0056b3;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }

    .btn-edit-blue:hover:before {
      left: 100%;
    }

    .btn-edit-blue:active {
      transform: translateY(0);
      box-shadow: 0 2px 6px rgba(0, 123, 255, 0.2);
    }

    /* Coordinator highlighting styles for View Members modal */
    .coordinator-highlight-cell {
      background-color: #f5e6a3 !important;
      border: 1px solid #d4c69a !important;
      padding: 8px !important;
    }

    .coordinator-name {
      font-weight: 600 !important;
      color: #856404 !important;
    }

    /* Coordinator highlighting for View History rows */
    .coordinator-highlight-row {
      background-color: #fff3cd !important;
      border: 1px solid #ffeaa7 !important;
      padding: 4px 8px !important;
      border-radius: 4px !important;
      margin-bottom: 2px !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }



    /* Make all member names in View History single line */
    .modal-body .row .col div {
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      line-height: 1.4 !important;
      margin-bottom: 2px !important;
    }

    /* Coordinator highlighting for Edit Members modal */
    .coordinator-highlight-edit {
      background-color: #f5e6a3 !important;
      border: 1px solid #d4c69a !important;
      border-radius: 6px !important;
    }

    .coordinator-highlight-edit:hover {
      border-color: #ff9800 !important;
      box-shadow: 0 2px 8px rgba(255, 193, 7, 0.5) !important;
    }

    /* Coordinator highlighting styles */
    .coordinator-highlight-cell {
      background-color: #f5e6a3 !important;
      border: 1px solid #d4c69a !important;
    }

    .coordinator-name {
      font-weight: 600 !important;
      color: #856404 !important;
    }

    /* Edit Member Styles */
    .member-list-container {
      max-height: 400px;
      overflow-y: auto;
      padding-right: 10px;
    }

    .member-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 15px;
      margin-bottom: 8px;
      background-color: #ffffff;
      border-radius: 6px;
      border: 1px solid #e0e0e0;
      transition: all 0.2s ease;
    }

    .member-row:hover {
      border-color: #007bff;
      box-shadow: 0 1px 4px rgba(0,123,255,0.15);
    }

    .member-info {
      flex: 1;
    }

    .member-name {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
      font-size: 0.95em;
    }

    .member-comment {
      font-size: 0.85em;
      color: #666;
      font-style: italic;
      margin-top: 4px;
    }

    .comment-edit-area {
      margin-top: 8px;
    }

    .comment-input {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 0.9em;
    }

    .member-actions {
      display: flex;
      gap: 8px;
      margin-left: 15px;
    }

    .btn-edit-small, .btn-save-small {
      padding: 6px 16px;
      font-size: 0.8em;
      border-radius: 4px;
      border: none;
      cursor: pointer;
      transition: all 0.2s ease;
      font-weight: 500;
      min-width: 50px;
    }

    .btn-edit-small {
      background: #17a2b8;
      color: white;
    }

    .btn-edit-small:hover {
      background: #138496;
    }

    .btn-save-small {
      background: #28a745;
      color: white;
    }

    .btn-save-small:hover {
      background: #1e7e34;
    }

    .btn-save-small:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    .comment-input {
      width: 100%;
      padding: 4px 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 12px;
      margin-top: 4px;
    }

    .member-comment {
      font-size: 11px;
      color: #666;
      font-style: italic;
      margin-top: 2px;
    }

    /* ===== PASTEL TASK CARD COLORS ===== */
    :root {
      --pastel-pink: #fce4ec;
      --pastel-blue: #e3f2fd;
      --pastel-green: #e8f5e8;
      --pastel-yellow: #fff8e1;
      --pastel-purple: #f3e5f5;
      --pastel-orange: #fff3e0;
      --pastel-teal: #e0f2f1;
      --pastel-lavender: #f8f4ff;
      --pastel-peach: #ffeee6;
      --pastel-mint: #e8f8f5;
      --pastel-coral: #ffe8e8;
      --pastel-sky: #e6f3ff;
    }

    /* Task Card Pastel Styling - Soft & Easy on Eyes */
    .col-lg-4:nth-child(1) .category-card > div {
      background: linear-gradient(135deg, #fce4ec 0%, #f8bbd9 100%) !important;
      border: 1px solid #f8bbd9 !important;
      box-shadow: 0 4px 15px rgba(248, 187, 217, 0.2) !important;
      transition: all 0.3s ease !important;
    }

    .col-lg-4:nth-child(1) .category-card > div:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 8px 25px rgba(248, 187, 217, 0.3) !important;
    }

    .col-lg-4:nth-child(1) .category-label {
      color: #ad1457 !important;
    }

    .col-lg-4:nth-child(2) .category-card > div {
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
      border: 1px solid #bbdefb !important;
      box-shadow: 0 4px 15px rgba(187, 222, 251, 0.2) !important;
      transition: all 0.3s ease !important;
    }

    .col-lg-4:nth-child(2) .category-card > div:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 8px 25px rgba(187, 222, 251, 0.3) !important;
    }

    .col-lg-4:nth-child(2) .category-label {
      color: #1565c0 !important;
    } 

    .col-lg-4:nth-child(3) .category-card > div {
      background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%) !important;
      border: 1px solid #c8e6c9 !important;
      box-shadow: 0 4px 15px rgba(200, 230, 201, 0.2) !important;
      transition: all 0.3s ease !important;
    }

    .col-lg-4:nth-child(3) .category-card > div:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 8px 25px rgba(200, 230, 201, 0.3) !important;
    }

    .col-lg-4:nth-child(3) .category-label {
      color: #2e7d32 !important;
    }

    .col-lg-4:nth-child(4) .category-card > div {
      background: linear-gradient(135deg, #fff8e1 0%, #fff3c4 100%) !important;
      border: 1px solid #fff3c4 !important;
      box-shadow: 0 4px 15px rgba(255, 243, 196, 0.2) !important;
      transition: all 0.3s ease !important;
    }

    .col-lg-4:nth-child(4) .category-card > div:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 8px 25px rgba(255, 243, 196, 0.3) !important;
    }

    .col-lg-4:nth-child(4) .category-label {
      color: #f57c00 !important;
    }

    .col-lg-4:nth-child(5) .category-card > div {
      background: linear-gradient(135deg, #f3e5f5 0%, #e1bee7 100%) !important;
      border: 1px solid #e1bee7 !important;
      box-shadow: 0 4px 15px rgba(225, 190, 231, 0.2) !important;
      transition: all 0.3s ease !important;
    }

    .col-lg-4:nth-child(5) .category-card > div:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 8px 25px rgba(225, 190, 231, 0.3) !important;
    }

    .col-lg-4:nth-child(5) .category-label {
      color: #6a1b9a !important;
    }

    .col-lg-4:nth-child(6) .category-card > div {
      background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%) !important;
      border: 1px solid #ffcc80 !important;
      box-shadow: 0 4px 15px rgba(255, 204, 128, 0.2) !important;
      transition: all 0.3s ease !important;
    }

    .col-lg-4:nth-child(6) .category-card > div:hover {
      transform: translateY(-3px) !important;
      box-shadow: 0 8px 25px rgba(255, 204, 128, 0.3) !important;
    }

    .col-lg-4:nth-child(6) .category-label {
      color: #ef6c00 !important;
    }

    /* Enhanced badge styling for pastel cards */
    .category-card .badge {
      border-radius: 20px !important;
      padding: 8px 15px !important;
      font-size: 14px !important;
      font-weight: 700 !important;
      margin: 0 5px !important;
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1) !important;
    }

    /* Enhanced button styling for pastel cards */
    .category-card .btn {
      border-radius: 25px !important;
      padding: 10px 20px !important;
      font-weight: 600 !important;
      transition: all 0.3s ease !important;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
    }

    .category-card .btn:hover {
      transform: translateY(-2px) !important;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2) !important;
    }


  </style>
</head>
<body>
    <header>
    <div class="logo">
      <img src="<?php echo e(asset('images/pnlogo-header.png')); ?>" alt="PN Logo">
    </div>
  </header>
  <div class="container-fluid">
    <div class="row">
      <!-- Sidebar -->
      <nav class="col-md-2 d-none d-md-block sidebar bg-light">
        <ul class="nav flex-column">
         <li class="nav-item"><a href="<?php echo e(route('dashboard')); ?>" class="nav-link sidebar-link">
           <img src="<?php echo e(asset('images/dashboard.png')); ?>" class="sidebar-icon">Dashboard </a>
        </li>
        <li class="nav-item"><a href="#" class="nav-link">
          <img src="<?php echo e(asset('images/assign.png')); ?>" class="sidebar-icon">General Tasks</a>
        </li>
           <li class="nav-item"><a href="<?php echo e(route('task.history')); ?>" class="nav-link">
           <img src="<?php echo e(asset('images/history.png')); ?>" class="sidebar-icon"> Room Task History</a>
        </li>
        <li class="nav-item"><a href="<?php echo e(route('reports.index')); ?>" class="nav-link">
          <img src="<?php echo e(asset('images/complaint.png')); ?>" class="sidebar-icon">Reports</a></li>
        <li class="nav-item">
          <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" style="display:inline;">
            <?php echo csrf_field(); ?>
            <a href="#" class="nav-link" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
              <img src="<?php echo e(asset('images/log-out.png')); ?>" class="sidebar-icon">Log Out
            </a>
          </form>
        </li>
      </ul>
    </nav>
      <!-- Main content beside sidebar, centered -->
      <main class="col-md-10" style="min-height: 90vh; padding: 20px; margin-top: 120px;">
        <div class="container-fluid py-3">
          <!-- Flash Messages -->
          <?php if(session('success')): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
              <i class="bi bi-check-circle me-2"></i><?php echo e(session('success')); ?>

              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
          <?php endif; ?>

          <?php if(session('error')): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
              <i class="bi bi-exclamation-triangle me-2"></i><?php echo e(session('error')); ?>

              <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
          <?php endif; ?>



          <!-- CAPSTONE TASKING SYSTEM -->
          <div class="card mb-4">
            <div class="card-header">
              <h5 class="mb-0">Capstone Tasking System</h5>
            </div>
            <div class="card-body">
              <!-- Student Input Section -->
              <div class="row mb-3">
                <div class="col-md-2">
                  <label class="form-label">Date:</label>
                  <input type="date" id="task_date" class="form-control" value="<?php echo e(date('Y-m-d')); ?>">
                </div>
                <div class="col-md-2">
                  <label class="form-label">Batch 2025 Boys:</label>
                  <input type="number" id="batch_2025_male" class="form-control" min="0" value="0">
                </div>
                <div class="col-md-2">
                  <label class="form-label">Batch 2025 Girls:</label>
                  <input type="number" id="batch_2025_female" class="form-control" min="0" value="0">
                </div>
                <div class="col-md-2">
                  <label class="form-label">Batch 2026 Boys:</label>
                  <input type="number" id="batch_2026_male" class="form-control" min="0" value="0">
                </div>
                <div class="col-md-2">
                  <label class="form-label">Batch 2026 Girls:</label>
                  <input type="number" id="batch_2026_female" class="form-control" min="0" value="0">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                  <button type="button" class="btn btn-primary w-100" onclick="loadTaskMatrix()">
                    Load Tasks
                  </button>
                </div>
              </div>

              <!-- Task Assignment Matrix -->
              <div id="taskMatrix">
                <p class="text-muted">Enter student counts and click "Load Tasks" to view assignment matrix</p>
              </div>
            </div>
          </div>

          <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
              <h2 class="dashboard-title mb-2">General Task Assignments</h2>
              <p class="text-muted mb-0">Manage and track all task assignments across categories</p>
            </div>
            <div class="d-flex gap-2">
              <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#taskChecklistModal">
                <i class="bi bi-list-task me-2"></i>General Task Checklist
              </button>
              <a href="<?php echo e(route('batches.index')); ?>" class="btn btn-primary">
                <i class="bi bi-calendar3 me-2"></i>Manage Batches
              </a>
            </div>
          </div>

          <!-- Task Categories & Descriptions Table -->
          <div class="mb-4">
            <div class="table-responsive">
              <table class="table table-bordered table-striped mb-0" style="border-collapse: collapse; width: 100%; border: 3px solid #000000; margin: 0 auto;">
                <thead>
                  <tr>
                    <th colspan="2" style="background-color: #007bff !important; color: white !important; padding: 25px; font-size: 22px; font-weight: bold; text-align: center; border: 3px solid #000000;">Task Categories & Descriptions</th>
                  </tr>
                  <tr>
                    <th style="background-color: #f8f9fa; color: #000000; padding: 25px; font-size: 22px; font-weight: bold; text-align: center; border: 3px solid #000000; width: 30%;">Task Category</th>
                    <th style="background-color: #f8f9fa; color: #000000; padding: 25px; font-size: 22px; font-weight: bold; text-align: center; border: 3px solid #000000; width: 70%;">Description</th>
                  </tr>
                </thead>
                <tbody>
                  <tr style="height: 60px;">
                    <td style="padding: 22px; font-size: 22px; font-weight: bold; color: #000000; border: 3px solid #000000; vertical-align: middle; line-height: 1.4; text-align: center;">Kitchen</td>
                    <td style="padding: 22px; font-size: 22px; color: #000000; border: 3px solid #000000; vertical-align: middle; line-height: 1.4; text-align: left;">Responsible for food preparation, cooking, and maintaining cleanliness in the kitchen area.</td>
                  </tr>
                  <tr style="height: 60px;">
                    <td style="padding: 22px; font-size: 22px; font-weight: bold; color: #000000; border: 3px solid #000000; vertical-align: middle; line-height: 1.4; text-align: center;">Dishwashing</td>
                    <td style="padding: 22px; font-size: 22px; color: #000000; border: 3px solid #000000; vertical-align: middle; line-height: 1.4; text-align: left;">Handles washing, drying, and organizing all kitchen utensils, plates, and cookware.</td>
                  </tr>
                  <tr style="height: 60px;">
                    <td style="padding: 22px; font-size: 22px; font-weight: bold; color: #000000; border: 3px solid #000000; vertical-align: middle; line-height: 1.4; text-align: center;">Dining</td>
                    <td style="padding: 22px; font-size: 22px; color: #000000; border: 3px solid #000000; vertical-align: middle; line-height: 1.4; text-align: left;">Prepares the dining area, sets tables, serves food, and ensures the dining space is clean.</td>
                  </tr>
                  <tr style="height: 60px;">
                    <td style="padding: 22px; font-size: 22px; font-weight: bold; color: #000000; border: 3px solid #000000; vertical-align: middle; line-height: 1.4; text-align: center;">Offices & Conference Rooms</td>
                    <td style="padding: 22px; font-size: 22px; color: #000000; border: 3px solid #000000; vertical-align: middle; line-height: 1.4; text-align: left;">Cleans and organizes offices and conference rooms, including dusting, sweeping, and arranging furniture.</td>
                  </tr>
                  <tr style="height: 60px;">
                    <td style="padding: 22px; font-size: 22px; font-weight: bold; color: #000000; border: 3px solid #000000; vertical-align: middle; line-height: 1.4; text-align: center;">Garbage, Rugs, & Rooftop</td>
                    <td style="padding: 22px; font-size: 22px; color: #000000; border: 3px solid #000000; vertical-align: middle; line-height: 1.4; text-align: left;">Manages waste disposal, cleans rugs, and maintains the cleanliness of the rooftop area.</td>
                  </tr>
                  <tr style="height: 60px;">
                    <td style="padding: 22px; font-size: 22px; font-weight: bold; color: #000000; border: 3px solid #000000; vertical-align: middle; line-height: 1.4; text-align: center;">Ground Floor</td>
                    <td style="padding: 22px; font-size: 22px; color: #000000; border: 3px solid #000000; vertical-align: middle; line-height: 1.4; text-align: left;">Ensures the ground floor is tidy, including sweeping, mopping, and general maintenance.</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div class="row" id="category-cards-container">
            <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
              <div class="col-lg-4 col-md-6">
                <div class="category-card text-center p-0 overflow-hidden" style="background:none; border:none; box-shadow:none;">
                  <div style="height:100%; min-height:100%; border-radius:15px; padding:20px; background:#f8f9fa; box-shadow: 0 0 10px rgba(0,0,0,0.07); position: relative;">
                    <div class="category-label" style="background:none; border:none; margin-bottom:10px; font-size:20px; font-weight:700; color:#333;">
                      <?php echo e($category->name); ?>

                    </div>
                    <div class="mb-2">
                     <?php
             $boys = 0; $girls = 0;
            $coor2025 = null; $coor2026 = null;
          foreach($category->assignments as $assignment) {
         foreach($assignment->assignmentMembers as $member) {
      if ($member->student) {
        if($member->student->gender === 'Male') $boys++;
        if($member->student->gender === 'Female') $girls++;
        if($member->is_coordinator && $member->student->batch == 2025 && !$coor2025) $coor2025 = $member->student->name;
        if($member->is_coordinator && $member->student->batch == 2026 && !$coor2026) $coor2026 = $member->student->name;
      }
    }
  }
?>
                      <span class="badge" style="background:#1565c0; color:#fff; font-weight:600;">Boys: <?php echo e($boys); ?></span>
                      <span class="badge" style="background:#f3f6fb; color:#222; font-weight:600;">Girls: <?php echo e($girls); ?></span>
                    </div>
                    <div class="mb-2">
                      <div><b>Coordinator 2025:</b> <?php echo e($coor2025 ?? 'None Assigned'); ?></div>
                      <div><b>Coordinator 2026:</b> <?php echo e($coor2026 ?? 'None Assigned'); ?></div>
                    </div>
                    <button class="btn btn-outline-primary btn-sm mt-2" data-bs-toggle="modal" data-bs-target="#studentAssignModal<?php echo e($category->id); ?>">View Members</button>
                  </div>
                </div>
              </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
          </div>

          <div class="d-flex justify-content-start align-items-center gap-2 mb-4" style="margin-top: 30px;">
            <a href="<?php echo e(url('/assignments/create')); ?>" class="btn btn-primary btn-custom">+ Assign New Tasks</a>
            <a href="<?php echo e(url('/assignments')); ?>" class="btn btn-success btn-custom">View All Assignments</a>
            <form method="POST" action="<?php echo e(url('/assignments/auto-shuffle')); ?>" style="display:inline;">
              <?php echo csrf_field(); ?>
              <button type="submit" class="btn btn-warning btn-custom">Auto-Shuffle</button>
            </form>
            <button class="btn btn-outline-dark btn-custom" data-bs-toggle="modal" data-bs-target="#historyModal">View History</button>
          </div>

    <div id="task-table-container" class="table-container">
      <table class="table table-hover" style="border-collapse: collapse; width: 100%;">
        <thead class="table-light">
          <tr>
            <th class="text-center" style="padding: 18px; font-size: 18px; font-weight: bold; border: 3px solid #000000; background-color: #f8f9fa;">STUDENT COOR</th>
            <th class="text-center" style="padding: 18px; font-size: 18px; font-weight: bold; border: 3px solid #000000; background-color: #f8f9fa;">CLASS YEAR</th>
            <th class="text-center" style="padding: 18px; font-size: 18px; font-weight: bold; border: 3px solid #000000; background-color: #f8f9fa;">STUDENT COOR</th>
            <th class="text-center" style="padding: 18px; font-size: 18px; font-weight: bold; border: 3px solid #000000; background-color: #f8f9fa;">CLASS YEAR</th>
            <th class="text-center" style="padding: 18px; font-size: 18px; font-weight: bold; border: 3px solid #000000; background-color: #f8f9fa;">CATEGORY</th>
            <th class="text-center" style="padding: 18px; font-size: 18px; font-weight: bold; border: 3px solid #000000; background-color: #f8f9fa;">DATE ASSIGN</th>
          </tr>
        </thead>
        <tbody id="task-table-body">
          <?php $__currentLoopData = $assignments->where('status', 'current')->unique('category_id'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $assignment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <?php
              $coor2025 = $assignment->assignmentMembers->where('is_coordinator', true)->where('student.batch', 2025)->first();
              $coor2026 = $assignment->assignmentMembers->where('is_coordinator', true)->where('student.batch', 2026)->first();
            ?>
            <tr>
              <td class="text-center" style="padding: 16px; font-size: 16px; vertical-align: middle;"><?php echo e($coor2025 ? $coor2025->student->name : 'N/A'); ?></td>
              <td class="text-center" style="padding: 16px; font-size: 16px; vertical-align: middle;"><?php echo e($coor2025 ? $coor2025->student->batch : 'N/A'); ?></td>
              <td class="text-center" style="padding: 16px; font-size: 16px; vertical-align: middle;"><?php echo e($coor2026 ? $coor2026->student->name : 'N/A'); ?></td>
              <td class="text-center" style="padding: 16px; font-size: 16px; vertical-align: middle;"><?php echo e($coor2026 ? $coor2026->student->batch : 'N/A'); ?></td>
              <td class="text-center" style="padding: 16px; font-size: 16px; vertical-align: middle;"><?php echo e($assignment->category->name); ?></td>
              <td class="text-center" style="padding: 16px; font-size: 16px; vertical-align: middle;"><?php echo e($assignment->start_date); ?> - <?php echo e($assignment->end_date); ?></td>
            </tr>
          <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </tbody>
      </table>
    </div>
        </div>
        </main>
      </div>
    </div>

  <!-- Student Assignment Modals for each category -->
  <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
  <div class="modal fade" id="studentAssignModal<?php echo e($category->id); ?>" tabindex="-1" aria-labelledby="studentAssignModalLabel<?php echo e($category->id); ?>" aria-hidden="true">
    <div class="modal-dialog" style="max-width:1500px; width:90vw; margin: 1.75rem auto;">
      <div class="modal-content" style="width: 100%; margin: 0 auto; transform: none; left: auto;">
        <div class="modal-header">
          <h5 class="modal-title" id="studentAssignModalLabel<?php echo e($category->id); ?>" style="font-size: 22px;">Members for <?php echo e($category->name); ?></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <table class="table table-bordered" style="font-size: 18px;">
            <thead style="background-color: #495057;">
              <tr>
                <th style="font-size: 20px; padding: 15px; text-align: center; background-color: #495057; color: white; font-weight: bold;">BATCH 2025</th>
                <th style="font-size: 20px; padding: 15px; text-align: center; background-color: #495057; color: white; font-weight: bold;">BATCH 2026</th>
              </tr>
            </thead>
            <tbody>
<?php
  $boys = 0; $girls = 0;
  $coor2025 = null; $coor2026 = null;
  foreach($category->assignments as $assignment) {
    foreach($assignment->assignmentMembers as $member) {
      if ($member->student) {
        if($member->student->gender === 'Male') $boys++;
        if($member->student->gender === 'Female') $girls++;
        if($member->is_coordinator && $member->student->batch == 2025 && !$coor2025) $coor2025 = $member->student->name;
        if($member->is_coordinator && $member->student->batch == 2026 && !$coor2026) $coor2026 = $member->student->name;
      }
    }
  }
?>
<?php
    // Get current assignment for this category
    $currentAssignment = $category->assignments->where('status', 'current')->first();
    $members2025 = collect();
    $members2026 = collect();

    if ($currentAssignment) {
        // Group members by batch
        $members2025 = $currentAssignment->assignmentMembers
            ->filter(function($member) {
                return $member->student && $member->student->batch == 2025;
            })
            ->values();

        $members2026 = $currentAssignment->assignmentMembers
            ->filter(function($member) {
                return $member->student && $member->student->batch == 2026;
            })
            ->values();
    }

    $maxRows = max($members2025->count(), $members2026->count());
?>
<?php for($i = 0; $i < $maxRows; $i++): ?>
    <tr>
        <td class="<?php echo e(isset($members2025[$i]) && $members2025[$i]->is_coordinator ? 'coordinator-highlight-cell' : ''); ?>" style="padding: 15px; font-size: 18px;">
            <?php if(isset($members2025[$i]) && $members2025[$i]->student): ?>
                <div>
                    <span class="<?php echo e($members2025[$i]->is_coordinator ? 'coordinator-name' : ''); ?>" style="font-size: 18px;"><?php echo e($members2025[$i]->student->name); ?></span>
                    <?php if($members2025[$i]->is_coordinator): ?>
                        <span class="badge bg-warning text-dark ms-1" style="font-size: 0.6em;">COORDINATOR</span>
                    <?php endif; ?>
                </div>
                <?php if($members2025[$i]->comments): ?>
                    <span class="text-muted" style="font-size: 16px;"> (<?php echo e($members2025[$i]->comments); ?>)</span>
                <?php endif; ?>
            <?php endif; ?>
        </td>
        <td class="<?php echo e(isset($members2026[$i]) && $members2026[$i]->is_coordinator ? 'coordinator-highlight-cell' : ''); ?>" style="padding: 15px; font-size: 18px;">
            <?php if(isset($members2026[$i]) && $members2026[$i]->student): ?>
                <div>
                    <span class="<?php echo e($members2026[$i]->is_coordinator ? 'coordinator-name' : ''); ?>" style="font-size: 18px;"><?php echo e($members2026[$i]->student->name); ?></span>
                    <?php if($members2026[$i]->is_coordinator): ?>
                        <span class="badge bg-warning text-dark ms-1" style="font-size: 0.6em;">COORDINATOR</span>
                    <?php endif; ?>
                </div>
                <?php if($members2026[$i]->comments): ?>
                    <span class="text-muted" style="font-size: 16px;"> (<?php echo e($members2026[$i]->comments); ?>)</span>
                <?php endif; ?>
            <?php endif; ?>
        </td>
    </tr>
<?php endfor; ?>
            </tbody>
          </table>

          <!-- Add and Delete Buttons - Above Edit Button -->
          <div class="text-center mt-3 mb-2">
            <button type="button" class="btn btn-success me-2" onclick="testAddModal(<?php echo e($category->id); ?>, '<?php echo e($category->name); ?>')">
              <i class="bi bi-person-plus me-1"></i>Add
            </button>
            <button type="button" class="btn btn-danger" onclick="testDeleteModal(<?php echo e($category->id); ?>, '<?php echo e($category->name); ?>')">
              <i class="bi bi-person-dash me-1"></i>Delete
            </button>
          </div>

          <!-- Edit Button - Below Add/Delete buttons -->
          <div class="text-center">
            <button type="button" class="btn btn-edit-blue" onclick="testEditModal(<?php echo e($category->id); ?>, '<?php echo e($category->name); ?>')">
              <i class="bi bi-pencil-square me-1"></i>Edit Members
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

  <!-- Add Members Modal -->
  <div class="modal fade" id="addMembersModal" tabindex="-1" aria-labelledby="addMembersModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="max-width:1500px; width:95vw;">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="addMembersModalLabel">Add Members to <span id="addCategoryName"></span></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <!-- Add New Student Section -->
          <div class="row mb-4">
            <div class="col-md-6">
              <div class="card border-success">
                <div class="card-header bg-success text-white">
                  <h6 class="mb-0">Add New Student - Batch 2025</h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-8">
                      <input type="text" class="form-control" id="newStudent2025Name" placeholder="Enter student name" style="font-size: 16px;">
                    </div>
                    <div class="col-md-4">
                      <select class="form-control" id="newStudent2025Gender" style="font-size: 16px;">
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                      </select>
                    </div>
                  </div>
                  <button type="button" class="btn btn-success btn-sm mt-2" onclick="addNewStudentToCategory(2025)">
                    <i class="bi bi-plus-circle"></i> Add to Category
                  </button>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card border-success">
                <div class="card-header bg-success text-white">
                  <h6 class="mb-0">Add New Student - Batch 2026</h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-8">
                      <input type="text" class="form-control" id="newStudent2026Name" placeholder="Enter student name" style="font-size: 16px;">
                    </div>
                    <div class="col-md-4">
                      <select class="form-control" id="newStudent2026Gender" style="font-size: 16px;">
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                      </select>
                    </div>
                  </div>
                  <button type="button" class="btn btn-success btn-sm mt-2" onclick="addNewStudentToCategory(2026)">
                    <i class="bi bi-plus-circle"></i> Add to Category
                  </button>
                </div>
              </div>
            </div>
          </div>

          <hr>

          <!-- Existing Students Section -->
          <div class="row">
            <div class="col-md-6">
              <h6 class="fw-bold mb-3 text-primary">Available Students - Batch 2025</h6>
              <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                <table class="table table-hover">
                  <tbody id="availableStudents2025">
                    <!-- Available students will be loaded here -->
                  </tbody>
                </table>
              </div>
            </div>
            <div class="col-md-6">
              <h6 class="fw-bold mb-3 text-primary">Available Students - Batch 2026</h6>
              <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                <table class="table table-hover">
                  <tbody id="availableStudents2026">
                    <!-- Available students will be loaded here -->
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <hr>

          <div class="row">
            <div class="col-12">
              <h6 class="fw-bold mb-3 text-primary">Selected Students to Add</h6>
              <div id="selectedStudentsToAdd" class="border rounded p-3" style="min-height: 100px; background-color: #f8f9fa;">
                <p class="text-muted mb-0">Click on students above to select them for adding to this category.</p>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-success" id="confirmAddMembers">Add Selected Members</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Members Modal -->
  <div class="modal fade" id="deleteMembersModal" tabindex="-1" aria-labelledby="deleteMembersModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="max-width:1400px; width:90vw;">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="deleteMembersModalLabel">Delete Members from <span id="deleteCategoryName"></span></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <h6 class="fw-bold mb-3 text-warning">Current Members - Batch 2025</h6>
              <p class="small text-muted">Click to select for removal from category only</p>
              <div id="currentMembers2025" style="max-height: 300px; overflow-y: auto;">
                <!-- Current members will be loaded here -->
              </div>
            </div>
            <div class="col-md-6">
              <h6 class="fw-bold mb-3 text-warning">Current Members - Batch 2026</h6>
              <p class="small text-muted">Click to select for removal from category only</p>
              <div id="currentMembers2026" style="max-height: 300px; overflow-y: auto;">
                <!-- Current members will be loaded here -->
              </div>
            </div>
          </div>

          <hr>

          <div class="row">
            <div class="col-12">
              <h6 class="fw-bold mb-3 text-warning">Selected Members to Remove from Category</h6>
              <div id="selectedMembersToDelete" class="border rounded p-3" style="min-height: 80px; background-color: #fff3cd;">
                <p class="text-muted mb-0">Click on members above to select them for removal from this category.</p>
              </div>
            </div>
          </div>

          <hr>

          <!-- Delete from System Section -->
          <div class="row">
            <div class="col-12">
              <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                  <h6 class="mb-0"><i class="bi bi-exclamation-triangle"></i> Permanently Delete Students from System</h6>
                </div>
                <div class="card-body">
                  <p class="text-danger small mb-3">
                    <strong>Warning:</strong> This will permanently delete students from the entire system and all assignments.
                  </p>
                  <div class="row">
                    <div class="col-md-6">
                      <h6 class="text-danger">Batch 2025 Students</h6>
                      <div id="systemDeleteMembers2025" style="max-height: 200px; overflow-y: auto;">
                        <!-- Members for system deletion will be loaded here -->
                      </div>
                    </div>
                    <div class="col-md-6">
                      <h6 class="text-danger">Batch 2026 Students</h6>
                      <div id="systemDeleteMembers2026" style="max-height: 200px; overflow-y: auto;">
                        <!-- Members for system deletion will be loaded here -->
                      </div>
                    </div>
                  </div>
                  <div class="mt-3">
                    <h6 class="text-danger">Selected Students to Delete from System</h6>
                    <div id="selectedStudentsToDeleteFromSystem" class="border rounded p-3" style="min-height: 60px; background-color: #f8d7da;">
                      <p class="text-muted mb-0">Click on students above to select them for permanent deletion.</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-warning" id="confirmDeleteMembers">Remove from Category</button>
          <button type="button" class="btn btn-danger" id="confirmDeleteFromSystem">Delete from System</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Edit Members Modal -->
  <div class="modal fade" id="editMembersModal" tabindex="-1" aria-labelledby="editMembersModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="max-width:1300px; width:90vw;">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="editMembersModalLabel">Edit Members for <span id="editCategoryName"></span></h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body" style="padding: 20px;">
          <div class="row">
            <div class="col-md-6">
              <h6 class="fw-bold mb-3 text-primary">Batch 2025</h6>
              <div id="editMembers2025Container" class="member-list-container">
                <!-- Members will be loaded here -->
              </div>
            </div>

            <div class="col-md-6">
              <h6 class="fw-bold mb-3 text-primary">Batch 2026</h6>
              <div id="editMembers2026Container" class="member-list-container">
                <!-- Members will be loaded here -->
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Task Assignment History Modal -->
  <div class="modal fade" id="historyModal" tabindex="-1" aria-labelledby="historyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header" style="background-color: transparent; border-bottom: 1px solid #dee2e6;">
          <h5 class="modal-title" id="historyModalLabel" style="color: #000000;">Task Assignment History</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div style="max-height:80vh; overflow-y:auto;">
            <table class="table table-bordered align-middle">
              <thead style="background-color: transparent;">
                <tr>
                  <th style="width: 120px; background-color: transparent; border: 1px solid #dee2e6; color: #000000;">Category</th>
                  <th style="width: 400px; background-color: transparent; border: 1px solid #dee2e6; color: #000000;">Period & Members</th>
                  <th style="width: 400px; background-color: transparent; border: 1px solid #dee2e6; color: #000000;">Previous Assignment</th>
                  <th style="width: 80px; background-color: transparent; border: 1px solid #dee2e6; color: #000000;">Status</th>
                </tr>
              </thead>
              <tbody>
                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                  <?php
                    // Get current and previous assignments for this category
                    $catAssignments = $assignmentHistory->where('category_id', $category->id)->sortByDesc('id')->values();
                    $current = $catAssignments->where('status', 'current')->first();
                    $previous = $catAssignments->where('status', 'previous')->sortByDesc('id')->first();
                  ?>
                  <tr>
                    <td class="fw-bold align-top"><?php echo e($category->name); ?></td>
                    <td>
                      <?php if($current): ?>
                        <div>
                          <span class="fw-semibold text-primary">Period: <?php echo e($current->start_date); ?> - <?php echo e($current->end_date); ?></span>
                        </div>
                        <?php
                          $members2025 = $current->assignmentMembers->where('student.batch', 2025)->values();
                          $members2026 = $current->assignmentMembers->where('student.batch', 2026)->values();
                          $maxRows = max($members2025->count(), $members2026->count());
                        ?>
                        <table class="table table-bordered table-sm mt-2">
                          <thead style="background-color: transparent;">
                            <tr>
                              <th class="fw-bold text-center" style="width: 50%; background-color: transparent; border: 1px solid #dee2e6; color: #000000;">Batch 2025</th>
                              <th class="fw-bold text-center" style="width: 50%; background-color: transparent; border: 1px solid #dee2e6; color: #000000;">Batch 2026</th>
                            </tr>
                          </thead>
                          <tbody>
                            <?php for($i = 0; $i < $maxRows; $i++): ?>
                              <tr>
                                <td class="<?php echo e(isset($members2025[$i]) && $members2025[$i]->is_coordinator ? 'coordinator-highlight-cell' : ''); ?>">
                                  <?php if(isset($members2025[$i])): ?>
                                    <div>
                                      <span class="<?php echo e($members2025[$i]->is_coordinator ? 'coordinator-name' : ''); ?>"><?php echo e($members2025[$i]->student->name); ?></span>
                                      <?php if($members2025[$i]->is_coordinator): ?>
                                        <span class="badge bg-warning text-dark ms-1" style="font-size: 0.7em;">COORDINATOR</span>
                                      <?php endif; ?>
                                    </div>
                                    <?php if($members2025[$i]->comments): ?>
                                      <span class="text-muted small"> (<?php echo e($members2025[$i]->comments); ?>)</span>
                                    <?php endif; ?>
                                  <?php endif; ?>
                                </td>
                                <td class="<?php echo e(isset($members2026[$i]) && $members2026[$i]->is_coordinator ? 'coordinator-highlight-cell' : ''); ?>">
                                  <?php if(isset($members2026[$i])): ?>
                                    <div>
                                      <span class="<?php echo e($members2026[$i]->is_coordinator ? 'coordinator-name' : ''); ?>"><?php echo e($members2026[$i]->student->name); ?></span>
                                      <?php if($members2026[$i]->is_coordinator): ?>
                                        <span class="badge bg-warning text-dark ms-1" style="font-size: 0.7em;">COORDINATOR</span>
                                      <?php endif; ?>
                                    </div>
                                    <?php if($members2026[$i]->comments): ?>
                                      <span class="text-muted small"> (<?php echo e($members2026[$i]->comments); ?>)</span>
                                    <?php endif; ?>
                                  <?php endif; ?>
                                </td>
                              </tr>
                            <?php endfor; ?>
                          </tbody>
                        </table>
                      <?php else: ?>
                        <span class="text-muted">No assignment</span>
                      <?php endif; ?>
                    </td>
                    <td>
                      <?php if($previous): ?>
                        <div>
                          <span class="fw-semibold text-primary">Period: <?php echo e($previous->start_date); ?> - <?php echo e($previous->end_date); ?></span>
                        </div>
                        <?php
                          $prevMembers2025 = $previous->assignmentMembers->where('student.batch', 2025)->values();
                          $prevMembers2026 = $previous->assignmentMembers->where('student.batch', 2026)->values();
                          $prevMaxRows = max($prevMembers2025->count(), $prevMembers2026->count());
                        ?>
                        <table class="table table-bordered table-sm mt-2">
                          <thead style="background-color: transparent;">
                            <tr>
                              <th class="fw-bold text-center" style="width: 50%; background-color: transparent; border: 1px solid #dee2e6; color: #000000;">Batch 2025</th>
                              <th class="fw-bold text-center" style="width: 50%; background-color: transparent; border: 1px solid #dee2e6; color: #000000;">Batch 2026</th>
                            </tr>
                          </thead>
                          <tbody>
                            <?php for($i = 0; $i < $prevMaxRows; $i++): ?>
                              <tr>
                                <td class="<?php echo e(isset($prevMembers2025[$i]) && $prevMembers2025[$i]->is_coordinator ? 'coordinator-highlight-cell' : ''); ?>">
                                  <?php if(isset($prevMembers2025[$i])): ?>
                                    <div>
                                      <span class="<?php echo e($prevMembers2025[$i]->is_coordinator ? 'coordinator-name' : ''); ?>"><?php echo e($prevMembers2025[$i]->student->name); ?></span>
                                      <?php if($prevMembers2025[$i]->is_coordinator): ?>
                                        <span class="badge bg-warning text-dark ms-1" style="font-size: 0.7em;">COORDINATOR</span>
                                      <?php endif; ?>
                                    </div>
                                    <?php if($prevMembers2025[$i]->comments): ?>
                                      <span class="text-muted small"> (<?php echo e($prevMembers2025[$i]->comments); ?>)</span>
                                    <?php endif; ?>
                                  <?php endif; ?>
                                </td>
                                <td class="<?php echo e(isset($prevMembers2026[$i]) && $prevMembers2026[$i]->is_coordinator ? 'coordinator-highlight-cell' : ''); ?>">
                                  <?php if(isset($prevMembers2026[$i])): ?>
                                    <div>
                                      <span class="<?php echo e($prevMembers2026[$i]->is_coordinator ? 'coordinator-name' : ''); ?>"><?php echo e($prevMembers2026[$i]->student->name); ?></span>
                                      <?php if($prevMembers2026[$i]->is_coordinator): ?>
                                        <span class="badge bg-warning text-dark ms-1" style="font-size: 0.7em;">COORDINATOR</span>
                                      <?php endif; ?>
                                    </div>
                                    <?php if($prevMembers2026[$i]->comments): ?>
                                      <span class="text-muted small"> (<?php echo e($prevMembers2026[$i]->comments); ?>)</span>
                                    <?php endif; ?>
                                  <?php endif; ?>
                                </td>
                              </tr>
                            <?php endfor; ?>
                          </tbody>
                        </table>
                      <?php else: ?>
                        <span class="text-muted">No previous assignment</span>
                      <?php endif; ?>
                    </td>
                    <td class="text-center align-top">
                      <?php if($loop->first): ?>
                        <span class="badge bg-success">Current</span>
                      <?php endif; ?>
                    </td>
                  </tr>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Task Checklist Modal -->
  <div class="modal fade" id="taskChecklistModal" tabindex="-1" aria-labelledby="taskChecklistModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen-xl-down" style="max-width: 98vw; width: 98vw; margin: 0.5rem auto;">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="taskChecklistModalLabel" style="font-size: 28px; font-weight: bold; color: #000000;">
            <i class="bi bi-list-check me-2"></i>Task Checklist
          </h5>
        </div>
        <div class="modal-body p-0">
          <!-- Navigation Controls -->
          <div class="d-flex justify-content-between align-items-center p-3 border-bottom">
            <button type="button" class="btn btn-outline-primary" onclick="changeTaskPage(-1)" id="prevPageBtn">
              <i class="bi bi-chevron-left"></i> Previous
            </button>
            <span class="fw-bold">Page <span id="currentPageNumber">1</span> of 10</span>
            <button type="button" class="btn btn-outline-primary" onclick="changeTaskPage(1)" id="nextPageBtn">
              Next <i class="bi bi-chevron-right"></i>
            </button>
          </div>

          <!-- Task Content Area -->
          <div id="taskPageContent" class="p-3">
            <!-- Content will be loaded dynamically -->
          </div>
        </div>

        <!-- Modal Footer with Action Buttons -->
        <div class="modal-footer d-flex justify-content-between align-items-center" style="padding: 15px 20px; border-top: 2px solid #dee2e6; background-color: #f8f9fa;">
          <div class="d-flex gap-2">
            <button type="button" class="btn btn-secondary" id="saveTaskStatusesBtn" onclick="saveAllTaskStatuses();" disabled style="border: none; padding: 10px 20px; border-radius: 5px; font-weight: 600;">
              <i class="bi bi-check-circle me-2"></i>Save All
            </button>
            <button type="button" class="btn btn-warning" id="editModeBtn" onclick="enableEditMode()" style="background-color: #ffc107; border: none; padding: 10px 20px; border-radius: 5px; font-weight: 600; color: #000;">
              <i class="bi bi-pencil me-2"></i>Edit
            </button>

          </div>
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" style="background-color: #6c757d; border: none; padding: 10px 20px; border-radius: 5px; font-weight: 600;">
            <i class="bi bi-x-circle me-2"></i>Close
          </button>
        </div>
      </div>
    </div>






  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

  <script>
    function editCategoryMembers(categoryId, categoryName) {
      // You can customize this function based on your needs
      // For now, it will redirect to an edit page or show an edit modal

      // Option 1: Redirect to edit page
      window.location.href = `/assignments/category/${categoryId}/edit`;

      // Option 2: Show alert (for testing)
      // alert(`Edit members for ${categoryName} (Category ID: ${categoryId})`);

      // Option 3: Open edit modal (you would need to create this modal)
      // $('#editCategoryModal').modal('show');
      // populateEditModal(categoryId, categoryName);
    }

    // Global variables for add/delete functionality
    let currentCategoryId = null;
    let selectedStudentsToAdd = [];
    let selectedMembersToDelete = [];
    let selectedStudentsToDeleteFromSystem = [];

    // Open Add Members Modal - SIMPLE TEST VERSION
    function openAddMembersModal(categoryId, categoryName) {
      alert('🚀 ADD MODAL TEST: Category ' + categoryId + ' - ' + categoryName);
      console.log('🚀 Opening Add Members Modal for category:', categoryId, categoryName);

      currentCategoryId = categoryId;
      selectedStudentsToAdd = [];

      // Set category name
      const titleElement = document.getElementById('addCategoryName');
      if (titleElement) {
        titleElement.textContent = categoryName;
        console.log('✅ Set modal title to:', categoryName);
      } else {
        console.error('❌ Title element not found!');
        alert('❌ ERROR: Title element not found!');
        return;
      }

      // SIMPLE TEST CONTENT - No AJAX, just static content
      const testContainer2025 = document.getElementById('availableStudents2025');
      const testContainer2026 = document.getElementById('availableStudents2026');

      if (testContainer2025) {
        testContainer2025.innerHTML = `
          <tr><td class="py-2 px-3 text-success">✅ TEST STUDENT 2025 - John Doe (Male)</td></tr>
          <tr><td class="py-2 px-3 text-success">✅ TEST STUDENT 2025 - Jane Smith (Female)</td></tr>
          <tr><td class="py-2 px-3 text-success">✅ TEST STUDENT 2025 - Mike Johnson (Male)</td></tr>
        `;
        console.log('✅ Added test content to 2025 container');
      } else {
        console.error('❌ 2025 container not found!');
        alert('❌ ERROR: 2025 container not found!');
      }

      if (testContainer2026) {
        testContainer2026.innerHTML = `
          <tr><td class="py-2 px-3 text-primary">✅ TEST STUDENT 2026 - Alice Brown (Female)</td></tr>
          <tr><td class="py-2 px-3 text-primary">✅ TEST STUDENT 2026 - Bob Wilson (Male)</td></tr>
          <tr><td class="py-2 px-3 text-primary">✅ TEST STUDENT 2026 - Carol Davis (Female)</td></tr>
        `;
        console.log('✅ Added test content to 2026 container');
      } else {
        console.error('❌ 2026 container not found!');
        alert('❌ ERROR: 2026 container not found!');
      }

      // Clear selected students display
      const selectedContainer = document.getElementById('selectedStudentsToAdd');
      if (selectedContainer) {
        selectedContainer.innerHTML = '<p class="text-success mb-0">✅ TEST: Click on students above to select them for adding to this category.</p>';
        console.log('🧹 Cleared selected students display');
      }

      // Show modal
      const modalElement = document.getElementById('addMembersModal');
      if (modalElement) {
        const addModal = new bootstrap.Modal(modalElement);
        addModal.show();
        console.log('✅ Modal shown successfully');
        alert('✅ SUCCESS: Modal should be visible now with test content!');
      } else {
        console.error('❌ Modal element not found!');
        alert('❌ CRITICAL ERROR: Modal element not found!');
      }
    }

    // Open Delete Members Modal - SIMPLE TEST VERSION
    function openDeleteMembersModal(categoryId, categoryName) {
      alert('🗑️ DELETE MODAL TEST: Category ' + categoryId + ' - ' + categoryName);
      console.log('🗑️ Opening Delete Members Modal for category:', categoryId, categoryName);

      currentCategoryId = categoryId;
      selectedMembersToDelete = [];
      selectedStudentsToDeleteFromSystem = [];

      // Set category name
      const titleElement = document.getElementById('deleteCategoryName');
      if (titleElement) {
        titleElement.textContent = categoryName;
        console.log('✅ Set delete modal title to:', categoryName);
      } else {
        console.error('❌ Delete title element not found!');
        alert('❌ ERROR: Delete title element not found!');
        return;
      }

      // SIMPLE TEST CONTENT - No AJAX, just static content
      const testCurrentMembers = document.getElementById('currentMembers2025');
      const testCurrentMembers2026 = document.getElementById('currentMembers2026');
      const testAllStudents2025 = document.getElementById('allStudents2025');
      const testAllStudents2026 = document.getElementById('allStudents2026');

      if (testCurrentMembers) {
        testCurrentMembers.innerHTML = `
          <tr><td class="py-2 px-3 text-warning">⚠️ CURRENT MEMBER 2025 - John Doe</td></tr>
          <tr><td class="py-2 px-3 text-warning">⚠️ CURRENT MEMBER 2025 - Jane Smith</td></tr>
        `;
      }
      if (testCurrentMembers2026) {
        testCurrentMembers2026.innerHTML = `
          <tr><td class="py-2 px-3 text-warning">⚠️ CURRENT MEMBER 2026 - Alice Brown</td></tr>
          <tr><td class="py-2 px-3 text-warning">⚠️ CURRENT MEMBER 2026 - Bob Wilson</td></tr>
        `;
      }
      if (testAllStudents2025) {
        testAllStudents2025.innerHTML = `
          <tr><td class="py-2 px-3 text-danger">🗑️ ALL STUDENT 2025 - Mike Johnson</td></tr>
          <tr><td class="py-2 px-3 text-danger">🗑️ ALL STUDENT 2025 - Carol Davis</td></tr>
        `;
      }
      if (testAllStudents2026) {
        testAllStudents2026.innerHTML = `
          <tr><td class="py-2 px-3 text-danger">🗑️ ALL STUDENT 2026 - David Lee</td></tr>
          <tr><td class="py-2 px-3 text-danger">🗑️ ALL STUDENT 2026 - Emma White</td></tr>
        `;
      }

      // Clear selected displays
      const selectedMembersContainer = document.getElementById('selectedMembersToDelete');
      const selectedStudentsContainer = document.getElementById('selectedStudentsToDeleteFromSystem');

      if (selectedMembersContainer) {
        selectedMembersContainer.innerHTML = '<p class="text-warning mb-0">✅ TEST: Click on members above to select them for removal from this category.</p>';
      }
      if (selectedStudentsContainer) {
        selectedStudentsContainer.innerHTML = '<p class="text-danger mb-0">✅ TEST: Click on students above to select them for permanent deletion.</p>';
      }

      // Show modal
      const modalElement = document.getElementById('deleteMembersModal');
      if (modalElement) {
        const deleteModal = new bootstrap.Modal(modalElement);
        deleteModal.show();
        console.log('✅ Delete modal shown successfully');
        alert('✅ SUCCESS: Delete modal should be visible now with test content!');
      } else {
        console.error('❌ Delete modal element not found!');
        alert('❌ CRITICAL ERROR: Delete modal element not found!');
      }
    }

    // Load available students for adding
    function loadAvailableStudents(categoryId) {
      console.log('🔍 Loading available students for category:', categoryId);

      fetch(`/assignments/category/${categoryId}/available-students`)
        .then(response => {
          console.log('📡 Response status:', response.status);
          return response.json();
        })
        .then(data => {
          console.log('📊 Received data:', data);
          if (data.success) {
            console.log('✅ Success! Students 2025:', data.students2025?.length, 'Students 2026:', data.students2026?.length);
            populateAvailableStudents(data.students2025, data.students2026);
          } else {
            console.error('❌ Error loading available students:', data.message);
            // Show error in modal
            document.getElementById('availableStudents2025').innerHTML = '<tr><td class="text-danger">Error loading students: ' + data.message + '</td></tr>';
            document.getElementById('availableStudents2026').innerHTML = '<tr><td class="text-danger">Error loading students: ' + data.message + '</td></tr>';
          }
        })
        .catch(error => {
          console.error('❌ Network error:', error);
          // Show error in modal
          document.getElementById('availableStudents2025').innerHTML = '<tr><td class="text-danger">Network error loading students</td></tr>';
          document.getElementById('availableStudents2026').innerHTML = '<tr><td class="text-danger">Network error loading students</td></tr>';
        });
    }

    // Populate available students in modal
    function populateAvailableStudents(students2025, students2026) {
      console.log('🎯 Populating available students...');
      console.log('📊 Students 2025:', students2025);
      console.log('📊 Students 2026:', students2026);

      const container2025 = document.getElementById('availableStudents2025');
      const container2026 = document.getElementById('availableStudents2026');

      if (!container2025 || !container2026) {
        console.error('❌ Modal containers not found!');
        return;
      }

      // Clear containers
      container2025.innerHTML = '';
      container2026.innerHTML = '';

      // Populate Batch 2025
      console.log('📝 Populating 2025 students...');
      if (students2025 && Array.isArray(students2025) && students2025.length > 0) {
        console.log('✅ Found', students2025.length, 'students for 2025');
        students2025.forEach(student => {
          const row = document.createElement('tr');
          row.className = 'student-row-add';
          row.style.cursor = 'pointer';
          row.dataset.studentId = student.id;
          row.dataset.studentName = student.name;
          row.dataset.studentBatch = student.batch;
          row.innerHTML = `<td class="py-2 px-3">${student.name} (${student.gender})</td>`;
          row.onclick = () => selectStudentToAdd(student);
          container2025.appendChild(row);
        });
        console.log('✅ Populated 2025 container with', students2025.length, 'students');
      } else {
        console.log('⚠️ No students found for 2025');
        container2025.innerHTML = '<tr><td class="text-muted text-center py-3">No available students in Batch 2025</td></tr>';
      }

      // Populate Batch 2026
      console.log('📝 Populating 2026 students...');
      if (students2026 && Array.isArray(students2026) && students2026.length > 0) {
        console.log('✅ Found', students2026.length, 'students for 2026');
        students2026.forEach(student => {
          const row = document.createElement('tr');
          row.className = 'student-row-add';
          row.style.cursor = 'pointer';
          row.dataset.studentId = student.id;
          row.dataset.studentName = student.name;
          row.dataset.studentBatch = student.batch;
          row.innerHTML = `<td class="py-2 px-3">${student.name} (${student.gender})</td>`;
          row.onclick = () => selectStudentToAdd(student);
          container2026.appendChild(row);
        });
        console.log('✅ Populated 2026 container with', students2026.length, 'students');
      } else {
        console.log('⚠️ No students found for 2026');
        container2026.innerHTML = '<tr><td class="text-muted text-center py-3">No available students in Batch 2026</td></tr>';
      }
    }

    // Select student to add
    function selectStudentToAdd(student) {
      // Check if already selected
      if (selectedStudentsToAdd.find(s => s.id === student.id)) {
        return;
      }

      selectedStudentsToAdd.push(student);
      updateSelectedStudentsToAddDisplay();

      // Highlight the row
      const rows = document.querySelectorAll(`[data-student-id="${student.id}"]`);
      rows.forEach(row => {
        row.classList.add('table-success');
        row.style.opacity = '0.6';
      });
    }

    // Update selected students to add display
    function updateSelectedStudentsToAddDisplay() {
      const container = document.getElementById('selectedStudentsToAdd');

      if (selectedStudentsToAdd.length === 0) {
        container.innerHTML = '<p class="text-muted mb-0">Click on students above to select them for adding to this category.</p>';
        return;
      }

      let html = '';
      selectedStudentsToAdd.forEach(student => {
        html += `
          <span class="badge bg-primary me-2 mb-2" style="font-size: 14px; padding: 8px 12px;">
            ${student.name} (Batch ${student.batch})
            <button type="button" class="btn-close btn-close-white ms-2" style="font-size: 10px;" onclick="removeStudentToAdd(${student.id})"></button>
          </span>
        `;
      });

      container.innerHTML = html;
    }

    // Remove student from add selection
    function removeStudentToAdd(studentId) {
      selectedStudentsToAdd = selectedStudentsToAdd.filter(s => s.id !== studentId);
      updateSelectedStudentsToAddDisplay();

      // Remove highlight from row
      const rows = document.querySelectorAll(`[data-student-id="${studentId}"]`);
      rows.forEach(row => {
        row.classList.remove('table-success');
        row.style.opacity = '1';
      });
    }

    // Add new student directly to category
    function addNewStudentToCategory(batch) {
      const nameInput = document.getElementById(`newStudent${batch}Name`);
      const genderSelect = document.getElementById(`newStudent${batch}Gender`);

      const name = nameInput.value.trim();
      const gender = genderSelect.value;

      if (!name) {
        alert('Please enter a student name');
        nameInput.focus();
        return;
      }

      // Create and add student to system, then assign to category
      fetch('/students/quick-add-to-category', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
          name: name,
          gender: gender,
          batch: batch,
          category_id: currentCategoryId
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          showNotification('success', data.message);
          // Clear input
          nameInput.value = '';
          // Refresh available students
          loadAvailableStudents(currentCategoryId);
        } else {
          alert('Error adding student: ' + data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Error adding student');
      });
    }

    // Load current members for deleting
    function loadCurrentMembers(categoryId) {
      fetch(`/assignments/category/${categoryId}/current-members`)
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            populateCurrentMembers(data.members2025, data.members2026);
          } else {
            console.error('Error loading current members:', data.message);
          }
        })
        .catch(error => {
          console.error('Error:', error);
        });
    }

    // Populate current members in delete modal
    function populateCurrentMembers(members2025, members2026) {
      const container2025 = document.getElementById('currentMembers2025');
      const container2026 = document.getElementById('currentMembers2026');

      // Clear containers
      container2025.innerHTML = '';
      container2026.innerHTML = '';

      // Populate Batch 2025
      members2025.forEach(member => {
        const div = document.createElement('div');
        div.className = 'member-item-delete p-2 border rounded mb-2';
        div.style.cursor = 'pointer';
        div.dataset.memberId = member.id;
        div.dataset.memberName = member.student.name;
        div.innerHTML = `
          <div class="${member.is_coordinator ? 'coordinator-name' : ''}">${member.student.name}</div>
          ${member.comments ? `<small class="text-muted">(${member.comments})</small>` : ''}
        `;
        div.onclick = () => selectMemberToDelete(member);
        container2025.appendChild(div);
      });

      // Populate Batch 2026
      members2026.forEach(member => {
        const div = document.createElement('div');
        div.className = 'member-item-delete p-2 border rounded mb-2';
        div.style.cursor = 'pointer';
        div.dataset.memberId = member.id;
        div.dataset.memberName = member.student.name;
        div.innerHTML = `
          <div class="${member.is_coordinator ? 'coordinator-name' : ''}">${member.student.name}</div>
          ${member.comments ? `<small class="text-muted">(${member.comments})</small>` : ''}
        `;
        div.onclick = () => selectMemberToDelete(member);
        container2026.appendChild(div);
      });
    }

    // Select member to delete
    function selectMemberToDelete(member) {
      // Check if already selected
      if (selectedMembersToDelete.find(m => m.id === member.id)) {
        return;
      }

      selectedMembersToDelete.push(member);
      updateSelectedMembersToDeleteDisplay();

      // Highlight the member
      const items = document.querySelectorAll(`[data-member-id="${member.id}"]`);
      items.forEach(item => {
        item.classList.add('bg-danger', 'text-white');
      });
    }

    // Update selected members to delete display
    function updateSelectedMembersToDeleteDisplay() {
      const container = document.getElementById('selectedMembersToDelete');

      if (selectedMembersToDelete.length === 0) {
        container.innerHTML = '<p class="text-muted mb-0">Click on members above to select them for removal from this category.</p>';
        return;
      }

      let html = '';
      selectedMembersToDelete.forEach(member => {
        html += `
          <span class="badge bg-danger me-2 mb-2" style="font-size: 14px; padding: 8px 12px;">
            ${member.student.name}
            <button type="button" class="btn-close btn-close-white ms-2" style="font-size: 10px;" onclick="removeMemberToDelete(${member.id})"></button>
          </span>
        `;
      });

      container.innerHTML = html;
    }

    // Remove member from delete selection
    function removeMemberToDelete(memberId) {
      selectedMembersToDelete = selectedMembersToDelete.filter(m => m.id !== memberId);
      updateSelectedMembersToDeleteDisplay();

      // Remove highlight from member
      const items = document.querySelectorAll(`[data-member-id="${memberId}"]`);
      items.forEach(item => {
        item.classList.remove('bg-danger', 'text-white');
      });
    }

    // Load all students for system deletion
    function loadAllStudentsForSystemDeletion() {
      fetch('/students/all-for-deletion')
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            populateStudentsForSystemDeletion(data.students2025, data.students2026);
          } else {
            console.error('Error loading students for deletion:', data.message);
          }
        })
        .catch(error => {
          console.error('Error:', error);
        });
    }

    // Populate students for system deletion
    function populateStudentsForSystemDeletion(students2025, students2026) {
      const container2025 = document.getElementById('systemDeleteMembers2025');
      const container2026 = document.getElementById('systemDeleteMembers2026');

      // Clear containers
      container2025.innerHTML = '';
      container2026.innerHTML = '';

      // Populate Batch 2025
      students2025.forEach(student => {
        const div = document.createElement('div');
        div.className = 'student-item-system-delete p-2 border rounded mb-2';
        div.style.cursor = 'pointer';
        div.dataset.studentId = student.id;
        div.dataset.studentName = student.name;
        div.innerHTML = `
          <div>${student.name} (${student.gender})</div>
        `;
        div.onclick = () => selectStudentForSystemDeletion(student);
        container2025.appendChild(div);
      });

      // Populate Batch 2026
      students2026.forEach(student => {
        const div = document.createElement('div');
        div.className = 'student-item-system-delete p-2 border rounded mb-2';
        div.style.cursor = 'pointer';
        div.dataset.studentId = student.id;
        div.dataset.studentName = student.name;
        div.innerHTML = `
          <div>${student.name} (${student.gender})</div>
        `;
        div.onclick = () => selectStudentForSystemDeletion(student);
        container2026.appendChild(div);
      });
    }

    // Select student for system deletion
    function selectStudentForSystemDeletion(student) {
      // Check if already selected
      if (selectedStudentsToDeleteFromSystem.find(s => s.id === student.id)) {
        return;
      }

      selectedStudentsToDeleteFromSystem.push(student);
      updateSelectedStudentsForSystemDeletionDisplay();

      // Highlight the student
      const items = document.querySelectorAll(`[data-student-id="${student.id}"]`);
      items.forEach(item => {
        if (item.classList.contains('student-item-system-delete')) {
          item.classList.add('bg-danger', 'text-white');
        }
      });
    }

    // Update selected students for system deletion display
    function updateSelectedStudentsForSystemDeletionDisplay() {
      const container = document.getElementById('selectedStudentsToDeleteFromSystem');

      if (selectedStudentsToDeleteFromSystem.length === 0) {
        container.innerHTML = '<p class="text-muted mb-0">Click on students above to select them for permanent deletion.</p>';
        return;
      }

      let html = '';
      selectedStudentsToDeleteFromSystem.forEach(student => {
        html += `
          <span class="badge bg-danger me-2 mb-2" style="font-size: 14px; padding: 8px 12px;">
            ${student.name}
            <button type="button" class="btn-close btn-close-white ms-2" style="font-size: 10px;" onclick="removeStudentFromSystemDeletion(${student.id})"></button>
          </span>
        `;
      });

      container.innerHTML = html;
    }

    // Remove student from system deletion selection
    function removeStudentFromSystemDeletion(studentId) {
      selectedStudentsToDeleteFromSystem = selectedStudentsToDeleteFromSystem.filter(s => s.id !== studentId);
      updateSelectedStudentsForSystemDeletionDisplay();

      // Remove highlight from student
      const items = document.querySelectorAll(`[data-student-id="${studentId}"]`);
      items.forEach(item => {
        if (item.classList.contains('student-item-system-delete')) {
          item.classList.remove('bg-danger', 'text-white');
        }
      });
    }

    // Open Edit Members Modal - SIMPLE TEST VERSION
    function openEditMembersModal(categoryId, categoryName) {
      alert('✏️ EDIT MODAL TEST: Category ' + categoryId + ' - ' + categoryName);
      console.log('✏️ Opening Edit Members Modal for category:', categoryId, categoryName);

      // Close any existing modal first
      const existingModal = bootstrap.Modal.getInstance(document.getElementById('studentAssignModal' + categoryId));
      if (existingModal) {
        existingModal.hide();
      }

      // Set category name in modal title
      const titleElement = document.getElementById('editCategoryName');
      if (titleElement) {
        titleElement.textContent = categoryName;
        console.log('✅ Set edit modal title to:', categoryName);
      } else {
        console.error('❌ Edit title element not found!');
        alert('❌ ERROR: Edit title element not found!');
        return;
      }

      // SIMPLE TEST CONTENT - No AJAX, just static content
      const editContainer2025 = document.getElementById('editMembers2025Container');
      const editContainer2026 = document.getElementById('editMembers2026Container');

      if (editContainer2025) {
        editContainer2025.innerHTML = `
          <div class="text-success p-3">
            <h6>✅ TEST EDIT MEMBERS 2025</h6>
            <p>✏️ John Doe - Currently assigned</p>
            <p>✏️ Jane Smith - Currently assigned</p>
            <p>✏️ Mike Johnson - Available to add</p>
          </div>
        `;
        console.log('✅ Added test content to edit 2025 container');
      } else {
        console.error('❌ Edit 2025 container not found!');
        alert('❌ ERROR: Edit 2025 container not found!');
      }

      if (editContainer2026) {
        editContainer2026.innerHTML = `
          <div class="text-primary p-3">
            <h6>✅ TEST EDIT MEMBERS 2026</h6>
            <p>✏️ Alice Brown - Currently assigned</p>
            <p>✏️ Bob Wilson - Currently assigned</p>
            <p>✏️ Carol Davis - Available to add</p>
          </div>
        `;
        console.log('✅ Added test content to edit 2026 container');
      } else {
        console.error('❌ Edit 2026 container not found!');
        alert('❌ ERROR: Edit 2026 container not found!');
      }

      // Show edit modal immediately
      const modalElement = document.getElementById('editMembersModal');
      if (modalElement) {
        const editModal = new bootstrap.Modal(modalElement);
        editModal.show();
        console.log('✅ Edit modal shown successfully');
        alert('✅ SUCCESS: Edit modal should be visible now with test content!');
      } else {
        console.error('❌ Edit modal element not found!');
        alert('❌ CRITICAL ERROR: Edit modal element not found!');
      }

      // Fetch members data and populate edit form directly
      fetch(`/assignments/category/${categoryId}/members`)
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            populateEditMembers(data.members2025, data.members2026);
          } else {
            showNotification('Error loading members data', 'error');
          }
        })
        .catch(error => {
          console.error('Error:', error);
          showNotification('Error loading members data', 'error');
        });
    }

    // Populate edit members containers
    function populateEditMembers(members2025, members2026) {
      const container2025 = document.getElementById('editMembers2025Container');
      const container2026 = document.getElementById('editMembers2026Container');

      // Clear containers
      container2025.innerHTML = '';
      container2026.innerHTML = '';

      // Populate Batch 2025
      members2025.forEach(member => {
        const memberHtml = createEditMemberRow(member);
        container2025.appendChild(memberHtml);
      });

      // Populate Batch 2026
      members2026.forEach(member => {
        const memberHtml = createEditMemberRow(member);
        container2026.appendChild(memberHtml);
      });
    }

    // Create edit member row
    function createEditMemberRow(member) {
      const div = document.createElement('div');
      div.className = `member-row ${member.is_coordinator ? 'coordinator-highlight-edit' : ''}`;
      div.id = `member-row-${member.id}`;

      div.innerHTML = `
        <div class="member-info">
          <div class="member-name ${member.is_coordinator ? 'coordinator-name' : ''}">
            ${member.student.name}
          </div>
          ${member.comments ? `<div class="member-comment" id="comment-display-${member.id}">(${member.comments})</div>` : ''}
          <div class="comment-edit-area" id="comment-edit-${member.id}" style="display: none;">
            <input type="text" class="comment-input" id="comment-input-${member.id}"
                   value="${member.comments || ''}" placeholder="Add comment (e.g., Not available - headache)">
          </div>
        </div>
        <div class="member-actions">
          <button class="btn-edit-small" onclick="editMember(${member.id})" id="edit-btn-${member.id}">Edit</button>
          <button class="btn-save-small" onclick="saveMember(${member.id})" id="save-btn-${member.id}" style="display: none;">Save</button>
        </div>
      `;

      return div;
    }

    // Edit member function
    function editMember(memberId) {
      // Hide comment display and edit button
      const commentDisplay = document.getElementById(`comment-display-${memberId}`);
      const editBtn = document.getElementById(`edit-btn-${memberId}`);
      const saveBtn = document.getElementById(`save-btn-${memberId}`);
      const commentEdit = document.getElementById(`comment-edit-${memberId}`);

      if (commentDisplay) commentDisplay.style.display = 'none';
      editBtn.style.display = 'none';
      saveBtn.style.display = 'inline-block';
      commentEdit.style.display = 'block';

      // Focus on input
      const input = document.getElementById(`comment-input-${memberId}`);
      input.focus();
    }

    // Edit student name
    function editStudentName(studentId) {
      const nameDisplay = document.getElementById(`name-display-${studentId}`);
      const nameEdit = document.getElementById(`name-edit-${studentId}`);
      const editBtn = document.getElementById(`edit-name-btn-${studentId}`);
      const saveBtn = document.getElementById(`save-name-btn-${studentId}`);
      const cancelBtn = document.getElementById(`cancel-name-btn-${studentId}`);

      nameDisplay.classList.add('d-none');
      nameEdit.classList.remove('d-none');
      editBtn.classList.add('d-none');
      saveBtn.classList.remove('d-none');
      cancelBtn.classList.remove('d-none');

      nameEdit.focus();
      nameEdit.select();
    }

    // Save student name
    function saveStudentName(studentId) {
      const nameEdit = document.getElementById(`name-edit-${studentId}`);
      const newName = nameEdit.value.trim();

      if (!newName) {
        alert('Name cannot be empty');
        return;
      }

      fetch(`/students/${studentId}/update-name`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ name: newName })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          const nameDisplay = document.getElementById(`name-display-${studentId}`);
          nameDisplay.textContent = newName;
          cancelEditName(studentId);

          // Show success message
          showNotification('success', data.message);
        } else {
          alert('Error updating name: ' + data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Error updating student name');
      });
    }

    // Cancel edit name
    function cancelEditName(studentId) {
      const nameDisplay = document.getElementById(`name-display-${studentId}`);
      const nameEdit = document.getElementById(`name-edit-${studentId}`);
      const editBtn = document.getElementById(`edit-name-btn-${studentId}`);
      const saveBtn = document.getElementById(`save-name-btn-${studentId}`);
      const cancelBtn = document.getElementById(`cancel-name-btn-${studentId}`);

      nameDisplay.classList.remove('d-none');
      nameEdit.classList.add('d-none');
      editBtn.classList.remove('d-none');
      saveBtn.classList.add('d-none');
      cancelBtn.classList.add('d-none');

      // Reset input value to original
      nameEdit.value = nameDisplay.textContent;
    }

    // Delete student
    function deleteStudent(studentId, studentName) {
      if (!confirm(`Are you sure you want to delete "${studentName}"? This action cannot be undone and will remove the student from all assignments.`)) {
        return;
      }

      fetch(`/students/${studentId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          showNotification('success', data.message);
          // Refresh the page to update the view
          setTimeout(() => {
            location.reload();
          }, 1500);
        } else {
          alert('Error deleting student: ' + data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Error deleting student');
      });
    }

    // Add new student
    function addNewStudent(batch) {
      const nameInput = document.getElementById(`newStudent${batch}Name`);
      const genderSelect = document.getElementById(`newStudent${batch}Gender`);

      const name = nameInput.value.trim();
      const gender = genderSelect.value;

      if (!name) {
        alert('Please enter a student name');
        nameInput.focus();
        return;
      }

      fetch('/students/quick-add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
          name: name,
          gender: gender,
          batch: batch
        })
      })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          showNotification('success', data.message);
          nameInput.value = '';
          // Refresh the page to update the view
          setTimeout(() => {
            location.reload();
          }, 1500);
        } else {
          alert('Error adding student: ' + data.message);
        }
      })
      .catch(error => {
        console.error('Error:', error);
        alert('Error adding student');
      });
    }

    // Show notification
    function showNotification(type, message) {
      const notification = document.createElement('div');
      notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
      notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
      notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      `;

      document.body.appendChild(notification);

      // Auto remove after 3 seconds
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove();
        }
      }, 3000);
    }

    // Save member function
    function saveMember(memberId) {
      const input = document.getElementById(`comment-input-${memberId}`);
      const comments = input.value.trim();

      // Show loading state
      const saveBtn = document.getElementById(`save-btn-${memberId}`);
      const originalText = saveBtn.textContent;
      saveBtn.textContent = 'Saving...';
      saveBtn.disabled = true;

      // Get CSRF token
      const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

      // Create form data
      const formData = new FormData();
      formData.append('member_id', memberId);
      formData.append('comments', comments);
      formData.append('_token', csrfToken);

      // Send AJAX request
      fetch('/assignments/update-member-comment', {
        method: 'POST',
        headers: {
          'X-CSRF-TOKEN': csrfToken,
          'Accept': 'application/json'
        },
        body: formData
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        if (data.success) {
          // Update display
          const commentDisplay = document.getElementById(`comment-display-${memberId}`);
          const editBtn = document.getElementById(`edit-btn-${memberId}`);
          const commentEdit = document.getElementById(`comment-edit-${memberId}`);

          if (comments) {
            if (commentDisplay) {
              commentDisplay.textContent = `(${comments})`;
              commentDisplay.style.display = 'block';
            } else {
              // Create comment display if it doesn't exist
              const memberInfo = document.querySelector(`#member-row-${memberId} .member-info`);
              const newCommentDisplay = document.createElement('div');
              newCommentDisplay.className = 'member-comment';
              newCommentDisplay.id = `comment-display-${memberId}`;
              newCommentDisplay.textContent = `(${comments})`;
              memberInfo.appendChild(newCommentDisplay);
            }
          } else {
            if (commentDisplay) commentDisplay.style.display = 'none';
          }

          // Reset buttons
          editBtn.style.display = 'inline-block';
          saveBtn.style.display = 'none';
          commentEdit.style.display = 'none';

          // Show success message
          showNotification('Comment saved successfully!', 'success');

          // Refresh the page after 1 second to show updated comments in View History
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } else {
          showNotification(data.message || 'Error saving comment. Please try again.', 'error');
        }
      })
      .catch(error => {
        console.error('Error:', error);
        showNotification('Error saving comment. Please try again.', 'error');
      })
      .finally(() => {
        // Reset button state
        saveBtn.textContent = originalText;
        saveBtn.disabled = false;
      });
    }

    // Simple notification function
    function showNotification(message, type) {
      const notification = document.createElement('div');
      notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} position-fixed`;
      notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
      notification.textContent = message;

      document.body.appendChild(notification);

      setTimeout(() => {
        notification.remove();
      }, 3000);
    }

    // Optional: Add some visual feedback when hovering over edit button
    document.addEventListener('DOMContentLoaded', function() {
      // Set today's date in all date inputs immediately when page loads
      setCurrentDateInInputs();

      // Load CSS for status buttons
      loadStatusButtonCSS();

      // Load saved statuses from localStorage
      setTimeout(() => {
        loadSavedStatuses();
      }, 500); // Small delay to ensure DOM is fully ready

      // Make sure the functions are globally available
      window.handleStatusButtonClickDirect = handleStatusButtonClickDirect;
      window.toggleButton = toggleButton;

      // Add global event delegation for status buttons
      document.addEventListener('click', function(e) {
        if (e.target.classList.contains('status-btn')) {
          console.log('🎯 Global click detected on status button!');
          const button = e.target;
          const taskId = button.dataset.task;
          const week = button.dataset.week;
          const day = button.dataset.day;
          const status = button.dataset.status;

          if (taskId && week && day && status) {
            console.log('🎯 Calling handleStatusButtonClickDirect from global handler');
            handleStatusButtonClickDirect(taskId, week, day, status, button);
          }
        }
      });

      const editButtons = document.querySelectorAll('.btn-edit-blue');
      editButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
          this.style.transform = 'translateY(-2px)';
        });
        button.addEventListener('mouseleave', function() {
          this.style.transform = 'translateY(0)';
        });
      });
    });

    // Task Checklist Modal Functions - Use more specific delegation
    document.addEventListener('click', function(e) {
        console.log('🖱️ Document click detected:', e.target);

        // Check if clicked element or its parent has status-btn class
        let target = e.target;
        if (!target.classList.contains('status-btn')) {
            target = target.closest('.status-btn');
        }

        console.log('🔍 Target after closest:', target);

        // Make sure we're inside the task modal
        const modal = target ? target.closest('#taskChecklistModal') : null;

        if (target && target.classList.contains('status-btn')) {
            console.log('✅ Status button clicked!', target);
            const taskId = target.dataset.task;
            const week = target.dataset.week;
            const day = target.dataset.day;
            const status = target.dataset.status;

            console.log('📊 Button data:', { taskId, week, day, status });

            // Find other button in same cell and deactivate it
            const otherButtons = target.parentElement.querySelectorAll('.status-btn');
            otherButtons.forEach(btn => {
                if (btn !== target) {
                    btn.classList.remove('active');
                }
            });

            // Toggle current button
            const wasActive = target.classList.contains('active');
            if (wasActive) {
                target.classList.remove('active');
            } else {
                target.classList.add('active');
            }
            const isNowActive = target.classList.contains('active');

            // Track pending changes for manual save
            const changeKey = `${taskId}_${week}_${day}`;
            const statusToSave = isNowActive ? status : null;

            // Initialize pendingTaskStatuses if it doesn't exist
            if (!window.pendingTaskStatuses) {
                window.pendingTaskStatuses = {};
            }

            if (statusToSave) {
                window.pendingTaskStatuses[changeKey] = {
                    task_id: taskId,
                    week: week,
                    day: day,
                    status: statusToSave
                };
            } else {
                // If deactivated, mark as cleared
                window.pendingTaskStatuses[changeKey] = {
                    task_id: taskId,
                    week: week,
                    day: day,
                    status: null
                };
            }

            // Debug logging
            console.log('📝 Pending statuses:', window.pendingTaskStatuses);
            console.log('📝 Pending count:', Object.keys(window.pendingTaskStatuses).length);

            // Update save button state
            updateSaveButtonState();

            console.log(`Task: ${taskId}, Week: ${week}, Day: ${day}, Status: ${statusToSave}, Pending save...`);
        }
    });

    // Update week dates function
    function updateWeekDates() {
        const week1Date = document.getElementById('week1_date').value;
        const week2Date = document.getElementById('week2_date').value;

        // You can add AJAX call here to save dates if needed
        console.log('Week 1 Date:', week1Date);
        console.log('Week 2 Date:', week2Date);
    }

    // Set current date in all date inputs
    function setCurrentDateInInputs() {
        const today = new Date();
        const currentDate = today.toISOString().split('T')[0]; // Format: YYYY-MM-DD

        // Set current date for all page date inputs
        const dateInputs = [
            'week1_date', 'week1_date_p2', 'week1_date_p3', 'week1_date_p4', 'week1_date_p5',
            'week1_date_p6', 'week1_date_p7', 'week1_date_p8', 'week1_date_p9', 'week1_date_p10'
        ];

        dateInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.value = currentDate;
            }
        });
    }

    // Global variable to track pending task statuses
    window.pendingTaskStatuses = {};

    // Save all task statuses function
    function saveAllTaskStatuses() {
        const saveButton = document.getElementById('saveTaskStatusesBtn');

        // Check if there are any pending changes
        if (Object.keys(window.pendingTaskStatuses).length === 0) {
            showNotification('No changes to save', 'info');
            return;
        }

        // Get the current date from the active page's date input
        const currentPage = currentTaskPage || 1;
        const dateInputId = currentPage === 1 ? 'week1_date' : `week1_date_p${currentPage}`;
        const dateInput = document.getElementById(dateInputId);
        let currentDate = dateInput ? dateInput.value : null;

        // Fallback: try other common date input IDs
        if (!currentDate) {
            const fallbackIds = ['week1_date', 'week1_date_p1', 'week1_date_p2', 'week1_date_p3'];
            for (const id of fallbackIds) {
                const fallbackInput = document.getElementById(id);
                if (fallbackInput && fallbackInput.value) {
                    currentDate = fallbackInput.value;
                    break;
                }
            }
        }

        // Final fallback: use today's date
        if (!currentDate) {
            currentDate = new Date().toISOString().split('T')[0];
        }

        console.log('📅 Using date:', currentDate, 'from input:', dateInputId);

        // Disable save button and show loading
        saveButton.disabled = true;
        saveButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Saving...';

        // Convert pendingTaskStatuses object to array format expected by controller
        const statusesArray = Object.values(window.pendingTaskStatuses);

        console.log('📤 Sending data to controller:');
        console.log('   - Date:', currentDate);
        console.log('   - Statuses array:', statusesArray);
        console.log('   - Array length:', statusesArray.length);

        // Save all to localStorage first (immediate persistence)
        statusesArray.forEach(statusData => {
            const localStorageKey = `task_status_${statusData.task_id}_${statusData.week}_${statusData.day}_${currentDate}`;
            localStorage.setItem(localStorageKey, statusData.status || '');
            console.log('💾 Saved to localStorage:', localStorageKey, '=', statusData.status);
        });

        // Try to send AJAX request to save all statuses to server
        fetch('/task-checklist/save-all-statuses', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                statuses: statusesArray,
                date: currentDate
            })
        })
        .then(response => {
            console.log('📥 Response status:', response.status);

            // Check if response is actually JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                console.warn('⚠️ Server returned non-JSON response, using localStorage only');
                return null;
            }

            if (!response.ok) {
                console.warn(`⚠️ Server error ${response.status}, using localStorage only`);
                return null;
            }
            return response.json();
        })
        .then(data => {
            if (data && data.success) {
                console.log('📥 Response data:', data);
                // Show enhanced success message
                const savedCount = data.saved_count || statusesArray.length;
                showSuccessMessage(`✅ Saved successfully! ${savedCount} task status(es) have been saved.`);

                // Mark localStorage entries as confirmed
                statusesArray.forEach(statusData => {
                    const localStorageKey = `task_status_${statusData.task_id}_${statusData.week}_${statusData.day}_${currentDate}`;
                    localStorage.setItem(localStorageKey + '_confirmed', 'true');
                });

                console.log('✅ Save completed successfully');
            } else if (data) {
                console.warn('⚠️ Server save failed, but localStorage save succeeded:', data.message);
                showSuccessMessage(`✅ Saved locally! ${statusesArray.length} task status(es) have been saved to your browser.`);
            } else {
                // Server didn't respond with JSON, but localStorage save worked
                showSuccessMessage(`✅ Saved locally! ${statusesArray.length} task status(es) have been saved to your browser.`);
            }

            // Clear pending statuses
            window.pendingTaskStatuses = {};

            // Keep button enabled but update state
            updateSaveButtonState();

            // Reload saved statuses to ensure UI is in sync
            setTimeout(() => {
                loadSavedTaskStatuses();
            }, 500);
        })
        .catch(error => {
            console.warn('⚠️ Server save failed, but localStorage save succeeded:', error.message);
            showSuccessMessage(`✅ Saved locally! ${statusesArray.length} task status(es) have been saved to your browser.`);

            // Clear pending statuses
            window.pendingTaskStatuses = {};

            // Keep button enabled but update state
            updateSaveButtonState();

            // Reload saved statuses to ensure UI is in sync
            setTimeout(() => {
                loadSavedTaskStatuses();
            }, 500);
        })
        .finally(() => {
            // Re-enable save button if there was an error
            if (saveButton.disabled && saveButton.innerHTML.includes('Saving...')) {
                saveButton.disabled = false;
                saveButton.classList.remove('btn-secondary');
                saveButton.classList.add('btn-success');
                saveButton.innerHTML = '<i class="bi bi-check-circle me-2"></i>Save All';
            }
        });
    }

    // Enable edit mode function
    function enableEditMode() {
        const editButton = document.getElementById('editModeBtn');

        // Toggle edit mode
        if (editButton.innerHTML.includes('Edit')) {
            // Enable edit mode
            editButton.innerHTML = '<i class="bi bi-lock me-2"></i>Lock';
            editButton.classList.remove('btn-warning');
            editButton.classList.add('btn-danger');

            // Enable all status buttons
            document.querySelectorAll('.status-btn').forEach(btn => {
                btn.disabled = false;
                btn.style.cursor = 'pointer';
            });

            showNotification('Edit mode enabled. You can now modify task statuses.', 'info');
        } else {
            // Disable edit mode
            editButton.innerHTML = '<i class="bi bi-pencil me-2"></i>Edit';
            editButton.classList.remove('btn-danger');
            editButton.classList.add('btn-warning');

            // Disable all status buttons
            document.querySelectorAll('.status-btn').forEach(btn => {
                btn.disabled = true;
                btn.style.cursor = 'not-allowed';
            });

            showNotification('Edit mode disabled. Task statuses are now locked.', 'info');
        }
    }

    // Update save button state based on pending changes
    function updateSaveButtonState() {
        console.log('🔄 updateSaveButtonState() called');
        const saveButton = document.getElementById('saveTaskStatusesBtn');
        if (!saveButton) {
            console.log('❌ Save button not found!');
            return;
        }

        // Initialize if needed
        if (!window.pendingTaskStatuses) {
            window.pendingTaskStatuses = {};
        }

        const pendingCount = Object.keys(window.pendingTaskStatuses).length;
        const hasPendingChanges = pendingCount > 0;

        console.log('🔄 Updating save button state:');
        console.log('   - Pending changes count:', pendingCount);
        console.log('   - Has pending changes:', hasPendingChanges);
        console.log('   - Current button disabled:', saveButton.disabled);
        console.log('   - Current button classes:', saveButton.className);

        // Always keep the save button enabled and show "Save All"
        console.log('🟢 Keeping save button enabled...');
        saveButton.disabled = false;
        saveButton.classList.remove('btn-secondary');
        saveButton.classList.add('btn-success');
        saveButton.innerHTML = '<i class="bi bi-check-circle me-2"></i>Save All';
        saveButton.style.backgroundColor = '#28a745';
        saveButton.style.borderColor = '#28a745';
        saveButton.style.color = 'white';
        console.log('✅ Button always enabled (green)');
        console.log('   - Button disabled:', saveButton.disabled);
        console.log('   - Button classes:', saveButton.className);
    }

    // Initialize save button state
    function initializeSaveButton() {
        console.log('🔄 Initializing save button...');
        const saveButton = document.getElementById('saveTaskStatusesBtn');
        if (saveButton) {
            saveButton.disabled = true;
            saveButton.classList.remove('btn-success');
            saveButton.classList.add('btn-secondary');
            saveButton.innerHTML = '<i class="bi bi-check-circle me-2"></i>Save All';
            console.log('✅ Save button initialized (disabled, gray)');
        } else {
            console.log('❌ Save button not found during initialization!');
        }
        window.pendingTaskStatuses = {};
        console.log('🔄 Pending statuses cleared');
    }

    // Attach event listeners to status buttons (alternative method)
    function attachStatusButtonListeners() {
        console.log('🔗 Attaching status button listeners...');
        const statusButtons = document.querySelectorAll('#taskChecklistModal .status-btn');
        console.log('🔗 Found', statusButtons.length, 'status buttons');

        statusButtons.forEach(button => {
            // Remove existing listeners to avoid duplicates
            button.removeEventListener('click', handleStatusButtonClick);
            // Add new listener
            button.addEventListener('click', handleStatusButtonClick);
        });
    }

    // Handle status button click
    function handleStatusButtonClick(e) {
        const target = e.currentTarget;
        console.log('🖱️ Status button clicked via direct listener!', target);

        const taskId = target.dataset.task;
        const week = target.dataset.week;
        const day = target.dataset.day;
        const status = target.dataset.status;

        console.log('📊 Button data:', { taskId, week, day, status });

        // Find other button in same cell and deactivate it
        const otherButtons = target.parentElement.querySelectorAll('.status-btn');
        otherButtons.forEach(btn => {
            if (btn !== target) {
                btn.classList.remove('active');
            }
        });

        // Toggle current button
        const wasActive = target.classList.contains('active');
        if (wasActive) {
            target.classList.remove('active');
        } else {
            target.classList.add('active');
        }
        const isNowActive = target.classList.contains('active');

        // Track pending changes for manual save
        const changeKey = `${taskId}_${week}_${day}`;
        const statusToSave = isNowActive ? status : null;

        // Initialize pendingTaskStatuses if it doesn't exist
        if (!window.pendingTaskStatuses) {
            window.pendingTaskStatuses = {};
        }

        if (statusToSave) {
            window.pendingTaskStatuses[changeKey] = {
                task_id: taskId,
                week: week,
                day: day,
                status: statusToSave
            };
        } else {
            // If deactivated, mark as cleared
            window.pendingTaskStatuses[changeKey] = {
                task_id: taskId,
                week: week,
                day: day,
                status: null
            };
        }

        // Debug logging
        console.log('📝 Pending statuses:', window.pendingTaskStatuses);
        console.log('📝 Pending count:', Object.keys(window.pendingTaskStatuses).length);

        // Update save button state
        updateSaveButtonState();

        console.log(`Task: ${taskId}, Week: ${week}, Day: ${day}, Status: ${statusToSave}, Pending save...`);
    }

    // DEBUG: Manual test functions
    window.testStatusClick = function() {
        console.log('🧪 Testing manual status click...');

        // Simulate a status button click
        if (!window.pendingTaskStatuses) {
            window.pendingTaskStatuses = {};
        }

        window.pendingTaskStatuses['test_1_mon'] = {
            task_id: 'test',
            week: '1',
            day: 'mon',
            status: 'correct'
        };

        console.log('🧪 Added test pending status:', window.pendingTaskStatuses);
        updateSaveButtonState();
    };

    window.testClearPending = function() {
        console.log('🧪 Clearing pending statuses...');
        window.pendingTaskStatuses = {};
        updateSaveButtonState();
    };

    // Test save functionality with minimal data
    window.testSaveDirectly = function() {
        console.log('🧪 Testing save functionality directly...');

        const testData = {
            statuses: [
                {
                    task_id: 'kitchen1',
                    week: '1',
                    day: 'mon',
                    status: 'correct'
                }
            ],
            date: '2024-12-23'
        };

        console.log('🧪 Sending test data:', testData);

        fetch('/task-checklist/save-all-statuses', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(testData)
        })
        .then(response => response.json())
        .then(data => {
            console.log('🧪 Test response:', data);
            if (data.success) {
                console.log('✅ Test save successful!');
            } else {
                console.log('❌ Test save failed:', data.message);
            }
        })
        .catch(error => {
            console.error('🧪 Test error:', error);
        });
    };

    // Load existing task statuses from database
    function loadExistingTaskStatuses() {
        const currentPage = currentTaskPage || 1;
        const dateInputId = currentPage === 1 ? 'week1_date' : `week1_date_p${currentPage}`;
        const dateInput = document.getElementById(dateInputId);
        const currentDate = dateInput ? dateInput.value : new Date().toISOString().split('T')[0];

        // Fetch existing statuses from database
        fetch('/task-checklist/get-statuses', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                date: currentDate,
                page: currentPage
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.statuses) {
                console.log('✅ Loaded existing statuses:', data.statuses);

                // Apply the loaded statuses to the UI
                data.statuses.forEach(statusData => {
                    const { task_id, status, day_name } = statusData;

                    if (status && task_id && day_name) {
                        // Convert day name to day abbreviation for matching
                        const dayMap = {
                            'Monday': 'mon',
                            'Tuesday': 'tue',
                            'Wednesday': 'wed',
                            'Thursday': 'thu',
                            'Friday': 'fri',
                            'Saturday': 'sat',
                            'Sunday': 'sun'
                        };

                        const dayAbbr = dayMap[day_name];
                        if (dayAbbr) {
                            // Find the corresponding button and activate it
                            const button = document.querySelector(`[data-task="${task_id}"][data-day="${dayAbbr}"][data-status="${status}"]`);
                            if (button) {
                                // Deactivate other buttons in the same cell
                                const otherButtons = button.parentElement.querySelectorAll('.status-btn');
                                otherButtons.forEach(btn => btn.classList.remove('active'));

                                // Activate the correct button
                                button.classList.add('active');
                                console.log(`✅ Restored status: Task ${task_id}, Day ${day_name}, Status ${status}`);
                            }
                        }
                    }
                });
            }
        })
        .catch(error => {
            console.error('❌ Error loading existing statuses:', error);
        });
    }

    // Mark task function for check/wrong buttons
    function toggleCheckbox(cellId) {
        const container = document.getElementById(`${cellId}_container`);
        const buttons = document.getElementById(`${cellId}_buttons`);
        const status = document.getElementById(`${cellId}_status`);

        // Check if the checkbox is already marked (has a status)
        if (status.innerHTML !== '') {
            // If already marked, clear it
            clearTask(cellId);
        } else {
            // If not marked, show buttons for selection
            if (buttons.style.display === 'none') {
                buttons.style.display = 'flex';
                status.style.display = 'none';
                container.style.backgroundColor = '#f8f9fa';
            } else {
                // If buttons are visible, hide them
                buttons.style.display = 'none';
                status.style.display = 'block';
                container.style.backgroundColor = 'white';
            }
        }
    }

    function markTask(cellId, status) {
        const container = document.getElementById(`${cellId}_container`);
        const buttons = document.getElementById(`${cellId}_buttons`);
        const statusSpan = document.getElementById(`${cellId}_status`);

        // Hide buttons and show status
        buttons.style.display = 'none';
        statusSpan.style.display = 'block';

        // Set the status icon and color
        if (status === 'check') {
            statusSpan.innerHTML = '✓';
            statusSpan.style.color = '#4caf50';
            container.style.backgroundColor = '#e8f5e8';
            container.style.borderColor = '#4caf50';
        } else if (status === 'wrong') {
            statusSpan.innerHTML = '✗';
            statusSpan.style.color = '#f44336';
            container.style.backgroundColor = '#ffeaea';
            container.style.borderColor = '#f44336';
        }

        // You can add logic here to save the status to database or local storage
        console.log(`Task ${cellId} marked as ${status}`);
    }

    function clearTask(cellId) {
        const container = document.getElementById(`${cellId}_container`);
        const buttons = document.getElementById(`${cellId}_buttons`);
        const statusSpan = document.getElementById(`${cellId}_status`);

        // Reset to original state
        statusSpan.innerHTML = '';
        statusSpan.style.color = '';
        container.style.backgroundColor = 'white';
        container.style.borderColor = '#dee2e6';
        buttons.style.display = 'none';
        statusSpan.style.display = 'block';

        console.log(`Task ${cellId} cleared`);
    }

    // Task Page Navigation
    let currentTaskPage = 1;
    const totalTaskPages = 10; // You can adjust this based on how many pages you want

    function navigateTaskPage(direction) {
        if (direction === 'next') {
            currentTaskPage = currentTaskPage < totalTaskPages ? currentTaskPage + 1 : 1;
        } else if (direction === 'prev') {
            currentTaskPage = currentTaskPage > 1 ? currentTaskPage - 1 : totalTaskPages;
        }

        console.log('Current Task Page:', currentTaskPage);

        // Update page number display
        document.getElementById('currentPageNumber').textContent = currentTaskPage;

        // Here you can load different content based on currentTaskPage
        loadTaskPageContent(currentTaskPage);

        // Load existing statuses for the new page after a short delay
        setTimeout(() => {
            loadExistingTaskStatuses();
            loadSavedStatuses(); // Load from localStorage as well
            // Also attach event listeners to newly loaded content
            attachStatusButtonListeners();
        }, 300);
    }

    function loadTaskPageContent(pageNumber) {
        console.log('🔄 Loading task page:', pageNumber);

        // SAVE CURRENT STATE BEFORE CHANGING PAGES
        if (typeof saveCurrentState === 'function') {
            saveCurrentState();
        }

        const taskPageContent = document.getElementById('taskPageContent');

        if (pageNumber === 1) {
            // Page 1: Kitchen & General Cleaning Tasks
            taskPageContent.innerHTML = getPage1Content();
        } else if (pageNumber === 2) {
            // Page 2: Dishwashing & General Cleaning Tasks
            taskPageContent.innerHTML = getPage2Content();
        } else if (pageNumber === 3) {
            // Page 3: Dining & Ground Floor Cleaning
            taskPageContent.innerHTML = getPage3Content();
        } else if (pageNumber === 4) {
            // Page 4: Laundry & Maintenance
            taskPageContent.innerHTML = getPage4Content();
        } else if (pageNumber === 5) {
            // Page 5: Security & Safety
            taskPageContent.innerHTML = getPage5Content();
        } else if (pageNumber === 6) {
            // Page 6: Office & Administrative
            taskPageContent.innerHTML = getPage6Content();
        } else if (pageNumber === 7) {
            // Page 7: Garden & Outdoor
            taskPageContent.innerHTML = getPage7Content();
        } else if (pageNumber === 8) {
            // Page 8: Storage & Inventory
            taskPageContent.innerHTML = getPage8Content();
        } else if (pageNumber === 9) {
            // Page 9: Recreation & Events
            taskPageContent.innerHTML = getPage9Content();
        } else if (pageNumber === 10) {
            // Page 10: Special Tasks & Projects
            taskPageContent.innerHTML = getPage10Content();
        } else {
            // Default to Page 1
            taskPageContent.innerHTML = getPage1Content();
        }

        // Update modal title to show current page
        document.getElementById('taskChecklistModalLabel').textContent = `Task Checklist - Page ${pageNumber}`;

        // Set today's date in all date inputs after content is loaded
        setTimeout(() => {
            setCurrentDateInInputs();
            // Also attach event listeners to newly created buttons
            attachStatusButtonListeners();
            // Add date change listener
            addDateChangeListener();

            // Load saved task statuses with multiple attempts to ensure buttons are rendered
            setTimeout(() => {
                console.log('🔄 First attempt to load localStorage...');
                loadSavedTaskStatuses();
            }, 300);

            // Double-check after another delay
            setTimeout(() => {
                console.log('🔄 Second attempt to load localStorage...');
                loadSavedTaskStatuses();
            }, 800);

            // Triple-check to be absolutely sure
            setTimeout(() => {
                console.log('🔄 Final attempt to load localStorage...');
                loadSavedTaskStatuses();
            }, 1500);
        }, 100);

        // AUTOMATIC RESTORE AFTER PAGE CONTENT LOADS
        setTimeout(() => {
            if (typeof loadCurrentState === 'function') {
                loadCurrentState();
                console.log('🔄 Auto-restored selections for page:', pageNumber);
            }
        }, 200);

        // ADDITIONAL RESTORE ATTEMPT
        setTimeout(() => {
            if (typeof loadCurrentState === 'function') {
                loadCurrentState();
                console.log('🔄 Second auto-restore for page:', pageNumber);
            }
        }, 800);
    }

    function getPage1Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 10px; border: 1px solid #dee2e6; width: 100%; table-layout: fixed;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: transparent; border: 1px solid #dee2e6; font-size: 11px; font-weight: 600; padding: 6px; vertical-align: middle; width: 120px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: transparent; border: 1px solid #dee2e6; font-size: 11px; font-weight: 600; padding: 4px; vertical-align: middle; width: 450px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: transparent; border: 1px solid #dee2e6; padding: 4px; font-size: 10px; font-weight: 600;">
                    DATE: <input type="date" id="week1_date" value="" class="form-control d-inline-block" style="width: 200px; font-size: 18px; padding: 12px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;" onchange="updateWeekDates()">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 400px; background-color: #000000; color: white; border: 1px solid #000; font-size: 16px; padding: 8px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 35px; border: 1px solid #dee2e6; font-size: 8px; padding: 2px; background-color: transparent; font-weight: 600;">MON</th>
                  <th class="text-center" style="width: 35px; border: 1px solid #dee2e6; font-size: 8px; padding: 2px; background-color: transparent; font-weight: 600;">TUE</th>
                  <th class="text-center" style="width: 35px; border: 1px solid #dee2e6; font-size: 8px; padding: 2px; background-color: transparent; font-weight: 600;">WED</th>
                  <th class="text-center" style="width: 35px; border: 1px solid #dee2e6; font-size: 8px; padding: 2px; background-color: transparent; font-weight: 600;">THU</th>
                  <th class="text-center" style="width: 35px; border: 1px solid #dee2e6; font-size: 8px; padding: 2px; background-color: transparent; font-weight: 600;">FRI</th>
                  <th class="text-center" style="width: 35px; border: 1px solid #dee2e6; font-size: 8px; padding: 2px; background-color: transparent; font-weight: 600;">SAT</th>
                  <th class="text-center" style="width: 35px; border: 1px solid #dee2e6; font-size: 8px; padding: 2px; background-color: transparent; font-weight: 600;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getKitchenRows()}
                ${getGeneralCleaningRows()}
              </tbody>
            </table>
        `;
    }

    function getKitchenRows() {
        const kitchenTasks = [
            'Assigned members wake up on time and completed their tasks as scheduled.',
            'The students assigned to cook the rice completed the task properly.',
            'The students assigned to cook the viand completed the task properly.',
            'The students assigned to assist the cook carried out their duties diligently.',
            'Ingredients were prepared ahead of time.',
            'The kitchen was properly cleaned after cooking.',
            'The food was transferred from the kitchen to the center.',
            'Proper inventory of stocks was maintained and deliveries were handled appropriately.',
            'Water and food supplies were regularly monitored and stored in the proper place.',
            'Receipts, kitchen phones, and keys were safely stored.',
            'Kitchen utensils were properly stored.',
            'The stove was turned off after cooking.',
            'Properly disposed of the garbage.',
            'Properly washed the burner.',
            'Wiped and arranged the chiller.',
            'Cleaned the canal after cooking.',
            'Arranged the freezer.'
        ];

        let rows = '';
        kitchenTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${kitchenTasks.length}" class="text-center category-cell" style="background-color: #4caf50; color: white; border: 1px solid #000; font-size: 14px; font-weight: bold; padding: 12px; vertical-align: middle;">KITCHEN<br><br>2-3<br>unchecked-<br>for<br>improvement<br>5 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td style="border: 1px solid #dee2e6; padding: 4px; font-size: 18px; background-color: #fafafa; text-align: left; line-height: 1.3; font-weight: normal; color: #000000;">
                        ${task}
                    </td>
                    ${generateCheckboxCells('kitchen' + (index + 1))}
                    <td style="border: 1px solid #dee2e6; padding: 12px; background-color: #fff8e1; width: 400px;">
                        <textarea class="form-control" placeholder="Remarks..." style="font-size: 16px; padding: 12px; border: 1px solid #dee2e6; color: #666; font-weight: normal; height: 60px; resize: vertical;"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGeneralCleaningRows() {
        const cleaningTasks = [
            'Cleaned the drainage canals.',
            'Brushed and rinsed the floor of the dishwashing area.',
            'Brushed the sink.',
            'Washed the barrel container.',
            'Cleaned and arranged the storage cabinet.',
            'Wiped the cabinets. (No dusts/stains inside and outside the cabinet)'
        ];

        let rows = '';
        cleaningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${cleaningTasks.length}" class="text-center category-cell" style="background-color: #2196f3; color: white; border: 1px solid #000; font-size: 14px; font-weight: normal; padding: 12px; vertical-align: middle;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td style="border: 1px solid #dee2e6; padding: 4px; font-size: 18px; background-color: #fafafa; text-align: left; line-height: 1.3; font-weight: normal; color: #000000;">
                        ${task}
                    </td>
                    ${generateCheckboxCells('cleaning' + (index + 1))}
                    <td style="border: 1px solid #dee2e6; padding: 12px; background-color: #fff8e1; width: 400px;">
                        <textarea class="form-control" placeholder="Remarks..." style="font-size: 16px; padding: 12px; border: 1px solid #dee2e6; color: #666; font-weight: normal; height: 60px; resize: vertical;"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function generateCheckboxCells(taskId, week = 'week1') {
        const days = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
        let cells = '';

        days.forEach(day => {
            const weekNumber = week === 'week1' ? '1' : '2';
            cells += `
                <td class="text-center" style="border: 1px solid #dee2e6; padding: 8px; background-color: #f8f9fa;">
                    <div class="d-flex justify-content-center gap-1">
                        <button type="button" class="btn btn-sm btn-outline-success status-btn"
                                data-task="${taskId}" data-week="${weekNumber}" data-day="${day}" data-status="correct"
                                onclick="toggleButton(this, '${taskId}', '${weekNumber}', '${day}', 'correct');"
                                style="width: 32px; height: 32px; padding: 0; font-size: 18px; border-radius: 6px; border: 2px solid #28a745; background-color: white; color: #28a745; font-weight: bold; margin: 2px; cursor: pointer;">
                            ✓
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger status-btn"
                                data-task="${taskId}" data-week="${weekNumber}" data-day="${day}" data-status="wrong"
                                onclick="toggleButton(this, '${taskId}', '${weekNumber}', '${day}', 'wrong');"
                                style="width: 32px; height: 32px; padding: 0; font-size: 18px; border-radius: 6px; border: 2px solid #dc3545; background-color: white; color: #dc3545; font-weight: bold; margin: 2px; cursor: pointer;">
                            ✗
                        </button>
                    </div>
                </td>
            `;
        });

        return cells;
    }

    // Direct click handler for status buttons
    function handleStatusButtonClickDirect(taskId, week, day, status, buttonElement) {
        console.log('🖱️ Button clicked!', { taskId, week, day, status });

        try {
            // Find other button in same cell and deactivate it
            const otherButtons = buttonElement.parentElement.querySelectorAll('.status-btn');
            otherButtons.forEach(btn => {
                if (btn !== buttonElement) {
                    btn.classList.remove('active');
                }
            });

            // Toggle current button
            const wasActive = buttonElement.classList.contains('active');
            if (wasActive) {
                buttonElement.classList.remove('active');
                console.log('🔴 Button deactivated');
            } else {
                buttonElement.classList.add('active');
                console.log('🟢 Button activated');
            }
            const isNowActive = buttonElement.classList.contains('active');

            // Track pending changes for manual save
            const changeKey = `${taskId}_${week}_${day}`;
            const statusToSave = isNowActive ? status : null;

            // Initialize pendingTaskStatuses if it doesn't exist
            if (!window.pendingTaskStatuses) {
                window.pendingTaskStatuses = {};
            }

            if (statusToSave) {
                window.pendingTaskStatuses[changeKey] = {
                    task_id: taskId,
                    week: week,
                    day: day,
                    status: statusToSave
                };
                console.log('💾 Added to pending:', changeKey, statusToSave);
            } else {
                // If deactivated, remove from pending
                delete window.pendingTaskStatuses[changeKey];
                console.log('🗑️ Removed from pending:', changeKey);
            }

            // Update save button state
            updateSaveButtonState();

            console.log(`✅ Status updated: ${taskId} ${week} ${day} = ${statusToSave}`);
        } catch (error) {
            console.error('❌ Error in button click handler:', error);
        }
    }

    // Make function globally available
    window.handleStatusButtonClickDirect = handleStatusButtonClickDirect;

    // Simple toggle button function
    function toggleButton(button, taskId, week, day, status) {
        console.log('🎯 toggleButton called!', { taskId, week, day, status });

        // Find other buttons in the same cell
        const parentDiv = button.parentElement;
        const allButtons = parentDiv.querySelectorAll('.status-btn');

        // Remove active class from all buttons in this cell
        allButtons.forEach(btn => {
            btn.classList.remove('active');
            btn.style.backgroundColor = '';
            btn.style.color = '';
        });

        // Toggle current button
        if (button.classList.contains('selected')) {
            // Deselect
            button.classList.remove('selected');
            button.style.backgroundColor = '';
            button.style.color = '';
            button.style.borderColor = '';
            button.style.boxShadow = '';
        } else {
            // Select
            button.classList.add('selected');
            if (status === 'correct') {
                button.style.backgroundColor = '#28a745';
                button.style.color = 'white';
                button.style.borderColor = '#28a745';
                button.style.boxShadow = '0 0 10px rgba(40, 167, 69, 0.5)';
            } else {
                button.style.backgroundColor = '#dc3545';
                button.style.color = 'white';
                button.style.borderColor = '#dc3545';
                button.style.boxShadow = '0 0 10px rgba(220, 53, 69, 0.5)';
            }
        }

        // Initialize pending statuses if needed
        if (!window.pendingTaskStatuses) {
            window.pendingTaskStatuses = {};
        }

        // Track the change
        const changeKey = `${taskId}_${week}_${day}`;
        if (button.classList.contains('selected')) {
            window.pendingTaskStatuses[changeKey] = {
                task_id: taskId,
                week: week,
                day: day,
                status: status
            };
        } else {
            delete window.pendingTaskStatuses[changeKey];
        }

        console.log('📝 Pending statuses:', window.pendingTaskStatuses);

        // Auto-save immediately (background save)
        autoSaveTaskStatus(taskId, week, day, button.classList.contains('selected') ? status : null);

        // Update save button to show there are changes to save manually
        updateSaveButtonState();
    }

    // Load saved statuses from localStorage
    function loadSavedStatuses() {
        console.log('🔄 Loading saved statuses from localStorage...');

        const currentPage = currentTaskPage || 1;
        const dateInputId = currentPage === 1 ? 'week1_date' : `week1_date_p${currentPage}`;
        const dateInput = document.getElementById(dateInputId);
        const currentDate = dateInput ? dateInput.value : new Date().toISOString().split('T')[0];

        // Use the main localStorage loading function
        console.log('🔄 Calling main loadFromLocalStorage function...');
        loadFromLocalStorage(currentDate);

        console.log('✅ Finished loading saved statuses');
    }

    // Make toggle function globally available
    window.toggleButton = toggleButton;

    // Auto-save individual task status (localStorage-first approach)
    function autoSaveTaskStatus(taskId, week, day, status) {
        console.log('💾 Auto-saving:', { taskId, week, day, status });

        // Get current date from the date input
        const currentPage = currentTaskPage || 1;
        const dateInputId = currentPage === 1 ? 'week1_date' : `week1_date_p${currentPage}`;
        const dateInput = document.getElementById(dateInputId);
        let currentDate = dateInput ? dateInput.value : new Date().toISOString().split('T')[0];

        console.log('📅 Using date for save:', currentDate);

        // Convert day names to numbers for backend
        const dayMapping = {
            'mon': 0, 'tue': 1, 'wed': 2, 'thu': 3,
            'fri': 4, 'sat': 5, 'sun': 6,
            'monday': 0, 'tuesday': 1, 'wednesday': 2, 'thursday': 3,
            'friday': 4, 'saturday': 5, 'sunday': 6
        };
        const dayNumber = dayMapping[day.toLowerCase()] !== undefined ? dayMapping[day.toLowerCase()] : parseInt(day);

        // ALWAYS save to localStorage first for immediate persistence (PAGE-SPECIFIC)
        const currentPage = currentTaskPage || 1;
        const localStorageKey = `task_status_${taskId}_${week}_${dayNumber}_${currentDate}_page${currentPage}`;
        localStorage.setItem(localStorageKey, status || '');
        console.log('💾 Saved to localStorage (page-specific):', localStorageKey, '=', status);

        console.log('📊 Saving data:', {
            task_id: taskId,
            week: week,
            day: dayNumber,
            status: status,
            date: currentDate
        });

        // Try to save to server (but don't fail if it doesn't work)
        fetch('/task-checklist/save-single-status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                task_id: taskId,
                week: week,
                day: dayNumber,
                status: status,
                date: currentDate
            })
        })
        .then(response => {
            console.log('📥 Auto-save response status:', response.status);

            // Check if response is actually JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                console.warn('⚠️ Server returned non-JSON response, using localStorage only');
                return null;
            }

            if (!response.ok) {
                console.warn(`⚠️ Server error ${response.status}, using localStorage only`);
                return null;
            }
            return response.json();
        })
        .then(data => {
            if (data && data.success) {
                console.log('✅ Auto-saved to server successfully:', data);
                localStorage.setItem(localStorageKey + '_confirmed', 'true');
            } else if (data) {
                console.warn('⚠️ Server save failed, using localStorage only:', data.message);
            }
        })
        .catch(error => {
            console.warn('⚠️ Server save failed, using localStorage only:', error.message);
            // Don't show error message since localStorage save worked
        });
    }

    // Load saved task statuses when page loads
    function loadSavedTaskStatuses() {
        console.log('📥 Loading saved task statuses...');

        // Get current date
        const currentPage = currentTaskPage || 1;
        const dateInputId = currentPage === 1 ? 'week1_date' : `week1_date_p${currentPage}`;
        const dateInput = document.getElementById(dateInputId);
        let currentDate = dateInput ? dateInput.value : new Date().toISOString().split('T')[0];

        console.log('📅 Loading statuses for date:', currentDate, 'page:', currentPage);

        // First, load from localStorage (immediate)
        loadFromLocalStorage(currentDate);

        // Then try to fetch from server (background sync)
        fetch(`/task-checklist/get-statuses?date=${currentDate}&page=${currentPage}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => {
            // Check if response is actually JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                console.warn('⚠️ Server returned non-JSON response, using localStorage only');
                return null;
            }
            return response.json();
        })
        .then(data => {
            if (data && data.success && data.statuses) {
                console.log('📥 Server response:', data);
                console.log('📥 Found', data.statuses.length, 'saved statuses from server');

                // Day mapping for converting day index to day name
                const dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

                // Apply saved statuses from server (but don't override localStorage)
                data.statuses.forEach(statusData => {
                    console.log('🔍 Processing server status:', statusData);

                    // Convert day index to day name if needed
                    const dayName = dayNames[statusData.day_index] || statusData.day_name?.toLowerCase();
                    const week = 1; // Assuming week 1 for now

                    // Check if localStorage already has this value
                    const localStorageKey = `task_status_${statusData.task_id}_${week}_${statusData.day_index}_${currentDate}`;
                    const localValue = localStorage.getItem(localStorageKey);

                    // Only apply server value if localStorage doesn't have a value
                    if (!localValue) {
                        // Find the correct button using multiple selectors
                        let button = document.querySelector(
                            `[data-task="${statusData.task_id}"][data-week="${week}"][data-day="${dayName}"][data-status="${statusData.status}"]`
                        );

                        // Try alternative selector if first one doesn't work
                        if (!button) {
                            button = document.querySelector(
                                `[data-task="${statusData.task_id}"][data-week="${week}"][data-day="${statusData.day_index}"][data-status="${statusData.status}"]`
                            );
                        }

                        if (button) {
                            updateButtonAppearance(button, statusData.status);
                            localStorage.setItem(localStorageKey, statusData.status);
                            localStorage.setItem(localStorageKey + '_confirmed', 'true');
                            console.log('✅ Applied server status:', statusData);
                        }
                    }
                });
            } else {
                console.log('📥 No saved statuses found on server or error:', data?.message);
            }
        })
        .catch(error => {
            console.warn('⚠️ Server load failed, using localStorage only:', error.message);
        });
    }

    // Load statuses from localStorage
    function loadFromLocalStorage(currentDate) {
        const currentPage = currentTaskPage || 1;
        console.log('💾 Loading from localStorage for date:', currentDate, 'page:', currentPage);

        let loadedCount = 0;
        const allButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status]');
        console.log('🔍 Found', allButtons.length, 'buttons on page', currentPage);

        // Get all localStorage keys for this date AND PAGE (PAGE-SPECIFIC)
        const pageSpecificSuffix = `_${currentDate}_page${currentPage}`;
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('task_status_') && key.endsWith(pageSpecificSuffix)) {
                const value = localStorage.getItem(key);
                console.log('🔑 Found localStorage key:', key, 'value:', value);
                if (value) {
                    // Parse the key to get task details (page-specific format)
                    const keyWithoutPrefix = key.replace('task_status_', '');
                    const keyWithoutSuffix = keyWithoutPrefix.replace(pageSpecificSuffix, '');
                    const parts = keyWithoutSuffix.split('_');

                    if (parts.length >= 3) {
                        const taskId = parts[0];
                        const week = parts[1];
                        const dayIndex = parts[2];

                        // Find button by data attributes
                        const dayNames = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
                        const dayName = dayNames[dayIndex];

                        let button = document.querySelector(
                            `[data-task="${taskId}"][data-week="${week}"][data-day="${dayName}"][data-status="${value}"]`
                        );

                        if (!button) {
                            button = document.querySelector(
                                `[data-task="${taskId}"][data-week="${week}"][data-day="${dayIndex}"][data-status="${value}"]`
                            );
                        }

                        if (button) {
                            console.log('✅ Found button, updating appearance:', { taskId, week, dayIndex, dayName, value });
                            console.log('🔍 Button before update:', {
                                classList: button.classList.toString(),
                                backgroundColor: button.style.backgroundColor,
                                selected: button.classList.contains('selected')
                            });

                            updateButtonAppearance(button, value);

                            console.log('🔍 Button after update:', {
                                classList: button.classList.toString(),
                                backgroundColor: button.style.backgroundColor,
                                selected: button.classList.contains('selected')
                            });

                            loadedCount++;
                            console.log('💾 Restored from localStorage:', { taskId, week, dayIndex, dayName, value });
                        } else {
                            console.warn('❌ Button not found for:', { taskId, week, dayIndex, dayName, value });
                            console.log('🔍 Selector tried:', `[data-task="${taskId}"][data-week="${week}"][data-day="${dayName}"][data-status="${value}"]`);

                            // Try to find any button with this task and day to debug
                            const anyButton = document.querySelector(`[data-task="${taskId}"][data-week="${week}"][data-day="${dayName}"]`);
                            if (anyButton) {
                                console.log('🔍 Found similar button:', anyButton);
                                console.log('🔍 Button attributes:', {
                                    task: anyButton.getAttribute('data-task'),
                                    week: anyButton.getAttribute('data-week'),
                                    day: anyButton.getAttribute('data-day'),
                                    status: anyButton.getAttribute('data-status')
                                });
                            }
                        }
                    }
                }
            }
        }

        if (loadedCount > 0) {
            console.log(`✅ Loaded ${loadedCount} statuses from localStorage`);
        } else {
            console.log('ℹ️ No localStorage data found for this date');
        }
    }

    // Test function to verify localStorage persistence
    function testLocalStoragePersistence() {
        const currentDate = new Date().toISOString().split('T')[0];
        console.log('🧪 Testing localStorage persistence for date:', currentDate);

        // List all localStorage keys for current date (SHARED ACROSS PAGES)
        const keys = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('task_status_') && key.endsWith('_' + currentDate)) {
                keys.push(key + ' = ' + localStorage.getItem(key));
            }
        }

        console.log('🔑 Found localStorage keys:', keys);
        return keys;
    }

    // Make test function available globally
    window.testLocalStoragePersistence = testLocalStoragePersistence;

    // Force reload localStorage data (can be called manually)
    function forceReloadLocalStorage() {
        const currentPage = currentTaskPage || 1;
        const dateInputId = currentPage === 1 ? 'week1_date' : `week1_date_p${currentPage}`;
        const dateInput = document.getElementById(dateInputId);
        const currentDate = dateInput ? dateInput.value : new Date().toISOString().split('T')[0];

        console.log('🔄 Force reloading localStorage for date:', currentDate);
        loadFromLocalStorage(currentDate);
    }

    // Make force reload available globally
    window.forceReloadLocalStorage = forceReloadLocalStorage;

    // Test function to manually highlight a button
    function testHighlightButton(taskId, week, day, status) {
        const button = document.querySelector(`[data-task="${taskId}"][data-week="${week}"][data-day="${day}"][data-status="${status}"]`);
        if (button) {
            console.log('🧪 Testing button highlight:', { taskId, week, day, status });
            updateButtonAppearance(button, status);
            console.log('✅ Button highlighted successfully');
            return true;
        } else {
            console.log('❌ Button not found for test:', { taskId, week, day, status });
            return false;
        }
    }

    // Make test function available globally
    window.testHighlightButton = testHighlightButton;

    // Test function to check all buttons on page
    function debugAllButtons() {
        const buttons = document.querySelectorAll('[data-task][data-week][data-day][data-status]');
        console.log('🔍 Found', buttons.length, 'buttons on page:');

        buttons.forEach((button, index) => {
            if (index < 10) { // Only show first 10 to avoid spam
                console.log(`Button ${index + 1}:`, {
                    task: button.getAttribute('data-task'),
                    week: button.getAttribute('data-week'),
                    day: button.getAttribute('data-day'),
                    status: button.getAttribute('data-status'),
                    selected: button.classList.contains('selected'),
                    backgroundColor: button.style.backgroundColor
                });
            }
        });

        return buttons.length;
    }

    // Make debug function available globally
    window.debugAllButtons = debugAllButtons;

    // Test function to manually save and load a status
    function testSaveAndLoad() {
        console.log('🧪 Testing save and load functionality...');

        // Find the first button on the page
        const firstButton = document.querySelector('[data-task][data-week][data-day][data-status="correct"]');
        if (firstButton) {
            const taskId = firstButton.getAttribute('data-task');
            const week = firstButton.getAttribute('data-week');
            const day = firstButton.getAttribute('data-day');
            const status = 'correct';

            console.log('🧪 Testing with button:', { taskId, week, day, status });

            // Manually save to localStorage
            const currentDate = new Date().toISOString().split('T')[0];
            const dayMapping = { 'mon': 0, 'tue': 1, 'wed': 2, 'thu': 3, 'fri': 4, 'sat': 5, 'sun': 6 };
            const dayNumber = dayMapping[day.toLowerCase()] || 0;
            const localStorageKey = `task_status_${taskId}_${week}_${dayNumber}_${currentDate}`;

            localStorage.setItem(localStorageKey, status);
            console.log('🧪 Saved to localStorage:', localStorageKey, '=', status);

            // Manually highlight the button
            updateButtonAppearance(firstButton, status);
            console.log('🧪 Button highlighted manually');

            // Test loading
            setTimeout(() => {
                console.log('🧪 Testing load...');
                loadFromLocalStorage(currentDate);
            }, 1000);

            return { taskId, week, day, status, localStorageKey };
        } else {
            console.log('❌ No buttons found for testing');
            return null;
        }
    }

    // Make test function available globally
    window.testSaveAndLoad = testSaveAndLoad;

    // Auto-refresh localStorage data every 3 seconds to ensure persistence
    function startAutoRefresh() {
        setInterval(() => {
            const taskModal = document.getElementById('taskChecklistModal');
            if (taskModal && taskModal.classList.contains('show')) {
                const currentDate = new Date().toISOString().split('T')[0];
                console.log('🔄 AUTO-REFRESH localStorage for date:', currentDate);
                loadFromLocalStorage(currentDate);
            }
        }, 3000);
    }

    // Start auto-refresh when page loads
    setTimeout(startAutoRefresh, 2000);

    // Make auto-refresh available globally
    window.startAutoRefresh = startAutoRefresh;

    // Comprehensive debug function to see what's happening
    function fullDebug() {
        console.log('🔍 === FULL DEBUG REPORT ===');

        const currentDate = new Date().toISOString().split('T')[0];
        console.log('📅 Current date:', currentDate);

        // Check localStorage
        console.log('💾 localStorage contents:');
        let localStorageCount = 0;
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('task_status_')) {
                const value = localStorage.getItem(key);
                console.log(`  ${key} = ${value}`);
                localStorageCount++;
            }
        }
        console.log(`💾 Total localStorage entries: ${localStorageCount}`);

        // Check buttons on page
        const allButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status]');
        console.log(`🔘 Total buttons on page: ${allButtons.length}`);

        // Check highlighted buttons
        const highlightedButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status].selected');
        console.log(`✅ Highlighted buttons: ${highlightedButtons.length}`);

        // Show first few buttons for debugging
        console.log('🔍 First 5 buttons:');
        for (let i = 0; i < Math.min(5, allButtons.length); i++) {
            const btn = allButtons[i];
            console.log(`  Button ${i + 1}:`, {
                task: btn.getAttribute('data-task'),
                week: btn.getAttribute('data-week'),
                day: btn.getAttribute('data-day'),
                status: btn.getAttribute('data-status'),
                selected: btn.classList.contains('selected'),
                backgroundColor: btn.style.backgroundColor,
                innerHTML: btn.innerHTML
            });
        }

        // Try to manually load one item from localStorage
        if (localStorageCount > 0) {
            console.log('🧪 Testing manual load of first localStorage item...');
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('task_status_')) {
                    const value = localStorage.getItem(key);
                    console.log(`🧪 Testing key: ${key} = ${value}`);

                    // Parse the key to get task info
                    const parts = key.replace('task_status_', '').split('_');
                    if (parts.length >= 4) {
                        const taskId = parts.slice(0, -3).join('_');
                        const week = parts[parts.length - 3];
                        const dayIndex = parts[parts.length - 2];
                        const date = parts[parts.length - 1];

                        console.log(`🧪 Parsed:`, { taskId, week, dayIndex, date, value });

                        // Try to find the button
                        const dayNames = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
                        const dayName = dayNames[parseInt(dayIndex)] || dayIndex;

                        const button = document.querySelector(
                            `[data-task="${taskId}"][data-week="${week}"][data-day="${dayName}"][data-status="${value}"]`
                        );

                        if (button) {
                            console.log('✅ Found button for test, highlighting...');
                            updateButtonAppearance(button, value);
                        } else {
                            console.log('❌ Button not found for test');
                        }
                        break;
                    }
                }
            }
        }

        return {
            localStorageCount,
            totalButtons: allButtons.length,
            highlightedButtons: highlightedButtons.length
        };
    }

    // Make full debug available globally
    window.fullDebug = fullDebug;

    // Force save and load test
    function forceSaveAndLoad() {
        console.log('🧪 === FORCE SAVE AND LOAD TEST ===');

        const currentDate = new Date().toISOString().split('T')[0];
        const allButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status]');

        if (allButtons.length === 0) {
            console.log('❌ No buttons found on page!');
            return;
        }

        // Take first few buttons and force save them
        const testButtons = Array.from(allButtons).slice(0, 6);
        console.log(`🧪 Testing with ${testButtons.length} buttons`);

        testButtons.forEach((button, index) => {
            const taskId = button.getAttribute('data-task');
            const week = button.getAttribute('data-week');
            const day = button.getAttribute('data-day');
            const status = button.getAttribute('data-status');

            // Alternate between correct and wrong
            const testStatus = (index % 2 === 0) ? 'correct' : 'wrong';

            // Force save to localStorage (SHARED ACROSS PAGES)
            const dayMapping = { 'mon': 0, 'tue': 1, 'wed': 2, 'thu': 3, 'fri': 4, 'sat': 5, 'sun': 6 };
            const dayNumber = dayMapping[day.toLowerCase()] || 0;
            const localStorageKey = `task_status_${taskId}_${week}_${dayNumber}_${currentDate}`;

            localStorage.setItem(localStorageKey, testStatus);
            console.log(`🧪 Force saved (shared): ${localStorageKey} = ${testStatus}`);
        });

        // Now try to load them
        setTimeout(() => {
            console.log('🧪 Now loading saved data...');
            loadFromLocalStorage(currentDate);

            // Check results
            setTimeout(() => {
                const highlightedButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status].selected');
                console.log(`🧪 Result: ${highlightedButtons.length} buttons highlighted`);

                if (highlightedButtons.length > 0) {
                    console.log('✅ SUCCESS: Buttons are being highlighted!');
                } else {
                    console.log('❌ FAILED: No buttons highlighted');
                }
            }, 1000);
        }, 500);
    }

    // Make force test available globally
    window.forceSaveAndLoad = forceSaveAndLoad;

    // Simple test to manually highlight buttons
    function testHighlighting() {
        console.log('🧪 === TESTING BUTTON HIGHLIGHTING ===');

        const allButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status]');
        console.log(`🔘 Found ${allButtons.length} buttons`);

        if (allButtons.length === 0) {
            console.log('❌ No buttons found!');
            return;
        }

        // Test highlighting first 8 buttons
        for (let i = 0; i < Math.min(8, allButtons.length); i++) {
            const button = allButtons[i];
            const status = (i % 2 === 0) ? 'correct' : 'wrong';

            console.log(`🧪 Testing button ${i + 1} with status: ${status}`);
            updateButtonAppearance(button, status);
        }

        // Check results after a short delay
        setTimeout(() => {
            const highlightedButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status].selected');
            console.log(`✅ Result: ${highlightedButtons.length} buttons highlighted`);

            highlightedButtons.forEach((btn, index) => {
                console.log(`Highlighted button ${index + 1}:`, {
                    backgroundColor: btn.style.backgroundColor,
                    color: btn.style.color,
                    innerHTML: btn.innerHTML,
                    selected: btn.classList.contains('selected')
                });
            });
        }, 500);
    }

    // Make test available globally
    window.testHighlighting = testHighlighting;

    // Clear localStorage for current date (for testing)
    function clearCurrentDateData() {
        const currentDate = new Date().toISOString().split('T')[0];
        console.log('🧹 Clearing localStorage for date:', currentDate);

        const keysToRemove = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('task_status_') && key.endsWith('_' + currentDate)) {
                keysToRemove.push(key);
            }
        }

        keysToRemove.forEach(key => {
            localStorage.removeItem(key);
            console.log('🗑️ Removed:', key);
        });

        console.log(`✅ Cleared ${keysToRemove.length} entries for date ${currentDate}`);

        // Reload to show cleared state
        setTimeout(() => {
            loadFromLocalStorage(currentDate);
        }, 100);
    }

    // Make clear function available globally
    window.clearCurrentDateData = clearCurrentDateData;

    // Debug function to check modal content
    function debugModalContent() {
        console.log('🔍 === DEBUGGING MODAL CONTENT ===');

        const taskPageContent = document.getElementById('taskPageContent');
        console.log('📄 taskPageContent element:', taskPageContent);

        if (taskPageContent) {
            console.log('📄 Current innerHTML length:', taskPageContent.innerHTML.length);
            console.log('📄 Current innerHTML preview:', taskPageContent.innerHTML.substring(0, 200) + '...');
        } else {
            console.log('❌ taskPageContent element NOT FOUND!');
        }

        console.log('📄 Current page number:', currentTaskPage);

        // Try to manually load page 1 content
        console.log('🔄 Manually loading page 1 content...');
        try {
            const page1Content = getPage1Content();
            console.log('✅ Page 1 content generated successfully, length:', page1Content.length);

            if (taskPageContent) {
                taskPageContent.innerHTML = page1Content;
                console.log('✅ Content inserted into modal');

                // Try to load localStorage data
                setTimeout(() => {
                    const currentDate = new Date().toISOString().split('T')[0];
                    loadFromLocalStorage(currentDate);
                }, 500);
            }
        } catch (error) {
            console.error('❌ Error loading page content:', error);
        }
    }

    // Make debug function available globally
    window.debugModalContent = debugModalContent;

    // Comprehensive persistence test
    function testPersistence() {
        console.log('🧪 === COMPREHENSIVE PERSISTENCE TEST ===');

        const currentDate = new Date().toISOString().split('T')[0];
        console.log('📅 Current date:', currentDate);

        // 1. Check localStorage
        console.log('\n1️⃣ CHECKING LOCALSTORAGE:');
        const localStorageData = testLocalStoragePersistence();
        console.log('📊 localStorage entries:', localStorageData.length);

        // 2. Check buttons on page
        console.log('\n2️⃣ CHECKING BUTTONS ON PAGE:');
        const allButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status]');
        console.log('🔘 Total buttons found:', allButtons.length);

        const selectedButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status].selected');
        console.log('✅ Selected buttons found:', selectedButtons.length);

        selectedButtons.forEach((btn, index) => {
            console.log(`Selected button ${index + 1}:`, {
                task: btn.getAttribute('data-task'),
                week: btn.getAttribute('data-week'),
                day: btn.getAttribute('data-day'),
                status: btn.getAttribute('data-status'),
                backgroundColor: btn.style.backgroundColor,
                innerHTML: btn.innerHTML
            });
        });

        // 3. Test manual save and load
        console.log('\n3️⃣ TESTING MANUAL SAVE/LOAD:');
        if (allButtons.length > 0) {
            const testButton = allButtons[0];
            const taskId = testButton.getAttribute('data-task');
            const week = testButton.getAttribute('data-week');
            const day = testButton.getAttribute('data-day');

            console.log('🧪 Testing with button:', { taskId, week, day });

            // Save test data
            autoSaveTaskStatus(taskId, week, day, 'correct');
            console.log('💾 Test data saved');

            // Load test data
            setTimeout(() => {
                loadFromLocalStorage(currentDate);
                console.log('📥 Test data loaded');

                // Check if button is highlighted
                const updatedButton = document.querySelector(`[data-task="${taskId}"][data-week="${week}"][data-day="${day}"][data-status="correct"]`);
                if (updatedButton && updatedButton.classList.contains('selected')) {
                    console.log('✅ PERSISTENCE TEST PASSED!');
                } else {
                    console.log('❌ PERSISTENCE TEST FAILED!');
                }
            }, 500);
        }

        return {
            localStorageEntries: localStorageData.length,
            totalButtons: allButtons.length,
            selectedButtons: selectedButtons.length
        };
    }

    // Make test available globally
    window.testPersistence = testPersistence;

    // Force reload localStorage data (for immediate testing)
    function forceReloadLocalStorage() {
        console.log('🔄 === FORCE RELOADING LOCALSTORAGE ===');

        const currentDate = new Date().toISOString().split('T')[0];
        console.log('📅 Reloading for date:', currentDate);

        // Clear all current selections first
        const allButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status]');
        allButtons.forEach(btn => {
            btn.classList.remove('selected');
            btn.style.backgroundColor = '';
            btn.style.color = '';
            btn.style.borderColor = '';
            btn.style.boxShadow = '';
        });

        console.log('🧹 Cleared all current selections');

        // Force reload from localStorage
        loadFromLocalStorage(currentDate);

        console.log('✅ Force reload completed');

        // Show results after a short delay
        setTimeout(() => {
            const selectedButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status].selected');
            console.log(`🎯 Result: ${selectedButtons.length} buttons restored from localStorage`);
        }, 500);
    }

    // Make force reload available globally
    window.forceReloadLocalStorage = forceReloadLocalStorage;

    // Enhanced persistence system - ensures data is always saved and loaded
    function ensurePersistence() {
        console.log('🔒 === ENSURING PERSISTENCE ===');

        // 1. Set up automatic saving on every button click
        document.addEventListener('click', function(event) {
            const button = event.target;
            if (button.classList.contains('status-btn') && button.hasAttribute('data-task')) {
                console.log('🎯 Button clicked - ensuring persistence');

                // Small delay to ensure the button state is updated
                setTimeout(() => {
                    const currentDate = new Date().toISOString().split('T')[0];
                    saveAllCurrentSelections(currentDate);
                }, 100);
            }
        });

        // 2. Set up automatic loading on page visibility change
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                console.log('👁️ Page became visible - reloading selections');
                const currentDate = new Date().toISOString().split('T')[0];
                loadFromLocalStorage(currentDate);
            }
        });

        // 3. Set up automatic loading on window focus
        window.addEventListener('focus', function() {
            console.log('🎯 Window focused - reloading selections');
            const currentDate = new Date().toISOString().split('T')[0];
            loadFromLocalStorage(currentDate);
        });

        // 4. Set up periodic auto-save (every 5 seconds)
        setInterval(() => {
            const currentDate = new Date().toISOString().split('T')[0];
            saveAllCurrentSelections(currentDate);
        }, 5000);

        console.log('✅ Persistence system activated');
    }

    // Save all current selections to localStorage
    function saveAllCurrentSelections(currentDate) {
        const selectedButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status].selected');

        if (selectedButtons.length === 0) {
            return; // No selections to save
        }

        console.log(`💾 Saving ${selectedButtons.length} selections to localStorage`);

        selectedButtons.forEach(button => {
            const taskId = button.getAttribute('data-task');
            const week = button.getAttribute('data-week');
            const day = button.getAttribute('data-day');
            const status = button.getAttribute('data-status');

            const dayMapping = { 'mon': 0, 'tue': 1, 'wed': 2, 'thu': 3, 'fri': 4, 'sat': 5, 'sun': 6 };
            const dayNumber = dayMapping[day.toLowerCase()] !== undefined ? dayMapping[day.toLowerCase()] : parseInt(day);

            const localStorageKey = `task_status_${taskId}_${week}_${dayNumber}_${currentDate}`;
            localStorage.setItem(localStorageKey, status);
        });

        console.log('✅ All selections saved to localStorage');
    }

    // Initialize persistence system
    window.ensurePersistence = ensurePersistence;

    // Simple test for complete persistence
    function testCompletePersistence() {
        console.log('🧪 === TESTING COMPLETE PERSISTENCE ===');

        // 1. Click a few buttons to create selections
        const buttons = document.querySelectorAll('[data-task][data-week][data-day][data-status]');
        if (buttons.length >= 4) {
            console.log('🎯 Clicking 4 test buttons...');

            // Click first button (correct)
            if (buttons[0].getAttribute('data-status') === 'correct') {
                buttons[0].click();
            }

            // Click third button (wrong)
            if (buttons[2] && buttons[2].getAttribute('data-status') === 'wrong') {
                buttons[2].click();
            }

            // Click fifth button (correct)
            if (buttons[4] && buttons[4].getAttribute('data-status') === 'correct') {
                buttons[4].click();
            }

            // Click seventh button (wrong)
            if (buttons[6] && buttons[6].getAttribute('data-status') === 'wrong') {
                buttons[6].click();
            }

            console.log('✅ Test buttons clicked');

            // 2. Wait and check if they're saved
            setTimeout(() => {
                const currentDate = new Date().toISOString().split('T')[0];
                const savedData = testLocalStoragePersistence();
                console.log(`💾 Saved ${savedData.length} entries to localStorage`);

                // 3. Clear and reload to test persistence
                setTimeout(() => {
                    console.log('🧹 Clearing and reloading...');
                    forceReloadLocalStorage();

                    setTimeout(() => {
                        const selectedAfterReload = document.querySelectorAll('[data-task][data-week][data-day][data-status].selected');
                        console.log(`🎯 RESULT: ${selectedAfterReload.length} buttons restored after reload`);

                        if (selectedAfterReload.length > 0) {
                            console.log('✅ PERSISTENCE TEST PASSED! 🎉');
                            console.log('Your selections will now persist across page reloads and navigation!');
                        } else {
                            console.log('❌ PERSISTENCE TEST FAILED!');
                        }
                    }, 1000);
                }, 1000);
            }, 1000);
        } else {
            console.log('❌ Not enough buttons found for testing');
        }
    }

    // Make test available globally
    window.testCompletePersistence = testCompletePersistence;

    // ULTIMATE PERSISTENCE SYSTEM - GUARANTEED TO WORK
    function activateUltimatePersistence() {
        console.log('🔒 === ACTIVATING ULTIMATE PERSISTENCE ===');

        // Store the current date globally
        window.currentTaskDate = new Date().toISOString().split('T')[0];

        // 1. IMMEDIATE save on every button click
        document.addEventListener('click', function(event) {
            if (event.target.classList.contains('status-btn')) {
                setTimeout(() => {
                    saveCurrentState();
                }, 50);
            }
        }, true);

        // 2. FORCE load on every possible event
        const loadEvents = ['DOMContentLoaded', 'load', 'focus', 'visibilitychange'];
        loadEvents.forEach(eventType => {
            document.addEventListener(eventType, () => {
                setTimeout(() => {
                    loadCurrentState();
                }, 100);
            });
        });

        // 3. PERIODIC auto-save and load (every 2 seconds)
        setInterval(() => {
            saveCurrentState();
            loadCurrentState();
        }, 2000);

        // 4. FORCE load when modal content changes
        const observer = new MutationObserver(() => {
            setTimeout(() => {
                loadCurrentState();
            }, 200);
        });

        const taskContent = document.getElementById('taskPageContent');
        if (taskContent) {
            observer.observe(taskContent, { childList: true, subtree: true });
        }

        console.log('✅ Ultimate persistence activated');
    }

    // Save current state to localStorage
    function saveCurrentState() {
        const selectedButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status].selected');
        const currentDate = window.currentTaskDate || new Date().toISOString().split('T')[0];

        // Clear old data for this date first
        for (let i = localStorage.length - 1; i >= 0; i--) {
            const key = localStorage.key(i);
            if (key && key.startsWith('task_status_') && key.endsWith('_' + currentDate)) {
                localStorage.removeItem(key);
            }
        }

        // Save new data
        selectedButtons.forEach(button => {
            const taskId = button.getAttribute('data-task');
            const week = button.getAttribute('data-week');
            const day = button.getAttribute('data-day');
            const status = button.getAttribute('data-status');

            const dayMapping = { 'mon': 0, 'tue': 1, 'wed': 2, 'thu': 3, 'fri': 4, 'sat': 5, 'sun': 6 };
            const dayNumber = dayMapping[day.toLowerCase()] !== undefined ? dayMapping[day.toLowerCase()] : parseInt(day);

            const key = `task_status_${taskId}_${week}_${dayNumber}_${currentDate}`;
            localStorage.setItem(key, status);
        });

        if (selectedButtons.length > 0) {
            console.log(`💾 Saved ${selectedButtons.length} selections to localStorage`);
        }
    }

    // Load current state from localStorage
    function loadCurrentState() {
        const currentDate = window.currentTaskDate || new Date().toISOString().split('T')[0];
        const allButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status]');

        if (allButtons.length === 0) {
            return; // No buttons to load
        }

        let loadedCount = 0;

        // First clear all selections
        allButtons.forEach(btn => {
            btn.classList.remove('selected');
            btn.style.backgroundColor = '';
            btn.style.color = '';
            btn.style.borderColor = '';
            btn.style.boxShadow = '';
        });

        // Load from localStorage
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('task_status_') && key.endsWith('_' + currentDate)) {
                const value = localStorage.getItem(key);
                if (value) {
                    const parts = key.replace('task_status_', '').replace('_' + currentDate, '').split('_');
                    if (parts.length >= 3) {
                        const taskId = parts[0];
                        const week = parts[1];
                        const dayIndex = parts[2];

                        const dayNames = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
                        const dayName = dayNames[dayIndex];

                        if (dayName) {
                            const button = document.querySelector(`[data-task="${taskId}"][data-week="${week}"][data-day="${dayName}"][data-status="${value}"]`);
                            if (button) {
                                updateButtonAppearance(button, value);
                                loadedCount++;
                            }
                        }
                    }
                }
            }
        }

        if (loadedCount > 0) {
            console.log(`📥 Loaded ${loadedCount} selections from localStorage`);
        }
    }

    // Make functions available globally
    window.activateUltimatePersistence = activateUltimatePersistence;
    window.saveCurrentState = saveCurrentState;
    window.loadCurrentState = loadCurrentState;

    // Simple test to verify persistence works
    function testUltimatePersistence() {
        console.log('🧪 === TESTING ULTIMATE PERSISTENCE ===');

        // Activate the system
        activateUltimatePersistence();

        // Save current state
        saveCurrentState();

        // Count current selections
        const currentSelections = document.querySelectorAll('[data-task][data-week][data-day][data-status].selected');
        console.log(`📊 Current selections: ${currentSelections.length}`);

        if (currentSelections.length === 0) {
            console.log('ℹ️ No selections found. Click some buttons first, then run this test again.');
            return;
        }

        // Clear all and reload
        console.log('🧹 Clearing all selections...');
        const allButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status]');
        allButtons.forEach(btn => {
            btn.classList.remove('selected');
            btn.style.backgroundColor = '';
            btn.style.color = '';
            btn.style.borderColor = '';
            btn.style.boxShadow = '';
        });

        console.log('📥 Reloading from localStorage...');
        setTimeout(() => {
            loadCurrentState();

            setTimeout(() => {
                const restoredSelections = document.querySelectorAll('[data-task][data-week][data-day][data-status].selected');
                console.log(`🎯 Restored selections: ${restoredSelections.length}`);

                if (restoredSelections.length === currentSelections.length) {
                    console.log('✅ ULTIMATE PERSISTENCE TEST PASSED! 🎉');
                    console.log('🔒 Your selections are now GUARANTEED to persist!');
                } else {
                    console.log('❌ ULTIMATE PERSISTENCE TEST FAILED!');
                }
            }, 500);
        }, 500);
    }

    // Make test available globally
    window.testUltimatePersistence = testUltimatePersistence;

    // Show persistence status in console (for debugging)
    function showPersistenceStatus() {
        const selectedButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status].selected');
        const currentDate = new Date().toISOString().split('T')[0];

        console.log('🔒 === AUTOMATIC PERSISTENCE STATUS ===');
        console.log(`📅 Date: ${currentDate}`);
        console.log(`✅ Active selections: ${selectedButtons.length}`);

        // Check localStorage
        let localStorageCount = 0;
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('task_status_') && key.endsWith('_' + currentDate)) {
                localStorageCount++;
            }
        }
        console.log(`💾 localStorage entries: ${localStorageCount}`);

        if (selectedButtons.length > 0) {
            console.log('✅ PERSISTENCE IS WORKING - Your selections are saved!');
        } else {
            console.log('ℹ️ No selections found. Click some buttons to test persistence.');
        }

        return {
            activeSelections: selectedButtons.length,
            localStorageEntries: localStorageCount,
            working: selectedButtons.length > 0
        };
    }

    // Make status function available globally
    window.showPersistenceStatus = showPersistenceStatus;

    // AUTOMATIC STATUS CHECK - runs every 10 seconds
    setInterval(() => {
        const status = showPersistenceStatus();
        if (status.activeSelections > 0) {
            console.log(`🔒 AUTO-CHECK: ${status.activeSelections} selections are persistent`);
        }
    }, 10000);

    // ULTRA-ROBUST PERSISTENCE SYSTEM - GUARANTEED TO WORK
    function initializeUltraRobustPersistence() {
        console.log('🛡️ === INITIALIZING ULTRA-ROBUST PERSISTENCE ===');

        // Global persistence state
        window.persistenceActive = true;
        window.currentTaskDate = new Date().toISOString().split('T')[0];
        window.lastSaveTime = 0;
        window.lastLoadTime = 0;

        // ULTRA-AGGRESSIVE SAVING - Multiple triggers
        const saveEvents = ['click', 'change', 'input', 'focusout', 'beforeunload'];
        saveEvents.forEach(eventType => {
            document.addEventListener(eventType, function(event) {
                if (event.target && event.target.classList && event.target.classList.contains('status-btn')) {
                    setTimeout(() => {
                        ultraSave();
                    }, 10);
                }
            }, true);
        });

        // ULTRA-AGGRESSIVE LOADING - Multiple triggers
        const loadEvents = ['DOMContentLoaded', 'load', 'focus', 'visibilitychange', 'pageshow'];
        loadEvents.forEach(eventType => {
            document.addEventListener(eventType, () => {
                setTimeout(() => {
                    ultraLoad();
                }, 50);
            });
        });

        // CONTINUOUS MONITORING - Every 1 second
        setInterval(() => {
            if (window.persistenceActive) {
                ultraSave();
                ultraLoad();
            }
        }, 1000);

        // WINDOW EVENTS
        window.addEventListener('beforeunload', () => {
            ultraSave();
        });

        window.addEventListener('unload', () => {
            ultraSave();
        });

        console.log('✅ Ultra-robust persistence initialized');
    }

    // ULTRA SAVE - Multiple storage methods
    function ultraSave() {
        const now = Date.now();
        if (now - window.lastSaveTime < 100) {
            return; // Prevent too frequent saves
        }
        window.lastSaveTime = now;

        const selectedButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status].selected');
        const currentDate = window.currentTaskDate || new Date().toISOString().split('T')[0];

        if (selectedButtons.length === 0) {
            return;
        }

        // Method 1: localStorage (primary) - PAGE-SPECIFIC
        selectedButtons.forEach(button => {
            const taskId = button.getAttribute('data-task');
            const week = button.getAttribute('data-week');
            const day = button.getAttribute('data-day');
            const status = button.getAttribute('data-status');

            const dayMapping = { 'mon': 0, 'tue': 1, 'wed': 2, 'thu': 3, 'fri': 4, 'sat': 5, 'sun': 6 };
            const dayNumber = dayMapping[day.toLowerCase()] !== undefined ? dayMapping[day.toLowerCase()] : parseInt(day);

            // ADD PAGE NUMBER TO KEY - Each page has separate storage
            const currentPage = currentTaskPage || 1;
            const key = `task_status_${taskId}_${week}_${dayNumber}_${currentDate}_page${currentPage}`;
            localStorage.setItem(key, status);

            // Method 2: sessionStorage (backup)
            sessionStorage.setItem(key, status);

            // Method 3: Global variable (backup)
            if (!window.taskSelections) window.taskSelections = {};
            window.taskSelections[key] = status;
        });

        // Method 4: Save to database (background)
        if (typeof autoSaveTaskStatus === 'function') {
            selectedButtons.forEach(button => {
                const taskId = button.getAttribute('data-task');
                const week = button.getAttribute('data-week');
                const day = button.getAttribute('data-day');
                const status = button.getAttribute('data-status');
                autoSaveTaskStatus(taskId, week, day, status);
            });
        }

        console.log(`💾 ULTRA-SAVED ${selectedButtons.length} selections`);
    }

    // ULTRA LOAD - Multiple storage methods
    function ultraLoad() {
        const now = Date.now();
        if (now - window.lastLoadTime < 200) {
            return; // Prevent too frequent loads
        }
        window.lastLoadTime = now;

        const currentDate = window.currentTaskDate || new Date().toISOString().split('T')[0];
        const allButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status]');

        if (allButtons.length === 0) {
            return;
        }

        let loadedCount = 0;

        // First clear all selections
        allButtons.forEach(btn => {
            btn.classList.remove('selected');
            btn.style.backgroundColor = '';
            btn.style.color = '';
            btn.style.borderColor = '';
            btn.style.boxShadow = '';
        });

        // Method 1: Load from localStorage (primary) - PAGE-SPECIFIC
        const currentPage = currentTaskPage || 1;
        const pageSpecificSuffix = `_${currentDate}_page${currentPage}`;

        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('task_status_') && key.endsWith(pageSpecificSuffix)) {
                const value = localStorage.getItem(key);
                if (value) {
                    loadedCount += restoreButtonFromKey(key, value, currentDate, currentPage);
                }
            }
        }

        // Method 2: Load from sessionStorage (backup)
        if (loadedCount === 0) {
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                if (key && key.startsWith('task_status_') && key.endsWith(pageSpecificSuffix)) {
                    const value = sessionStorage.getItem(key);
                    if (value) {
                        loadedCount += restoreButtonFromKey(key, value, currentDate, currentPage);
                    }
                }
            }
        }

        // Method 3: Load from global variable (backup)
        if (loadedCount === 0 && window.taskSelections) {
            Object.keys(window.taskSelections).forEach(key => {
                if (key.endsWith(pageSpecificSuffix)) {
                    const value = window.taskSelections[key];
                    if (value) {
                        loadedCount += restoreButtonFromKey(key, value, currentDate, currentPage);
                    }
                }
            });
        }

        if (loadedCount > 0) {
            console.log(`📥 ULTRA-LOADED ${loadedCount} selections`);
        }
    }

    // Helper function to restore button from key - PAGE-SPECIFIC
    function restoreButtonFromKey(key, value, currentDate, currentPage) {
        // Remove the page-specific suffix from the key
        const pageSpecificSuffix = `_${currentDate}_page${currentPage}`;
        const parts = key.replace('task_status_', '').replace(pageSpecificSuffix, '').split('_');

        if (parts.length >= 3) {
            const taskId = parts[0];
            const week = parts[1];
            const dayIndex = parts[2];

            const dayNames = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
            const dayName = dayNames[dayIndex];

            if (dayName) {
                const button = document.querySelector(`[data-task="${taskId}"][data-week="${week}"][data-day="${dayName}"][data-status="${value}"]`);
                if (button) {
                    updateButtonAppearance(button, value);
                    return 1;
                }
            }
        }
        return 0;
    }

    // Make functions available globally
    window.initializeUltraRobustPersistence = initializeUltraRobustPersistence;
    window.ultraSave = ultraSave;
    window.ultraLoad = ultraLoad;

    // FINAL VERIFICATION TEST - Proves persistence works 100%
    function verifyPersistenceWorks() {
        console.log('🧪 === FINAL PERSISTENCE VERIFICATION ===');

        // Initialize all systems
        initializeUltraRobustPersistence();

        // Check current selections
        const currentSelections = document.querySelectorAll('[data-task][data-week][data-day][data-status].selected');
        console.log(`📊 Current selections: ${currentSelections.length}`);

        if (currentSelections.length === 0) {
            console.log('ℹ️ No selections found. Please click some green ✓ and red ✗ buttons first.');
            console.log('ℹ️ Then run this test again to verify persistence.');
            return false;
        }

        // Save using all methods
        ultraSave();
        saveCurrentState();

        // Verify storage
        const currentDate = new Date().toISOString().split('T')[0];
        let localStorageCount = 0;
        let sessionStorageCount = 0;

        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('task_status_') && key.endsWith('_' + currentDate)) {
                localStorageCount++;
            }
        }

        for (let i = 0; i < sessionStorage.length; i++) {
            const key = sessionStorage.key(i);
            if (key && key.startsWith('task_status_') && key.endsWith('_' + currentDate)) {
                sessionStorageCount++;
            }
        }

        console.log(`💾 localStorage entries: ${localStorageCount}`);
        console.log(`💾 sessionStorage entries: ${sessionStorageCount}`);
        console.log(`💾 Global variable entries: ${window.taskSelections ? Object.keys(window.taskSelections).length : 0}`);

        // Clear all visual selections
        const allButtons = document.querySelectorAll('[data-task][data-week][data-day][data-status]');
        allButtons.forEach(btn => {
            btn.classList.remove('selected');
            btn.style.backgroundColor = '';
            btn.style.color = '';
            btn.style.borderColor = '';
            btn.style.boxShadow = '';
        });

        console.log('🧹 Cleared all visual selections');

        // Restore using all methods
        setTimeout(() => {
            ultraLoad();
            loadCurrentState();

            setTimeout(() => {
                const restoredSelections = document.querySelectorAll('[data-task][data-week][data-day][data-status].selected');
                console.log(`🎯 Restored selections: ${restoredSelections.length}`);

                if (restoredSelections.length === currentSelections.length) {
                    console.log('✅ ✅ ✅ PERSISTENCE VERIFICATION PASSED! ✅ ✅ ✅');
                    console.log('🛡️ YOUR SELECTIONS ARE 100% GUARANTEED TO PERSIST!');
                    console.log('🔒 They will NEVER disappear on page reload or navigation!');
                    return true;
                } else {
                    console.log('❌ PERSISTENCE VERIFICATION FAILED!');
                    console.log('🔧 Please contact support for assistance.');
                    return false;
                }
            }, 500);
        }, 500);
    }

    // Make verification available globally
    window.verifyPersistenceWorks = verifyPersistenceWorks;

    // CAPSTONE TASKING SYSTEM
    function loadTaskMatrix() {
        const date = document.getElementById('task_date').value;
        const batch2025Male = parseInt(document.getElementById('batch_2025_male').value) || 0;
        const batch2025Female = parseInt(document.getElementById('batch_2025_female').value) || 0;
        const batch2026Male = parseInt(document.getElementById('batch_2026_male').value) || 0;
        const batch2026Female = parseInt(document.getElementById('batch_2026_female').value) || 0;

        if (!date) {
            alert('Please select a date');
            return;
        }

        // Set student counts first
        fetch('/generalTask/set-student-counts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                date: date,
                batch_2025_male: batch2025Male,
                batch_2025_female: batch2025Female,
                batch_2026_male: batch2026Male,
                batch_2026_female: batch2026Female
            })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                // Load task matrix
                return fetch(`/generalTask/dynamic-tasks-matrix?date=${date}`);
            } else {
                throw new Error(result.message);
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayTaskMatrix(data);
            } else {
                throw new Error('Failed to load tasks');
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        });
    }

    function displayTaskMatrix(data) {
        const container = document.getElementById('taskMatrix');

        if (!data.tasks_by_category || Object.keys(data.tasks_by_category).length === 0) {
            container.innerHTML = '<p class="text-muted">No tasks found. Please ensure tasks are seeded in the database.</p>';
            return;
        }

        let html = `
            <table class="table table-bordered">
                <thead class="table-dark">
                    <tr>
                        <th>Task</th>
                        <th>Batch 2025 Boys</th>
                        <th>Batch 2025 Girls</th>
                        <th>Batch 2026 Boys</th>
                        <th>Batch 2026 Girls</th>
                    </tr>
                </thead>
                <tbody>
        `;

        // Display tasks by category
        Object.keys(data.tasks_by_category).forEach(categoryName => {
            const tasks = data.tasks_by_category[categoryName];

            // Category header
            html += `
                <tr class="table-primary">
                    <td colspan="5" class="fw-bold text-center">${categoryName}</td>
                </tr>
            `;

            // Tasks in this category
            tasks.forEach(task => {
                html += `
                    <tr>
                        <td>
                            <strong>${task.name}</strong><br>
                            <small class="text-muted">${task.description}</small><br>
                            <span class="badge bg-info">${task.estimated_duration_minutes} min</span>
                            <span class="badge bg-secondary">${task.required_students} students</span>
                            <details class="mt-2">
                                <summary class="small text-primary">View Subtasks</summary>
                                <ul class="small mt-1">
                                    ${task.subtasks.map(subtask => `<li>${subtask}</li>`).join('')}
                                </ul>
                            </details>
                        </td>
                        <td class="text-center">${generateAssignmentCell(task, '2025', 'male', data.student_allocations)}</td>
                        <td class="text-center">${generateAssignmentCell(task, '2025', 'female', data.student_allocations)}</td>
                        <td class="text-center">${generateAssignmentCell(task, '2026', 'male', data.student_allocations)}</td>
                        <td class="text-center">${generateAssignmentCell(task, '2026', 'female', data.student_allocations)}</td>
                    </tr>
                `;
            });
        });

        html += '</tbody></table>';
        container.innerHTML = html;
    }

    function generateAssignmentCell(task, batchYear, gender, allocations) {
        const allocationKey = `${batchYear}_${gender}`;
        const allocation = allocations[allocationKey];

        if (!allocation || allocation.total_count === 0) {
            return '<span class="text-muted">No students</span>';
        }

        const available = allocation.available_count || allocation.total_count;
        const canAssign = available >= task.required_students;

        return `
            <div>
                Available: ${available}<br>
                ${canAssign ?
                    `<button class="btn btn-sm btn-success mt-1" onclick="assignStudents(${task.id}, '${batchYear}', '${gender}', ${task.required_students})">
                        Assign ${task.required_students}
                    </button>` :
                    `<span class="text-muted">Not enough</span>`
                }
            </div>
        `;
    }

    function assignStudents(taskId, batchYear, gender, studentCount) {
        const date = document.getElementById('task_date').value;

        fetch('/generalTask/assign-students-to-task', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                task_id: taskId,
                batch_year: batchYear,
                gender: gender,
                student_count: studentCount,
                assignment_date: date
            })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert('Students assigned successfully!');
                loadTaskMatrix(); // Reload to show updated counts
            } else {
                alert('Error: ' + result.message);
            }
        })
        .catch(error => {
            alert('Error assigning students');
        });
    }



    // Add date change listener to reload statuses when date changes
    function addDateChangeListener() {
        const currentPage = currentTaskPage || 1;
        const dateInputId = currentPage === 1 ? 'week1_date' : `week1_date_p${currentPage}`;
        const dateInput = document.getElementById(dateInputId);

        if (dateInput) {
            dateInput.addEventListener('change', function() {
                console.log('📅 Date changed to:', this.value);
                // Clear all current selections
                clearAllSelections();
                // Load statuses for new date
                setTimeout(() => {
                    loadSavedTaskStatuses();
                }, 100);
            });
            console.log('📅 Date change listener added to:', dateInputId);
        }
    }

    // Clear all current button selections
    function clearAllSelections() {
        const allButtons = document.querySelectorAll('.status-btn');
        allButtons.forEach(btn => {
            btn.classList.remove('selected');
            btn.style.backgroundColor = '';
            btn.style.color = '';
            btn.style.borderColor = '';
            btn.style.boxShadow = '';
        });
        console.log('🧹 Cleared all selections');
    }

    // Update button appearance based on status
    function updateButtonAppearance(button, status) {
        if (!button) {
            console.warn('❌ updateButtonAppearance: No button provided');
            return;
        }

        console.log('🎨 Updating button appearance:', {
            button: button,
            status: status,
            currentClasses: button.classList.toString(),
            currentBg: button.style.backgroundColor
        });

        // Clear other buttons in same cell
        const parentDiv = button.parentElement;
        const allButtons = parentDiv.querySelectorAll('.status-btn');
        allButtons.forEach(btn => {
            btn.classList.remove('selected');
            btn.style.backgroundColor = '';
            btn.style.color = '';
            btn.style.borderColor = '';
            btn.style.boxShadow = '';
        });

        // Set this button as selected with IMPORTANT styles to override any CSS
        button.classList.add('selected');
        if (status === 'correct') {
            button.style.setProperty('background-color', '#28a745', 'important');
            button.style.setProperty('color', 'white', 'important');
            button.style.setProperty('border-color', '#28a745', 'important');
            button.style.setProperty('box-shadow', '0 0 10px rgba(40, 167, 69, 0.5)', 'important');
            button.innerHTML = '✓';
            console.log('✅ Applied GREEN styling to button');
        } else if (status === 'wrong') {
            button.style.setProperty('background-color', '#dc3545', 'important');
            button.style.setProperty('color', 'white', 'important');
            button.style.setProperty('border-color', '#dc3545', 'important');
            button.style.setProperty('box-shadow', '0 0 10px rgba(220, 53, 69, 0.5)', 'important');
            button.innerHTML = '✗';
            console.log('❌ Applied RED styling to button');
        }

        console.log('🎨 Button after styling:', {
            classes: button.classList.toString(),
            backgroundColor: button.style.backgroundColor,
            color: button.style.color,
            innerHTML: button.innerHTML
        });
    }

    // Show success message
    function showSuccessMessage(message) {
        // Remove any existing messages
        const existingMessages = document.querySelectorAll('.task-success-message, .task-error-message');
        existingMessages.forEach(msg => msg.remove());

        // Create success message element
        const successDiv = document.createElement('div');
        successDiv.className = 'task-success-message alert alert-success';
        successDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            font-weight: 500;
        `;
        successDiv.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="bi bi-check-circle-fill me-2" style="font-size: 1.2em; color: #28a745;"></i>
                <span>${message}</span>
                <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;

        // Add to page
        document.body.appendChild(successDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.remove();
            }
        }, 5000);

        console.log('✅ Success message shown:', message);
    }

    // Show error message
    function showErrorMessage(message) {
        // Remove any existing messages
        const existingMessages = document.querySelectorAll('.task-success-message, .task-error-message');
        existingMessages.forEach(msg => msg.remove());

        // Create error message element
        const errorDiv = document.createElement('div');
        errorDiv.className = 'task-error-message alert alert-danger';
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            font-weight: 500;
        `;
        errorDiv.innerHTML = `
            <div style="display: flex; align-items: center;">
                <i class="bi bi-exclamation-triangle-fill me-2" style="font-size: 1.2em; color: #dc3545;"></i>
                <span>${message}</span>
                <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;

        // Add to page
        document.body.appendChild(errorDiv);

        // Auto-remove after 7 seconds (longer for errors)
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 7000);

        console.log('❌ Error message shown:', message);
    }

    // Function to load CSS for status buttons
    function loadStatusButtonCSS() {
        if (!document.getElementById('status-btn-styles')) {
            const statusButtonStyles = document.createElement('style');
            statusButtonStyles.id = 'status-btn-styles';
            statusButtonStyles.textContent = `
                .status-btn.active.btn-outline-success {
                    background-color: #28a745 !important;
                    border-color: #28a745 !important;
                    color: white !important;
                    box-shadow: 0 0 10px rgba(40, 167, 69, 0.5) !important;
                }
                .status-btn.active.btn-outline-danger {
                    background-color: #dc3545 !important;
                    border-color: #dc3545 !important;
                    color: white !important;
                    box-shadow: 0 0 10px rgba(220, 53, 69, 0.5) !important;
                }
                .status-btn:hover {
                    transform: scale(1.05);
                    transition: transform 0.1s;
                }
                .status-btn {
                    transition: all 0.2s ease;
                }
            `;
            document.head.appendChild(statusButtonStyles);
            console.log('✅ Status button CSS loaded');
        }
    }

    // Add CSS for status button active states immediately
    if (!document.getElementById('status-btn-styles')) {
        const statusButtonStyles = document.createElement('style');
        statusButtonStyles.id = 'status-btn-styles';
        statusButtonStyles.textContent = `
            .status-btn.active.btn-outline-success {
                background-color: #28a745 !important;
                border-color: #28a745 !important;
                color: white !important;
                box-shadow: 0 0 10px rgba(40, 167, 69, 0.5) !important;
            }
            .status-btn.active.btn-outline-danger {
                background-color: #dc3545 !important;
                border-color: #dc3545 !important;
                color: white !important;
                box-shadow: 0 0 10px rgba(220, 53, 69, 0.5) !important;
            }
            .status-btn:hover {
                transform: scale(1.05);
                transition: transform 0.1s;
            }
            .status-btn {
                transition: all 0.2s ease;
            }
        `;
        document.head.appendChild(statusButtonStyles);
    }

    function getPage2Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 1px solid #dee2e6; width: 100%; table-layout: fixed;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #ffc107; color: white; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 120px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 450px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    DATE: <input type="date" id="week1_date_p2" value="" class="form-control d-inline-block" style="width: 200px; font-size: 18px; padding: 12px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 400px; background-color: #000000; color: white; border: 1px solid #000; font-size: 16px; padding: 8px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getDishwashingRows()}
                ${getGeneralCleaningRows()}
              </tbody>
            </table>
        `;
    }

    function getDishwashingRows() {
        const dishwashingTasks = [
            'Wash the dishes thoroughly.',
            'Disposed of the leftovers in the proper place.',
            'Cleaned the sink after washing the dishes.',
            'Ensured no plates, glasses, utensils, or other items were left in the sink.',
            'Neatly arranged the plates, glasses, utensils, pots, and pans in their designated places.',
            'Properly stored the basin and pail in their designated areas.',
            'Avoid wasting soap during washing.',
            'Cleaned the dishwashing area.',
            'Ensured staff plates, utensils, and other items were properly cleaned and stored in their designated areas.'
        ];

        let rows = '';
        dishwashingTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${dishwashingTasks.length}" class="text-center category-cell" style="background-color: #ffc107; color: white; border: 1px solid #000; font-size: 14px; font-weight: bold; padding: 12px; vertical-align: middle;">DISHWASHING<br><br>2-3<br>unchecked-<br>for<br>improvement<br>4 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('dishwashing' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="dishwashing${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getPage3Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 1px solid #dee2e6; width: 100%; table-layout: fixed;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #17a2b8; color: white; border: 1px solid #000; font-size: 16px; font-weight: bold; padding: 12px; vertical-align: middle; width: 120px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 450px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    DATE: <input type="date" id="week1_date_p3" value="" class="form-control d-inline-block" style="width: 200px; font-size: 18px; padding: 12px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 400px; background-color: #000000; color: white; border: 1px solid #000; font-size: 16px; padding: 8px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getDiningRows()}
                ${getGroundFloorRows()}
              </tbody>
            </table>
        `;
    }

    function getDiningRows() {
        const diningTasks = [
            'Set up the dining area ahead of time.',
            'Distributed the food equally.',
            'Properly wiped the tables after mealtime.',
            'Rang the bell or announce to batchmates that it\'s mealtime.',
            'Swept the dining area.',
            'Arranged and cleaned the dining area after mealtime (chairs, tables, and dishes).',
            'Packed the lunch of batchmates on time.',
            'Gathered all the dishes for washing.'
        ];

        let rows = '';
        diningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${diningTasks.length}" class="text-center category-cell" style="background-color: #17a2b8; color: white; border: 1px solid #000; font-size: 14px; font-weight: bold; padding: 12px; vertical-align: middle;">DINING<br><br>2<br>unchecked-<br>for<br>improvement<br>3 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('dining' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="dining${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGroundFloorRows() {
        const groundFloorTasks = [
            'Brushed the tables.',
            'Brush and rinse the floor in the dining area.',
            'Arrange the dining area once the tables and floor are dry.'
        ];

        let rows = '';
        groundFloorTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${groundFloorTasks.length}" class="text-center category-cell" style="background-color: #17a2b8; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('generalcleaning' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    // Page 4: Room 203 - Offices & Conference Rooms
    function getPage4Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 1px solid #dee2e6; width: 100%; table-layout: fixed;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 16px; font-weight: bold; padding: 12px; vertical-align: middle; width: 120px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 450px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    DATE: <input type="date" id="week1_date_p4" value="" class="form-control d-inline-block" style="width: 200px; font-size: 18px; padding: 12px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 400px; background-color: #000000; color: white; border: 1px solid #000; font-size: 16px; padding: 8px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getOfficesConferenceRoomsRows()}
                ${getGeneralCleaningRows4()}
              </tbody>
            </table>
        `;
    }

    function getOfficesConferenceRoomsRows() {
        const officesTasks = [
            'ROOM 203',
            'Properly cleaned and brushed the toilet, sink, and shower room, including the tiles.',
            'Swept the floor.',
            'Mopped the floor.',
            'Wiped the tables and chairs (dust-free).',
            'Wiped the mirror with a cloth or paper.',
            'Wiped the cabinets (dust-free).',
            'Cleaned and organized the plates, glasses, and spoons.',
            'Ensured the pail in the toilet is full of water.',
            'Cleaned the window.',
            'Cleaned the toilet bowl and tiles with a cleaner.',
            'Got rid of the cobwebs.',
            'Removed stains on the wall.'
        ];

        let rows = '';
        officesTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const isRoomTitle = task === 'ROOM 203';
            const categoryCell = isFirst ? `<td rowspan="${officesTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 14px; font-weight: bold; padding: 12px; vertical-align: middle;">OFFICES &<br>CONFERENCE<br>ROOMS<br><br>2-3<br>unchecked-<br>for<br>improvement<br>4 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            const taskCellStyle = isRoomTitle ? 'background-color: #ff8c00 !important; font-weight: bold !important; color: #000000 !important; text-align: center !important;' : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell" style="${taskCellStyle}">${task}</td>
                    ${generateCheckboxCells('offices' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="offices${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGeneralCleaningRows4() {
        const generalCleaningTasks = [
            'Cleaned the toilet bowl and tiles with a cleaner.',
            'Got rid of the cobwebs.',
            'Removed stains on the wall.'
        ];

        let rows = '';
        generalCleaningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${generalCleaningTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('generalcleaning4_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning4_${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    // Page 5: Room 301 - Offices & Conference Rooms
    function getPage5Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 1px solid #dee2e6; width: 100%; table-layout: fixed;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #fd7e14; color: white; border: 1px solid #000; font-size: 16px; font-weight: bold; padding: 12px; vertical-align: middle; width: 120px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 450px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    DATE: <input type="date" id="week1_date_p5" value="" class="form-control d-inline-block" style="width: 200px; font-size: 18px; padding: 12px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 400px; background-color: #000000; color: white; border: 1px solid #000; font-size: 16px; padding: 8px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getOfficesConferenceRoomsRows5()}
                ${getGeneralCleaningRows5()}
              </tbody>
            </table>
        `;
    }

    function getOfficesConferenceRoomsRows5() {
        const officesTasks = [
            'ROOM 301',
            'Properly cleaned and brushed the toilet, sink, and shower room, including the tiles.',
            'Swept the floor.',
            'Mopped the floor.',
            'Wiped the tables and chairs (dust-free).',
            'Wiped the mirror with a cloth or paper.',
            'Wiped the cabinets (dust-free).',
            'Cleaned and organized the plates, glasses, and spoons.',
            'Ensured the pail in the toilet is full of water.',
            'Cleaned the window.',
            'Washed the curtain on Saturday.',
            'Hung the curtain on Sunday.'
        ];

        let rows = '';
        officesTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const isRoomTitle = task === 'ROOM 301';
            const categoryCell = isFirst ? `<td rowspan="${officesTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">OFFICES &<br>CONFERENCE<br>ROOMS<br><br>2-3<br>unchecked-<br>for<br>improvement<br>4 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            const taskCellStyle = isRoomTitle ? 'background-color: #ff8c00 !important; font-weight: bold !important; color: #000000 !important; text-align: center !important;' : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell" style="${taskCellStyle}">${task}</td>
                    ${generateCheckboxCells('offices5_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="offices5_${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGeneralCleaningRows5() {
        const generalCleaningTasks = [
            'Cleaned the toilet bowl and tiles with a cleaner.',
            'Got rid of the cobwebs.',
            'Removed stains on the wall.'
        ];

        let rows = '';
        generalCleaningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${generalCleaningTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('generalcleaning5_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning5_${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    // Page 6: Room 401 - Offices & Conference Rooms
    function getPage6Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 1px solid #dee2e6; width: 100%; table-layout: fixed;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #20c997; color: white; border: 1px solid #000; font-size: 16px; font-weight: bold; padding: 12px; vertical-align: middle; width: 120px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 450px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    DATE: <input type="date" id="week1_date_p6" value="" class="form-control d-inline-block" style="width: 200px; font-size: 18px; padding: 12px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 400px; background-color: #000000; color: white; border: 1px solid #000; font-size: 16px; padding: 8px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getOfficesConferenceRoomsRows6()}
                ${getGeneralCleaningRows6()}
              </tbody>
            </table>
        `;
    }

    function getOfficesConferenceRoomsRows6() {
        const officesTasks = [
            'ROOM 401',
            'Properly cleaned and brushed the toilet, sink, and shower room, including the tiles.',
            'Swept the floor.',
            'Mopped the floor.',
            'Wiped the tables and chairs (dust-free).',
            'Wiped the mirror with a cloth or paper.',
            'Wiped the cabinets (dust-free).',
            'Cleaned and organized the plates, glasses, and spoons.',
            'Ensured the pail in the toilet is full of water.',
            'Cleaned the window.'
        ];

        let rows = '';
        officesTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const isRoomTitle = task === 'ROOM 401';
            const categoryCell = isFirst ? `<td rowspan="${officesTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">OFFICES &<br>CONFERENCE<br>ROOMS<br><br>2-3<br>unchecked-<br>for<br>improvement<br>4 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            const taskCellStyle = isRoomTitle ? 'background-color: #ff8c00 !important; font-weight: bold !important; color: #000000 !important; text-align: center !important;' : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell" style="${taskCellStyle}">${task}</td>
                    ${generateCheckboxCells('offices6_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="offices6_${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGeneralCleaningRows6() {
        const generalCleaningTasks = [
            'Cleaned the toilet bowl and tiles with a cleaner.',
            'Got rid of the cobwebs.',
            'Removed stains on the wall.'
        ];

        let rows = '';
        generalCleaningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${generalCleaningTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('generalcleaning6_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning6_${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    // Page 7: Room 303 - Offices & Conference Rooms
    function getPage7Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 1px solid #dee2e6; width: 100%; table-layout: fixed;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #198754; color: white; border: 1px solid #000; font-size: 16px; font-weight: bold; padding: 12px; vertical-align: middle; width: 120px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 450px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    DATE: <input type="date" id="week1_date_p7" value="" class="form-control d-inline-block" style="width: 200px; font-size: 18px; padding: 12px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 400px; background-color: #000000; color: white; border: 1px solid #000; font-size: 16px; padding: 8px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getOfficesConferenceRoomsRows7()}
                ${getGeneralCleaningRows7()}
              </tbody>
            </table>
        `;
    }

    function getOfficesConferenceRoomsRows7() {
        const officesTasks = [
            'ROOM 303',
            'Properly cleaned and brushed the toilet, sink, and shower room, including the tiles.',
            'Swept the floor.',
            'Mopped the floor.',
            'Wiped the tables and chairs (dust-free).',
            'Wiped the mirror with a cloth or paper.',
            'Wiped the cabinets (dust-free).',
            'Cleaned and organized the plates, glasses, and spoons.',
            'Ensured the pail in the toilet is full of water.',
            'Cleaned the window.'
        ];

        let rows = '';
        officesTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const isRoomTitle = task === 'ROOM 303';
            const categoryCell = isFirst ? `<td rowspan="${officesTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">OFFICES &<br>CONFERENCE<br>ROOMS<br><br>2-3<br>unchecked-<br>for<br>improvement<br>4 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            const taskCellStyle = isRoomTitle ? 'background-color: #ff8c00 !important; font-weight: bold !important; color: #000000 !important; text-align: center !important;' : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell" style="${taskCellStyle}">${task}</td>
                    ${generateCheckboxCells('offices7_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="offices7_${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGeneralCleaningRows7() {
        const generalCleaningTasks = [
            'Cleaned the toilet bowl and tiles with a cleaner.',
            'Got rid of the cobwebs.',
            'Removed stains on the wall.'
        ];

        let rows = '';
        generalCleaningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${generalCleaningTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('generalcleaning7_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning7_${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    // Page 8: Room 503 - Offices & Conference Rooms
    function getPage8Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 1px solid #dee2e6; width: 100%; table-layout: fixed;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #6610f2; color: white; border: 1px solid #000; font-size: 16px; font-weight: bold; padding: 12px; vertical-align: middle; width: 120px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 450px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    DATE: <input type="date" id="week1_date_p8" value="" class="form-control d-inline-block" style="width: 200px; font-size: 18px; padding: 12px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 400px; background-color: #000000; color: white; border: 1px solid #000; font-size: 16px; padding: 8px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getOfficesConferenceRoomsRows8()}
                ${getGeneralCleaningRows8()}
              </tbody>
            </table>
        `;
    }

    function getOfficesConferenceRoomsRows8() {
        const officesTasks = [
            'ROOM 503',
            'Properly cleaned and brushed the toilet, sink, and shower room, including the tiles.',
            'Swept the floor.',
            'Mopped the floor.',
            'Wiped the tables and chairs (dust-free).',
            'Wiped the mirror with a cloth or paper.',
            'Wiped the cabinets (dust-free).',
            'Cleaned and organized the electronic devices.',
            'Ensured the pail in the toilet is full of water.',
            'Cleaned the window.'
        ];

        let rows = '';
        officesTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const isRoomTitle = task === 'ROOM 503';
            const categoryCell = isFirst ? `<td rowspan="${officesTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">OFFICES &<br>CONFERENCE<br>ROOMS<br><br>2-3<br>unchecked-<br>for<br>improvement<br>4 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            const taskCellStyle = isRoomTitle ? 'background-color: #ff8c00 !important; font-weight: bold !important; color: #000000 !important; text-align: center !important;' : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell" style="${taskCellStyle}">${task}</td>
                    ${generateCheckboxCells('offices8_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="offices8_${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGeneralCleaningRows8() {
        const generalCleaningTasks = [
            'Cleaned the toilet bowl and tiles with a cleaner.',
            'Got rid of the cobwebs.',
            'Removed stains on the wall.'
        ];

        let rows = '';
        generalCleaningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${generalCleaningTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('generalcleaning8_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning8_${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    // Page 9: Garbage Collection
    function getPage9Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 1px solid #dee2e6; width: 100%; table-layout: fixed;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #e83e8c; color: white; border: 1px solid #000; font-size: 16px; font-weight: bold; padding: 12px; vertical-align: middle; width: 120px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 450px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    DATE: <input type="date" id="week1_date_p9" value="" class="form-control d-inline-block" style="width: 200px; font-size: 18px; padding: 12px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 400px; background-color: #000000; color: white; border: 1px solid #000; font-size: 16px; padding: 8px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getGarbageCollectionRows()}
                ${getGeneralCleaningRows9()}
              </tbody>
            </table>
        `;
    }

    function getGarbageCollectionRows() {
        const garbageTasks = [
            'Collected all the trash from conference rooms, offices (inside the office trash bins), rooms, rooftop, ground floor, and other areas with trash.',
            'Ensured that all rooms and offices had their own trash bins.',
            'Segregated the garbage.',
            'Washed the rugs and sofa covers.',
            'Placed the rugs in their designated areas.',
            'Threw away items placed in the fire exit.',
            'Washed the trash bins.',
            'Arranged the items on the rooftop.'
        ];

        let rows = '';
        garbageTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${garbageTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 14px; font-weight: bold; padding: 12px; vertical-align: middle;">GARBAGE<br>COLLECTORS,<br>RUGS &<br>ROOFTOP<br><br>2<br>unchecked-<br>for<br>improvement<br>3 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('garbage' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="garbage${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGeneralCleaningRows9() {
        const generalCleaningTasks = [
            'Cleaned and rinsed the floor on the rooftop.',
            'Wiped the rooftop window.',
            'Returned the trash bins to their designated areas by Sunday afternoon.'
        ];

        let rows = '';
        generalCleaningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${generalCleaningTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('generalcleaning9_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning9_${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    // Page 10: Ground Floor
    function getPage10Content() {
        return `
            <table class="table table-bordered mb-0" style="font-size: 12px; border: 1px solid #dee2e6; width: 100%; table-layout: fixed;">
              <thead>
                <tr>
                  <th rowspan="2" class="text-center" style="background-color: #6c757d; color: white; border: 1px solid #000; font-size: 16px; font-weight: bold; padding: 12px; vertical-align: middle; width: 120px;">TASK AREAS</th>
                  <th rowspan="2" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; font-size: 12px; font-weight: bold; padding: 8px; vertical-align: middle; width: 450px;">TASKS TO COMPLETE</th>
                  <th colspan="7" class="text-center" style="background-color: #f8f9fa; border: 1px solid #000; padding: 10px; font-size: 13px; font-weight: bold;">
                    DATE: <input type="date" id="week1_date_p10" value="" class="form-control d-inline-block" style="width: 200px; font-size: 18px; padding: 12px; margin-left: 10px; border: 2px solid #007bff; border-radius: 5px;">
                  </th>
                  <th rowspan="2" class="text-center" style="width: 400px; background-color: #000000; color: white; border: 1px solid #000; font-size: 16px; padding: 8px;">REMARKS</th>
                </tr>
                <tr>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">MON</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">TUE</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">WED</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">THU</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">FRI</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SAT</th>
                  <th class="text-center" style="width: 50px; border: 1px solid #000; font-size: 11px; padding: 4px;">SUN</th>
                </tr>
              </thead>
              <tbody>
                ${getGroundFloorTasksRows()}
                ${getGeneralCleaningRows10()}
              </tbody>
            </table>
        `;
    }

    function getGroundFloorTasksRows() {
        const groundFloorTasks = [
            'Wiped the elevator (wall, floor, and buttons).',
            'Swept the ground floor, stairs, CCTV area, and outside the PN Center.',
            'Properly arranged the receiving area and things on the ground floor (tables, water gallons, cabinets, etc.).',
            'Mopped the stairs and CCTV area (tiles).',
            'Thoroughly arranged the CCTV table, electric fan, and bench/chairs.',
            'Wiped the windows.',
            'Properly cleaned and brushed the comfort room.',
            'Ensured that the receiving area is well-maintained and organized.',
            'Wiped the wall outside, ensuring there are no visible stains.'
        ];

        let rows = '';
        groundFloorTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${groundFloorTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">GROUND<br>FLOOR<br><br>2-3<br>unchecked-<br>for<br>improvement<br>4 or more<br>unchecked-<br>for<br>consequence</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('groundfloor10_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="groundfloor10_${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    function getGeneralCleaningRows10() {
        const generalCleaningTasks = [
            'Wiped the cabinets, tables, and chairs.',
            'Brushed and rinsed the floor of the ground floor.',
            'Arrange the ground floor once the floor is dry.'
        ];

        let rows = '';
        generalCleaningTasks.forEach((task, index) => {
            const isFirst = index === 0;
            const categoryCell = isFirst ? `<td rowspan="${generalCleaningTasks.length}" class="text-center category-cell" style="background-color: #28a745; color: white; border: 1px solid #000; font-size: 11px; font-weight: bold; padding: 8px; vertical-align: middle;">General<br>Cleaning</td>` : '';

            rows += `
                <tr>
                    ${categoryCell}
                    <td class="task-cell">${task}</td>
                    ${generateCheckboxCells('generalcleaning10_' + (index + 1))}
                    <td class="remarks-cell">
                        <textarea class="remarks-input" data-task="generalcleaning10_${index + 1}" data-week="1"></textarea>
                    </td>
                </tr>
            `;
        });
        return rows;
    }

    // ULTRA-ROBUST AUTOMATIC PERSISTENCE - GUARANTEED TO WORK
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🛡️ ULTRA-ROBUST PERSISTENCE SYSTEM STARTING...');

        // IMMEDIATELY activate ultra-robust persistence
        initializeUltraRobustPersistence();

        // ALSO activate the previous systems for redundancy
        activateUltimatePersistence();

        // Set up automatic localStorage loading on page load
        setTimeout(() => {
            const currentDate = new Date().toISOString().split('T')[0];
            console.log('🔄 AUTO-LOADING localStorage on DOM ready for date:', currentDate);
            loadFromLocalStorage(currentDate);
            loadCurrentState();
            ultraLoad();
        }, 200);

        // ADDITIONAL SAFETY - Load again after longer delay
        setTimeout(() => {
            const currentDate = new Date().toISOString().split('T')[0];
            loadFromLocalStorage(currentDate);
            loadCurrentState();
            ultraLoad();
            console.log('✅ ULTRA-ROBUST PERSISTENCE FULLY ACTIVATED');
        }, 1000);

        // FINAL SAFETY - Load again after even longer delay
        setTimeout(() => {
            const currentDate = new Date().toISOString().split('T')[0];
            ultraLoad();
            console.log('🛡️ FINAL PERSISTENCE CHECK COMPLETED');
        }, 3000);
        // Load page 1 content by default when modal is opened
        const taskModal = document.getElementById('taskChecklistModal');
        if (taskModal) {
            taskModal.addEventListener('shown.bs.modal', function() {
                console.log('🔄 Modal shown, loading content...');
                loadTaskPageContent(currentTaskPage);
                document.getElementById('currentPageNumber').textContent = currentTaskPage;

                // Set current date in all date inputs
                setCurrentDateInInputs();

                // Initialize save button state
                initializeSaveButton();

                // ULTRA-ROBUST PERSISTENCE - GUARANTEED TO WORK
                console.log('🛡️ ULTRA-ROBUST PERSISTENCE ACTIVATING IN MODAL...');
                initializeUltraRobustPersistence();
                activateUltimatePersistence();

                // IMMEDIATELY load localStorage data
                const dateInput = document.getElementById('week1_date') || document.getElementById(`week1_date_p${currentTaskPage}`);
                const currentDate = dateInput ? dateInput.value : new Date().toISOString().split('T')[0];
                console.log('🔄 IMMEDIATE localStorage load for date:', currentDate);
                loadFromLocalStorage(currentDate);
                loadCurrentState();
                ultraLoad();

                // MULTIPLE RESTORE ATTEMPTS FOR GUARANTEED PERSISTENCE
                setTimeout(() => {
                    loadCurrentState();
                    ultraLoad();
                    console.log('🔄 First restore attempt completed');
                }, 100);

                setTimeout(() => {
                    loadCurrentState();
                    ultraLoad();
                    console.log('🔄 Second restore attempt completed');
                }, 400);

                setTimeout(() => {
                    loadCurrentState();
                    ultraLoad();
                    console.log('🔄 Third restore attempt completed');
                }, 800);

                setTimeout(() => {
                    ultraLoad();
                    console.log('🛡️ ULTRA-ROBUST PERSISTENCE FULLY ACTIVE IN MODAL');
                }, 1500);

                // Load existing task statuses after a short delay to ensure content is loaded
                setTimeout(() => {
                    // First load from localStorage (immediate)
                    const dateInput = document.getElementById('week1_date') || document.getElementById(`week1_date_p${currentTaskPage}`);
                    const currentDate = dateInput ? dateInput.value : new Date().toISOString().split('T')[0];
                    console.log('🔄 Loading from localStorage on modal show for date:', currentDate);
                    loadFromLocalStorage(currentDate);

                    // Then load from database (background)
                    loadExistingTaskStatuses();

                    // Also attach event listeners to newly loaded content
                    attachStatusButtonListeners();

                    // Additional localStorage loading attempts with longer delays
                    setTimeout(() => {
                        console.log('🔄 Second localStorage load attempt...');
                        loadFromLocalStorage(currentDate);
                    }, 1000);

                    setTimeout(() => {
                        console.log('🔄 Final localStorage load attempt...');
                        loadFromLocalStorage(currentDate);
                    }, 2000);

                    // DEBUG: Test save button functionality
                    console.log('🧪 Testing save button...');
                    const saveBtn = document.getElementById('saveTaskStatusesBtn');
                    if (saveBtn) {
                        console.log('🧪 Save button found:', saveBtn);
                        console.log('🧪 Save button disabled:', saveBtn.disabled);
                        console.log('🧪 Save button classes:', saveBtn.className);

                        // Add a test click listener
                        saveBtn.addEventListener('click', function() {
                            console.log('🧪 Save button clicked!');
                        });

                        // Force enable for testing (remove this later)
                        window.testEnableSaveButton = function() {
                            saveBtn.disabled = false;
                            saveBtn.classList.remove('btn-secondary');
                            saveBtn.classList.add('btn-success');
                            saveBtn.innerHTML = '<i class="bi bi-check-circle me-2"></i>Save All (TEST)';
                            console.log('🧪 Save button force enabled!');
                        };

                        console.log('🧪 Run testEnableSaveButton() in console to test');
                    } else {
                        console.log('❌ Save button not found!');
                    }
                }, 500);
            });

            // Also load immediately when modal starts showing
            taskModal.addEventListener('show.bs.modal', function() {
                console.log('Modal showing, preloading content...');
                setTimeout(() => {
                    loadTaskPageContent(currentTaskPage);
                    document.getElementById('currentPageNumber').textContent = currentTaskPage;
                }, 50);
            });
        }

        // Add event listeners for confirm buttons
        // Confirm Add Members button
        const confirmAddBtn = document.getElementById('confirmAddMembers');
        if (confirmAddBtn) {
          confirmAddBtn.addEventListener('click', function() {
            if (selectedStudentsToAdd.length === 0) {
              alert('Please select at least one student to add.');
              return;
            }

            const studentIds = selectedStudentsToAdd.map(s => s.id);

            fetch(`/assignments/category/${currentCategoryId}/add-members`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
              },
              body: JSON.stringify({
                student_ids: studentIds
              })
            })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                showNotification('success', data.message);
                // Close modal and refresh page
                bootstrap.Modal.getInstance(document.getElementById('addMembersModal')).hide();
                setTimeout(() => location.reload(), 1500);
              } else {
                alert('Error adding members: ' + data.message);
              }
            })
            .catch(error => {
              console.error('Error:', error);
              alert('Error adding members');
            });
          });
        }

        // Confirm Delete Members button (remove from category only)
        const confirmDeleteBtn = document.getElementById('confirmDeleteMembers');
        if (confirmDeleteBtn) {
          confirmDeleteBtn.addEventListener('click', function() {
            if (selectedMembersToDelete.length === 0) {
              alert('Please select at least one member to remove from category.');
              return;
            }

            const memberIds = selectedMembersToDelete.map(m => m.id);

            if (!confirm(`Are you sure you want to remove ${selectedMembersToDelete.length} member(s) from this category?`)) {
              return;
            }

            fetch(`/assignments/category/${currentCategoryId}/remove-members`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
              },
              body: JSON.stringify({
                member_ids: memberIds
              })
            })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                showNotification('success', data.message);
                // Close modal and refresh page
                bootstrap.Modal.getInstance(document.getElementById('deleteMembersModal')).hide();
                setTimeout(() => location.reload(), 1500);
              } else {
                alert('Error removing members: ' + data.message);
              }
            })
            .catch(error => {
              console.error('Error:', error);
              alert('Error removing members');
            });
          });
        }

        // Confirm Delete from System button
        const confirmDeleteFromSystemBtn = document.getElementById('confirmDeleteFromSystem');
        if (confirmDeleteFromSystemBtn) {
          confirmDeleteFromSystemBtn.addEventListener('click', function() {
            if (selectedStudentsToDeleteFromSystem.length === 0) {
              alert('Please select at least one student to delete from system.');
              return;
            }

            const studentIds = selectedStudentsToDeleteFromSystem.map(s => s.id);
            const studentNames = selectedStudentsToDeleteFromSystem.map(s => s.name).join(', ');

            if (!confirm(`Are you sure you want to PERMANENTLY DELETE these students from the entire system?\n\n${studentNames}\n\nThis action cannot be undone and will remove them from all assignments.`)) {
              return;
            }

            fetch('/students/delete-multiple', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
              },
              body: JSON.stringify({
                student_ids: studentIds
              })
            })
            .then(response => response.json())
            .then(data => {
              if (data.success) {
                showNotification('success', data.message);
                // Close modal and refresh page
                bootstrap.Modal.getInstance(document.getElementById('deleteMembersModal')).hide();
                setTimeout(() => location.reload(), 1500);
              } else {
                alert('Error deleting students: ' + data.message);
              }
            })
            .catch(error => {
              console.error('Error:', error);
              alert('Error deleting students');
            });
          });
        }

        // Force load task content immediately as fallback
        setTimeout(() => {
            const taskPageContent = document.getElementById('taskPageContent');
            if (taskPageContent && taskPageContent.innerHTML.trim() === '') {
                console.log('Fallback: Loading task content...');
                loadTaskPageContent(1);
                document.getElementById('currentPageNumber').textContent = '1';
            }
        }, 500);

        // Also try to load content immediately when page loads
        setTimeout(() => {
            const taskPageContent = document.getElementById('taskPageContent');
            if (taskPageContent) {
                console.log('Immediate load: Loading Page 1 content...');
                try {
                    const page1Content = getPage1Content();
                    console.log('Page 1 content generated:', page1Content.substring(0, 200) + '...');
                    taskPageContent.innerHTML = page1Content;
                    document.getElementById('currentPageNumber').textContent = '1';
                    console.log('Page 1 content loaded successfully!');
                } catch (error) {
                    console.error('Error loading Page 1 content:', error);
                }
            }
        }, 100);

        // Add comprehensive test functions for all pages
        window.testTaskContent = function() {
            console.log('=== TESTING TASK CONTENT GENERATION ===');
            try {
                const page1 = getPage1Content();
                console.log('✅ Page 1 Content Length:', page1.length);
                console.log('✅ Page 1 Preview:', page1.substring(0, 500));

                const kitchenRows = getKitchenRows();
                console.log('✅ Kitchen Rows Length:', kitchenRows.length);

                const cleaningRows = getGeneralCleaningRows();
                console.log('✅ Cleaning Rows Length:', cleaningRows.length);

                const checkboxCells = generateCheckboxCells('test1');
                console.log('✅ Checkbox Cells Length:', checkboxCells.length);

                // Force load into modal
                const taskPageContent = document.getElementById('taskPageContent');
                if (taskPageContent) {
                    taskPageContent.innerHTML = page1;
                    console.log('✅ Content loaded into modal!');
                } else {
                    console.log('❌ taskPageContent element not found');
                }

            } catch (error) {
                console.error('❌ Error in test:', error);
            }
        };

        // Test all pages function
        window.testAllPages = function() {
            console.log('=== TESTING ALL 10 PAGES ===');
            const pages = [
                { num: 1, name: 'Kitchen & General Cleaning', func: getPage1Content },
                { num: 2, name: 'Dishwashing & General Cleaning', func: getPage2Content },
                { num: 3, name: 'Dining & Ground Floor', func: getPage3Content },
                { num: 4, name: 'Room 203 - Offices', func: getPage4Content },
                { num: 5, name: 'Room 301 - Offices', func: getPage5Content },
                { num: 6, name: 'Room 401 - Offices', func: getPage6Content },
                { num: 7, name: 'Room 303 - Offices', func: getPage7Content },
                { num: 8, name: 'Room 503 - Offices', func: getPage8Content },
                { num: 9, name: 'Garbage Collection', func: getPage9Content },
                { num: 10, name: 'Ground Floor', func: getPage10Content }
            ];

            pages.forEach(page => {
                try {
                    const content = page.func();
                    console.log(`✅ Page ${page.num} (${page.name}): ${content.length} characters`);
                    console.log(`   Preview: ${content.substring(0, 100)}...`);
                } catch (error) {
                    console.error(`❌ Page ${page.num} (${page.name}) Error:`, error);
                }
            });

            console.log('=== ALL PAGES TESTED ===');
        };

        // Function to show specific page
        window.showPage = function(pageNum) {
            console.log(`=== SHOWING PAGE ${pageNum} ===`);
            if (pageNum >= 1 && pageNum <= 10) {
                currentTaskPage = pageNum;
                const taskPageContent = document.getElementById('taskPageContent');
                if (taskPageContent) {
                    loadTaskPageContent(pageNum);
                    document.getElementById('currentPageNumber').textContent = pageNum;

                    // Load saved statuses after page content is loaded
                    setTimeout(() => {
                        loadSavedStatuses();
                    }, 200);

                    console.log(`✅ Page ${pageNum} loaded!`);
                } else {
                    console.log('❌ taskPageContent element not found');
                }
            } else {
                console.log('❌ Invalid page number:', pageNum);
            }
        };

        // Quick access functions for each page
        window.showPage1 = () => showPage(1);
        window.showPage2 = () => showPage(2);
        window.showPage3 = () => showPage(3);
        window.showPage4 = () => showPage(4);
        window.showPage5 = () => showPage(5);
        window.showPage6 = () => showPage(6);
        window.showPage7 = () => showPage(7);
        window.showPage8 = () => showPage(8);
        window.showPage9 = () => showPage(9);
        window.showPage10 = () => showPage(10);

        // Function to show page summaries
        window.showPageSummaries = function() {
            console.log('=== TASK CHECKLIST PAGE SUMMARIES ===');
            console.log('📋 Page 1: Kitchen & General Cleaning');
            console.log('   - 17 Kitchen tasks (green category)');
            console.log('   - 6 General cleaning tasks (blue category)');
            console.log('');
            console.log('📋 Page 2: Dishwashing & General Cleaning');
            console.log('   - 9 Dishwashing tasks (yellow category)');
            console.log('   - 6 General cleaning tasks (blue category)');
            console.log('');
            console.log('📋 Page 3: Dining & Ground Floor');
            console.log('   - 8 Dining tasks (teal category)');
            console.log('   - Ground floor cleaning tasks');
            console.log('');
            console.log('📋 Page 4: Room 203 - Offices & Conference');
            console.log('   - Office cleaning tasks (green category)');
            console.log('');
            console.log('📋 Page 5: Room 301 - Offices & Conference');
            console.log('   - Office cleaning tasks (orange category)');
            console.log('');
            console.log('📋 Page 6: Room 401 - Offices & Conference');
            console.log('   - Office cleaning tasks (teal category)');
            console.log('');
            console.log('📋 Page 7: Room 303 - Offices & Conference');
            console.log('   - Office cleaning tasks (green category)');
            console.log('');
            console.log('📋 Page 8: Room 503 - Offices & Conference');
            console.log('   - Office cleaning tasks (purple category)');
            console.log('');
            console.log('📋 Page 9: Garbage Collection');
            console.log('   - Garbage collection tasks (pink category)');
            console.log('');
            console.log('📋 Page 10: Ground Floor');
            console.log('   - Ground floor maintenance tasks (gray category)');
            console.log('');
            console.log('🎯 Use showPage(1-10) to navigate to any page');
            console.log('🎯 Use testAllPages() to test all page generation');
        };
    });

    // Navigation functions for task checklist
    function changeTaskPage(direction) {
        console.log('changeTaskPage called with direction:', direction);
        console.log('Current page before change:', currentTaskPage);

        const newPage = currentTaskPage + direction;

        if (newPage >= 1 && newPage <= 10) {
            currentTaskPage = newPage;
            console.log('Loading new page:', currentTaskPage);

            try {
                loadTaskPageContent(currentTaskPage);
                document.getElementById('currentPageNumber').textContent = currentTaskPage;

                // IMMEDIATE localStorage load
                const dateInput = document.getElementById('week1_date') || document.getElementById(`week1_date_p${currentTaskPage}`);
                const currentDate = dateInput ? dateInput.value : new Date().toISOString().split('T')[0];
                console.log('🔄 IMMEDIATE localStorage load on page change for date:', currentDate);
                loadFromLocalStorage(currentDate);

                // Load saved statuses after page content is loaded
                setTimeout(() => {
                    loadSavedStatuses();
                }, 200);

                console.log('✅ Successfully loaded page:', currentTaskPage);
            } catch (error) {
                console.error('❌ Error loading page:', error);
            }
        } else {
            console.log('Page out of range:', newPage);
        }
    }

    // Single checkbox functions
    function showOptions(cellId) {
        const mainBox = document.getElementById(cellId);
        const optionsDiv = document.getElementById(cellId + '_options');

        // Hide main box and show options
        mainBox.style.display = 'none';
        optionsDiv.style.display = 'flex';
        optionsDiv.style.gap = '2px';
        optionsDiv.style.justifyContent = 'center';
        optionsDiv.style.alignItems = 'center';
    }

    function selectOption(cellId, option) {
        const mainBox = document.getElementById(cellId);
        const optionsDiv = document.getElementById(cellId + '_options');

        // Hide options and show main box with selected option
        optionsDiv.style.display = 'none';
        mainBox.style.display = 'flex';

        if (option === 'check') {
            mainBox.style.backgroundColor = '#28a745';
            mainBox.style.color = 'white';
            mainBox.style.border = '2px solid #28a745';
            mainBox.innerHTML = '✓';
            mainBox.setAttribute('data-selected', 'check');
        } else if (option === 'wrong') {
            mainBox.style.backgroundColor = '#dc3545';
            mainBox.style.color = 'white';
            mainBox.style.border = '2px solid #dc3545';
            mainBox.innerHTML = '✗';
            mainBox.setAttribute('data-selected', 'wrong');
        }

        // Add click handler to reset if clicked again
        mainBox.onclick = function() {
            resetBox(cellId);
        };
    }

    function resetBox(cellId) {
        const mainBox = document.getElementById(cellId);

        // Reset to original state - completely empty
        mainBox.style.backgroundColor = 'white';
        mainBox.style.color = 'black';
        mainBox.style.border = '2px solid black';
        mainBox.innerHTML = '';
        mainBox.removeAttribute('data-selected');

        // Restore original click handler
        mainBox.onclick = function() {
            showOptions(cellId);
        };
    }

    // ========== SUPER SIMPLE TEST FUNCTIONS ==========
    function testAddModal(categoryId, categoryName) {
        alert('🚀 ADD BUTTON CLICKED! Category: ' + categoryId + ' - ' + categoryName);

        // Try to find and show the modal
        const modal = document.getElementById('addMembersModal');
        if (modal) {
            alert('✅ Modal found! Opening...');
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            // Add test content
            setTimeout(() => {
                const container2025 = document.getElementById('availableStudents2025');
                const container2026 = document.getElementById('availableStudents2026');

                if (container2025) {
                    container2025.innerHTML = '<tr><td class="text-success p-3">✅ TEST STUDENT 2025 - John Doe</td></tr>';
                }
                if (container2026) {
                    container2026.innerHTML = '<tr><td class="text-primary p-3">✅ TEST STUDENT 2026 - Jane Smith</td></tr>';
                }

                alert('✅ Test content added to modal!');
            }, 500);
        } else {
            alert('❌ ERROR: Modal not found!');
        }
    }

    function testDeleteModal(categoryId, categoryName) {
        alert('🗑️ DELETE BUTTON CLICKED! Category: ' + categoryId + ' - ' + categoryName);

        const modal = document.getElementById('deleteMembersModal');
        if (modal) {
            alert('✅ Delete modal found! Opening...');
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            setTimeout(() => {
                const container = document.getElementById('currentMembers2025');
                if (container) {
                    container.innerHTML = '<tr><td class="text-warning p-3">⚠️ TEST MEMBER - Mike Johnson</td></tr>';
                }
                alert('✅ Test delete content added!');
            }, 500);
        } else {
            alert('❌ ERROR: Delete modal not found!');
        }
    }

    function testEditModal(categoryId, categoryName) {
        alert('✏️ EDIT BUTTON CLICKED! Category: ' + categoryId + ' - ' + categoryName);

        const modal = document.getElementById('editMembersModal');
        if (modal) {
            alert('✅ Edit modal found! Opening...');
            const bsModal = new bootstrap.Modal(modal);
            bsModal.show();

            setTimeout(() => {
                const container = document.getElementById('editMembers2025Container');
                if (container) {
                    container.innerHTML = '<div class="text-info p-3">✏️ TEST EDIT CONTENT - Alice Brown</div>';
                }
                alert('✅ Test edit content added!');
            }, 500);
        } else {
            alert('❌ ERROR: Edit modal not found!');
        }
    }
  </script>

  <style>
    /* Task Checklist Modal Styles - Enhanced Tabular Format */
    .table {
      border-collapse: collapse !important;
      margin: 0 !important;
      background-color: #ffffff !important;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
    }

    /* Enhanced table headers */
    .table thead th {
      background-color: transparent !important;
      border: 1px solid #dee2e6 !important;
      font-weight: 600 !important;
      text-align: center !important;
      padding: 12px 8px !important;
      color: #333 !important;
      font-size: 13px !important;
    }

    /* Task Checklist Modal - Remove ONLY header backgrounds, keep category colors */
    #taskChecklistModal .table thead th {
      background-color: transparent !important;
      background: none !important;
      border: 1px solid #dee2e6 !important;
      color: #333 !important;
    }

    /* Override Bootstrap table-bordered default styling for task checklist headers only */
    #taskChecklistModal .table-bordered thead th {
      background-color: transparent !important;
      background: none !important;
      border: 1px solid #dee2e6 !important;
    }

    /* Remove any Bootstrap table header backgrounds but keep category cell colors */
    #taskChecklistModal .table > thead > tr > th {
      background-color: transparent !important;
      background: none !important;
      border: 1px solid #dee2e6 !important;
    }

    /* Keep category cell colors - DO NOT override these */
    #taskChecklistModal .table tbody td[style*="background-color: #90EE90"],
    #taskChecklistModal .table tbody td[style*="background: linear-gradient"] {
      /* Keep original background colors for category cells */
    }

    /* Vertical text rotation for category cells */
    .vertical-text {
      writing-mode: vertical-lr;
      text-orientation: mixed;
      transform: rotate(180deg);
      white-space: nowrap;
    }

    /* Category headers with better styling */
    .table thead th.category-header {
      background-color: transparent !important;
      font-weight: 600 !important;
      font-size: 14px !important;
      color: #333 !important;
    }

    /* Week headers */
    .table thead th.week-header {
      background-color: transparent !important;
      font-weight: 600 !important;
      font-size: 13px !important;
      color: #333 !important;
    }

    /* Day headers */
    .table thead th.day-header {
      background-color: transparent !important;
      font-weight: 600 !important;
      font-size: 11px !important;
      color: #333 !important;
    }

    .table td, .table th {
      border: 1px solid #000 !important;
      padding: 2px !important;
      vertical-align: middle !important;
      line-height: 1.2 !important;
    }

    .status-buttons {
      display: flex !important;
      gap: 2px !important;
      justify-content: center !important;
      align-items: center !important;
    }

    .status-btn {
      width: 30px !important;
      height: 30px !important;
      border: 2px solid #000 !important;
      background: white !important;
      font-size: 14px !important;
      font-weight: bold !important;
      cursor: pointer !important;
      border-radius: 4px !important;
      padding: 0 !important;
      margin: 2px !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      line-height: 1 !important;
      transition: all 0.2s ease !important;
    }

    .check-btn {
      background: white !important;
      color: #28a745 !important;
      border: 2px solid #28a745 !important;
      width: 30px !important;
      height: 30px !important;
      font-size: 14px !important;
      font-weight: bold !important;
    }

    .check-btn:hover {
      background: #e8f5e8 !important;
    }

    .check-btn.active {
      background: #28a745 !important;
      color: white !important;
      border: 2px solid #28a745 !important;
    }

    .wrong-btn {
      background: white !important;
      color: #dc3545 !important;
      border: 2px solid #dc3545 !important;
      width: 30px !important;
      height: 30px !important;
      font-size: 14px !important;
      font-weight: bold !important;
    }

    .wrong-btn:hover {
      background: #fdeaea !important;
    }

    .wrong-btn.active {
      background: #dc3545 !important;
      color: white !important;
      border: 2px solid #dc3545 !important;
    }

    .remarks-input {
      border: none !important;
      resize: none !important;
      background: #ffebee !important;
      padding: 4px !important;
      margin: 0 !important;
      outline: none !important;
      width: 100% !important;
      height: 30px !important;
      font-size: 11px !important;
      font-weight: 500 !important;
      line-height: 1.2 !important;
      color: #333 !important;
    }

    .remarks-input:focus {
      box-shadow: none !important;
      border: none !important;
    }

    /* Make Task Checklist Modal Content Larger */
    #taskChecklistModal .modal-content {
      width: 98vw !important;
      max-width: 2000px !important;
      margin: 1rem 1rem 1rem 0.5rem !important;
      height: 95vh !important;
      overflow-x: hidden !important;
      overflow-y: auto !important;
    }

    /* Make Task Page Content Area Wider */
    #taskPageContent {
      width: 100% !important;
      max-width: 100% !important;
      margin: 0 !important;
      padding: 15px 20px !important;
      overflow-x: hidden !important;
    }

    /* Make tables inside task content wider */
    #taskPageContent table {
      width: 100% !important;
      min-width: 100% !important;
      max-width: 100% !important;
      font-size: 12px !important;
      table-layout: fixed !important;
    }

    #taskPageContent .table-bordered {
      border: 1px solid #dee2e6 !important;
    }

    #taskChecklistModal .modal-dialog {
      max-width: 99vw !important;
      width: 99vw !important;
      margin: 0.5rem auto !important;
      height: auto !important;
    }

    /* Improved Modal Table Spacing */
    .modal-body table {
      width: 100% !important;
      border-collapse: collapse !important;
      border-spacing: 0 !important;
      margin: 10px 0 !important;
      max-width: 1700px !important;
      font-size: 12px !important;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
      border: 1px solid #dee2e6 !important;
    }

    .modal-body table th,
    .modal-body table td {
      padding: 8px 6px !important;
      border: 2px solid #000 !important;
      text-align: center !important;
      vertical-align: middle !important;
      font-size: 12px !important;
      line-height: 1.4 !important;d
      font-weight: 500 !important;
    }

    .modal-body table th {
      background-color: #2c3e50 !important;
      font-weight: bold !important;
      color: white !important;
      border: 2px solid #000 !important;
      padding: 12px 8px !important;
      text-align: center !important;
      font-size: 13px !important;
      text-transform: uppercase !important;
      letter-spacing: 0.5px !important;
    }

    .modal-body table tbody tr:nth-child(even) {
      background-color: #f8f9fa !important;
    }

    .modal-body table tbody tr:nth-child(odd) {
      background-color: #ffffff !important;
    }

    .modal-body table tbody tr:hover {
      background-color: #e3f2fd !important;
    }

    /* Center the fullscreen modal content */
    .modal-fullscreen .modal-content {
      display: flex !important;
      flex-direction: column !important;
      justify-content: center !important;
      align-items: center !important;
      min-height: 100vh !important;
      margin: 0 auto !important;
      max-width: 95% !important;
      padding: 20px !important;
    }

    .modal-fullscreen .modal-body {
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      width: 100% !important;
      overflow: auto !important;
    }

    .modal-fullscreen .modal-header {
      width: 100% !important;
      text-align: center !important;
      justify-content: center !important;
      position: relative !important;
    }

    .modal-fullscreen .modal-header .btn-close {
      position: absolute !important;
      right: 20px !important;
      top: 50% !important;
      transform: translateY(-50%) !important;
    }

    /* Better Modal Centering */
    .modal-dialog {
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
      min-height: calc(100vh - 3.5rem) !important;
    }

    /* Specific adjustments for member modals */
    #studentAssignModal .modal-content,
    #addMembersModal .modal-content,
    #deleteMembersModal .modal-content,
    #editMembersModal .modal-content {
      transform: none !important;
      left: auto !important;
      margin: 0 auto !important;
    }

    /* Task description styling - Enhanced Tabular Format */
    .task-cell:not(.category-cell) {
      background-color: #ffffff !important;
      padding: 12px 16px !important;
      border: 1px solid #333 !important;
      font-size: 18px !important;
      font-weight: normal !important;
      line-height: 1.5 !important;
      vertical-align: middle !important;
      width: 350px !important;
      white-space: normal !important;
      text-align: left !important;
      color: #000000 !important;
      border-right: 2px solid #666 !important;
    }

    /* Keep category cells unchanged */
    .task-cell.category-cell {
      /* Preserve original styling for task area column */
    }

    /* Task Checklist Modal Title - Make it bigger and more readable */
    #taskChecklistModalLabel {
      font-size: 32px !important;
      font-weight: normal !important;
      color: #000000 !important;
      text-align: center !important;
      margin: 0 !important;
      padding: 15px 0 !important;
    }

    /* Task Checklist Modal Header */
    #taskChecklistModal .modal-header {
      padding: 15px 20px !important;
      border-bottom: 2px solid #dee2e6 !important;
      background-color: #f8f9fa !important;
    }

    /* Improve text selection/highlighting readability for task descriptions only */
    .task-cell:not(.category-cell)::selection,
    .task-cell:not(.category-cell) *::selection {
      background-color: #000000 !important;
      color: #ffffff !important;
    }

    /* For Firefox */
    .task-cell:not(.category-cell)::-moz-selection,
    .task-cell:not(.category-cell) *::-moz-selection {
      background-color: #000000 !important;
      color: #ffffff !important;
    }

    /* Bold styling for important task items */
    .task-cell.important-task {
      font-weight: bold !important;
      background-color: #f8f9fa !important;
      color: #2c3e50 !important;
    }

    /* Room title styling - Enhanced Orange Highlight */
    .task-cell.room-title,
    .task-cell[style*="background-color: #ff8c00"] {
      background-color: #ff8c00 !important;
      font-weight: bold !important;
      color: #000000 !important;
      text-align: center !important;
      font-size: 18px !important;
      border: 1px solid #333 !important;
      padding: 8px !important;
    }

    /* Day cells - Enhanced Tabular Format */
    .day-cell {
      text-align: center !important;
      padding: 8px 6px !important;
      border: 1px solid #333 !important;
      width: 75px !important;
      height: 45px !important;
      vertical-align: middle !important;
      background-color: #ffffff !important;
      font-weight: normal !important;
    }

    /* Checkbox cells styling */
    .checkbox-cell {
      background-color: #fafafa !important;
      border: 1px solid #333 !important;
      padding: 6px !important;
      text-align: center !important;
      vertical-align: middle !important;
    }

    /* Single checkbox styling */
    .single-checkbox {
      border: 2px solid #333 !important;
      background-color: #ffffff !important;
      border-radius: 4px !important;
      transition: all 0.2s ease !important;
    }

    .single-checkbox:hover {
      border-color: #007bff !important;
      box-shadow: 0 0 0 2px rgba(0,123,255,0.25) !important;
    }

    /* Remarks cells - Enhanced Tabular Format */
    .remarks-cell {
      padding: 12px !important;
      border: 1px solid #333 !important;
      width: 400px !important;
      vertical-align: middle !important;
      background-color: #fff5f5 !important;
    }

    /* Category cells - Enhanced styling */
    .category-cell {
      border: 2px solid #333 !important;
      font-weight: bold !important;
      text-align: center !important;
      vertical-align: middle !important;
      padding: 12px 8px !important;
      line-height: 1.3 !important;
    }

    /* Remarks input styling */
    .remarks-input {
      width: 100% !important;
      height: 60px !important;
      border: 1px solid #ccc !important;
      border-radius: 3px !important;
      padding: 12px !important;
      font-size: 16px !important;
      background-color: #ffffff !important;
      resize: vertical !important;
      font-family: Arial, sans-serif !important;
    }

    .remarks-input:focus {
      border-color: #007bff !important;
      box-shadow: 0 0 0 2px rgba(0,123,255,0.25) !important;
      outline: none !important;
    }

    /* Table row styling */
    .table tbody tr {
      border-bottom: 1px solid #dee2e6 !important;
    }

    .table tbody tr:nth-child(even) {
      background-color: #f8f9fa !important;
    }

    .table tbody tr:hover {
      background-color: #e3f2fd !important;
    }

    /* Improve readability of task description text only - NOT task areas */
    #taskChecklistModal .table tbody td:not(.category-cell) {
      font-size: 16px !important;
      line-height: 1.4 !important;
      color: #000000 !important;
      font-weight: normal !important;
    }

    /* Override any blue text highlighting with better contrast - ONLY for task descriptions */
    #taskChecklistModal .table tbody td:not(.category-cell)[style*="color: blue"],
    #taskChecklistModal .table tbody td:not(.category-cell)[style*="color: #0000ff"],
    #taskChecklistModal .table tbody td:not(.category-cell)[style*="color: #007bff"] {
      color: #000000 !important;
      background-color: #f8f9fa !important;
      font-weight: normal !important;
    }

    /* Keep task area column colors unchanged */
    #taskChecklistModal .table tbody td.category-cell {
      /* Preserve original colors and styling for task areas */
    }
  </style>
</body>
</html><?php /**PATH C:\PN_Systems\G16_CAPSTONE\resources\views/generalTask.blade.php ENDPATH**/ ?>