body{
    font-family: <PERSON><PERSON>s;
}
.page-container {
    padding: 20px;
    max-width: 100%;
}

.header-section {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.header-section h2 {
    font-size: 24px;
    color: #333;
    margin: 0;
}

.add-button {
    background: #ff9933;
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    border: none;
}

.add-button:hover {
    background-color:rgb(246, 124, 2);
    color: white;
    text-decoration: none;
}

.table-wrapper {
    background: white;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.table-header {
    display: grid;
    grid-template-columns: 200px 1fr 200px;
    background: #4CAF50;
    color: white;
}

.header-cell {
    padding: 16px 24px;
    font-size: 14px;
    font-weight: normal;
}

.table-row {
    display: grid;
    grid-template-columns: 200px 1fr 200px;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
}

.cell {
    padding: 16px 24px;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
    flex-wrap: nowrap;
}

/* Icon Buttons */
.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    color: #fff;
    background-color: #4a90e2;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.btn-icon:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.btn-icon i {
    font-size: 14px;
}

/* View button */
.btn-icon[title="View"] {
    background-color: #4a90e2;
}

.btn-icon[title="View"]:hover {
    background-color: #357abd;
}

/* Edit button */
.btn-icon[title="Edit"] {
    background-color: #f39c12;
}

.btn-icon[title="Edit"]:hover {
    background-color: #d68910;
}

/* Delete button */
.delete-form .btn-icon[title="Delete"],
.btn-icon[title="Delete"] {
    background-color: #dc3545 !important;
}

.delete-form .btn-icon[title="Delete"]:hover,
.btn-icon[title="Delete"]:hover {
    background-color: #c82333 !important;
}

/* Form styles */
.delete-form {
    display: inline;
    margin: 0;
    padding: 0;
}

.empty-message {
    grid-column: 1 / -1;
    text-align: center;
    color: #666;
}

/* Alert Styling */
.alert {
    padding: 12px 16px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 6px;
    position: relative;
    display: flex;
    align-items: center;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-error {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

@media (max-width: 768px) {
    .page-container {
        padding: 16px;
    }

    .table-wrapper {
        overflow-x: auto;
    }

    .table-header,
    .table-row {
        min-width: 700px;
    }
}