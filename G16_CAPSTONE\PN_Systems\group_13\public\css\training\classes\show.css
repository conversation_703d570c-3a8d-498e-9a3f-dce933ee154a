
/* Force Poppins font for all elements */
* {
    font-family: 'Poppins', sans-serif !important;
}

/* Preserve icon fonts */
.fas, .far, .fal, .fab, .fa,
[class*="fa-"],
.material-icons,
.glyphicon {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 5 Free", "Font Awesome 5 Pro", "Material Icons", "Glyphicons Halflings" !important;
}

.page-container {
    padding: 20px;
    max-width: 100%;
}

.header-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
}

.header-section h2 {
    font-size: 24px;
    color: #333;
    margin: 0;
}

.btn-back {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background-color: #ff9933;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-back:hover {
    background-color:rgb(253, 126, 0);
    text-decoration: none;
    color: white;
}

.back-button {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 15px;
    /* background-color: #ff9933; */
    color: rgb(50, 50, 50);
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.2s;
}

.back-button:hover {
    color: #ff9933;
}

.back-button i {
    font-size: 12px;
}

.content-section {
    display: grid;
    gap: 20px;
}

.card {
    background: white;
    padding: 24px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.card h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
    font-size: 18px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.info-item label {
    font-weight: 500;
    color: #666;
    font-size: 14px;
}

.info-item span {
    color: #333;
    font-size: 16px;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.table th {
    background-color: #22bbea;
    font-weight: 500;
    color: white;
}

.table tr:hover {
    background-color: #f8f9fa;
}

.text-center {
    text-align: center;
}

.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

@media (max-width: 768px) {
    .info-grid {
        grid-template-columns: 1fr;
    }
}
