<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Visitor;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\NotificationView;

class VisitorController extends Controller
{
    public function create()
    {
        Log::info('Accessed visitor log creation form.');
        return view('visitor.visitor-log');
    }

    public function store(Request $request)
    {
        $request->validate([
            'valid_id' => 'required|string|max:255',
            'visitor_name' => 'required|string|max:255|regex:/^[A-Za-z\s]+$/',
            'id_number' => 'required|string|max:255',
            'relationship' => 'required|string|max:100',
            'purpose' => 'required|string|max:255',
            'other_id_type' => 'required_if:valid_id,Other|nullable|string|max:255',
        ]);

        $validId = $request->valid_id === 'Other' ? $request->other_id_type : $request->valid_id;
        // Step 1: Determine the next available visitor_pass (1 to 10)
        $maxPassNumber = 10;

        $usedPasses = Visitor::whereDate('visitor_date', now()->format('Y-m-d'))
            ->whereNull('time_out')
            ->pluck('visitor_pass')
            ->toArray();

        $visitorPass = null;
        for ($i = 1; $i <= $maxPassNumber; $i++) {
            if (!in_array($i, $usedPasses)) {
                $visitorPass = $i;
                break;
            }
        }

        if ($visitorPass === null) {
            return back()->withErrors([
                'visitor_pass' => 'All visitor passes are currently in use.',
            ]);
        }

        // Step 2: Save the new visitor
        Visitor::create([
            'visitor_pass' => $visitorPass,
            'visitor_id' => $request->id,
            'valid_id' => $validId,
            'id_number' => $request->id_number,
            'visitor_name' => $request->visitor_name,
            'relationship' => $request->relationship,
            'purpose' => $request->purpose,
            'visitor_date' => date('Y-m-d'),
            'time_in' => date('H:i:s'),
            'time_out' => null,
        ]);


        Log::info('Visitor log created successfully.', [
            'visitor_pass' => $request->visitor_pass,
            'visitor_name' => $request->visitor_name,
            'date' => date('Y-m-d'),
        ]);

        $visitors = Visitor::all();
        return redirect()->route('visitor.dashboard.show', compact('visitors'))->with('success', 'Visitor log created successfully!');
    }

    public function logOut(Request $request, $id)
    {
        $visitor = Visitor::findOrFail($id);
        $visitor->time_out = now()->setTimezone('Asia/Manila')->format('H:i:s');
        $visitor->save();

        Log::info('Visitor logged out.', [
            'visitor_id' => $id,
            'time_out' => $visitor->time_out,
        ]);

        return redirect()->back()->with('success', 'Time out logged successfully!');
    }
}
