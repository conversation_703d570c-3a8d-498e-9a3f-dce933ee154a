<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Notifications\Notifiable;
use Carbon\Carbon;

class Academic extends Model
{
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */

     public $timestamps = false;

    // Optional: if you want to cast is_deleted to boolean
    protected $casts = [
        'is_deleted' => 'boolean',
    ];

    protected $fillable = [
        'student_id',
        'academic_date',
        'time_out',
        'time_out_remark',
        'time_out_consideration',
        'time_out_reason',
        'time_out_absent_validation',
        'monitor_logged_out',
        'time_in',
        'time_in_remark',
        'educator_consideration',
        'time_in_reason',
        'time_in_absent_validation',
        'monitor_logged_in',
        'created_by',
        'created_at',
        'updated_by',
        'updated_at',
        'is_deleted'
    ];

    public function studentDetail()
    {
        return $this->belongsTo(StudentDetail::class, 'student_id', 'student_id');
    }

    // Get the monitor name who set the time out consideration
    public function getTimeOutMonitorNameAttribute()
    {
        if ($this->time_out_consideration && $this->updated_by) {
            return $this->updated_by;
        }
        return null;
    }

    // Get the monitor name who set the time in consideration
    public function getTimeInMonitorNameAttribute()
    {
        if ($this->educator_consideration && $this->updated_by) {
            return $this->updated_by;
        }
        return null;
    }



    // Format time_out to 12-hour format
    public function getFormattedTimeOutAttribute()
    {
        if (!$this->time_out) {
            return '—';
        }
        return Carbon::createFromFormat('H:i:s', $this->time_out)->format('g:i A');
    }

    // Format time_in to 12-hour format
    public function getFormattedTimeInAttribute()
    {
        if (!$this->time_in) {
            return '—';
        }
        return Carbon::createFromFormat('H:i:s', $this->time_in)->format('g:i A');
    }
}
