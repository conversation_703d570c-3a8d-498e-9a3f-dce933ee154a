<x-monitorLogLayout>
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <div class="p-8 bg-white shadow-md rounded-2xl">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-bold text-orange-700">GOING OUT LOGS MONITOR</h1>
            <div class="flex items-center space-x-4">
                <form action="{{ route('monitor.goingout.logs') }}" method="GET" class="flex items-center space-x-2">
                    <input type="date" name="date" value="{{ $selectedDate }}"
                        class="px-3 py-2 text-sm border border-gray-300 rounded-sm focus:outline-none focus:ring-2 focus:ring-orange-500">
                    <button type="submit"
                        class="px-4 py-2 text-sm font-semibold text-white bg-orange-500 rounded-sm hover:bg-orange-600">
                        Filter
                    </button>
                </form>

                <a href="{{ route('monitor.dashboard') }}"
                    class="inline-flex items-center text-sm font-medium text-blue-600 hover:underline">
                    <i data-feather="arrow-left" class="w-5 h-5 mr-1"></i> Back to Dashboard
                </a>
            </div>
        </div>

        {{-- Search and Filters --}}
        <div class="mb-6">
            {{-- Single Row: Search + Filters --}}
            <div class="flex flex-col lg:flex-row gap-4 items-end">
                {{-- Search Bar (Left Side) --}}
                <div class="flex-1 lg:max-w-md">
                    <label for="studentSearch" class="block text-sm font-medium text-gray-700 mb-1">Search Students</label>
                    <div class="relative">
                        <input type="text" id="studentSearch" placeholder="Search by ID, First Name, or Last Name..."
                               class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 pr-8">
                        <button type="button" id="clearSearch" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 hidden">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                {{-- Batch Filter --}}
                <div class="flex-1 lg:max-w-xs">
                    <label for="batchFilter" class="block text-sm font-medium text-gray-700 mb-1">Filter by Batch</label>
                    <select id="batchFilter" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                        <option value="">All Batches</option>
                        @foreach($batches as $batch)
                            <option value="{{ $batch }}">{{ $batch }}</option>
                        @endforeach
                    </select>
                </div>

                {{-- Group Filter --}}
                <div class="flex-1 lg:max-w-xs">
                    <label for="groupFilter" class="block text-sm font-medium text-gray-700 mb-1">Filter by Group</label>
                    <select id="groupFilter" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                        <option value="">All Groups</option>
                        @foreach($groups as $group)
                            <option value="{{ $group }}">{{ $group }}</option>
                        @endforeach
                    </select>
                </div>

                  {{-- Status Filter --}}
                <div class="flex-1 lg:max-w-xs">
                    <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-1">Filter by Status</label>
                    <select id="statusFilter" class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500">
                        <option value="">All Students</option>
                        <option value="not-logged-in">Not Logged In Yet</option>
                        <option value="late">Late Students</option>
                        <option value="absent">Not Going Out Students</option>
                    </select>
                </div>
            </div>

            {{-- Search Results Info --}}
            <div id="searchResults" class="hidden mt-3">
                <div class="text-sm text-gray-600">
                    <span id="searchCount">0</span> student(s) found
                </div>
            </div>
        </div>

        {{-- Table --}}
         <div class="relative w-full overflow-hidden border border-gray-200 rounded-lg">
                <div class="w-full overflow-x-auto table-container">
                    <div class="overflow-y-auto max-h-[calc(100vh-200px)] table-container">
                        <table class="w-full min-w-full text-sm text-left text-gray-700">
                            <thead class="sticky top-0 z-10 text-xs font-semibold tracking-wider uppercase bg-gray-100">
                    <tr>
                        <th class="px-6 py-3 text-black">Student ID</th>
                        <th class="px-6 py-3 text-black">Student Name</th>
                        <th class="px-6 py-3 text-black">Batch</th>
                        <th class="px-6 py-3 text-black">Group</th>
                        <th class="px-6 py-3 text-black">Date</th>
                          <th class="px-6 py-3 text-black">Session</th>
                        <th class="px-6 py-3 text-black">Destination</th>
                        <th class="px-6 py-3 text-black">Purpose</th>
                        <th class="px-6 py-3 text-black">Log Out Time</th>
                        <th class="px-6 py-3 text-black">Log Out Remarks</th>
                        <th class="px-6 py-3 text-black">Log Out Consideration</th>
                        <th class="px-6 py-3 text-black">Log In Time</th>
                        <th class="px-6 py-3 text-black">Log In Remarks</th>
                        <th class="px-6 py-3 text-black">Log In Consideration</th>
                    </tr>
                </thead>
                <tbody class="text-sm text-gray-700">
                    @forelse($logs as $index => $log)
                        @php
                            // Let JavaScript handle all color coding dynamically
                            $rowClass = 'border-b border-gray-200 hover:bg-gray-50';
                        @endphp
                        <tr class="{{ $rowClass }}"
                            data-batch="{{ $log->studentDetail->batch ?? '' }}"
                            data-group="{{ $log->studentDetail->group ?? '' }}"
                            data-student-id="{{ $log->student_id }}"
                            data-student-fname="{{ strtolower($log->studentDetail->user->user_fname ?? '') }}"
                            data-student-lname="{{ strtolower($log->studentDetail->user->user_lname ?? '') }}"
                            data-student-fullname="{{ strtolower(($log->studentDetail->user->user_fname ?? '') . ' ' . ($log->studentDetail->user->user_lname ?? '')) }}">
                            <td class="px-6 py-4 font-medium">{{ $log->student_id }}</td>
                            <td class="px-6 py-4">{{ $log->studentDetail->user->user_fname ?? 'N/A' }} {{ $log->studentDetail->user->user_lname ?? '' }}</td>
                            <td class="px-6 py-4">{{ $log->studentDetail->batch ?? 'N/A' }}</td>
                            <td class="px-6 py-4">{{ $log->studentDetail->group ?? 'N/A' }}</td>
                            <td class="px-6 py-4">{{ \Carbon\Carbon::parse($log->going_out_date)->format('M j, Y') }}</td>
                             <td class="px-6 py-4">
                                <span class="px-2 py-1 text-xs font-semibold rounded-full bg-orange-100 text-orange-800">
                                    Session {{ $log->session_number ?? 1 }}
                                </span>
                            </td>
                            <td class="px-6 py-4">{{ $log->destination ?? '—' }}</td>
                            <td class="px-6 py-4">{{ $log->purpose ?? '—' }}</td>
                            <td class="px-6 py-4">
                                @if($log->time_out)
                                    {{ $log->formatted_time_out }}
                                @else
                                    {{-- Log Out Form --}}
                                    <div id="logout-form-{{ $log->id }}" class="space-y-2">
                                        <button onclick="showLogoutForm({{ $log->id }})"
                                                class="px-3 py-1 text-xs text-white bg-red-500 rounded hover:bg-red-600">
                                            Log Out Student
                                        </button>
                                    </div>

                                    {{-- Hidden form for destination and purpose --}}
                                    <div id="logout-details-{{ $log->id }}" class="hidden space-y-2">
                                        <input type="text"
                                               id="destination-{{ $log->id }}"
                                               placeholder="Destination"
                                               class="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500"
                                               maxlength="100">
                                        <input type="text"
                                               id="purpose-{{ $log->id }}"
                                               placeholder="Purpose"
                                               class="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500"
                                               maxlength="100">
                                        <div class="flex space-x-1">
                                            <button onclick="performLogOutWithDetails({{ $log->id }}, '{{ $log->studentDetail->student_id }}')"
                                                    class="flex-1 px-2 py-1 text-xs text-white bg-red-500 rounded hover:bg-red-600">
                                                Confirm
                                            </button>
                                            <button onclick="hideLogoutForm({{ $log->id }})"
                                                    class="flex-1 px-2 py-1 text-xs text-gray-600 bg-gray-200 rounded hover:bg-gray-300">
                                                Cancel
                                            </button>
                                        </div>
                                    </div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <span class="{{ $log->time_out_remark === 'Late' ? 'text-red-600 font-bold' : ($log->time_out_remark === 'On Time' ? 'text-blue-600 font-bold' : ($log->time_out_remark === 'Early' ? 'text-green-600 font-bold' : '')) }}">
                                    {{ $log->time_out_remark ?? '—' }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                {{-- Log Out Consideration Form - Always Available --}}
                                @if($log->time_out_consideration)
                                    <div class="p-3 bg-gray-50 rounded-lg border">
                                        <div class="font-semibold text-sm {{ $log->time_out_consideration === 'Excused' ? 'text-green-600' : (($log->time_out_consideration === 'Absent' || $log->time_out_consideration === 'Not going out') ? 'text-gray-600' : 'text-red-600') }}">
                                            {{ $log->time_out_consideration === 'Absent' ? 'Not going out' : $log->time_out_consideration }}
                                        </div>
                                        @if($log->time_out_reason)
                                            <div class="text-xs text-gray-600 mt-1 italic">{{ $log->time_out_reason }}</div>
                                        @endif
                                        <div class="text-xs text-blue-600 mt-1 font-medium">
                                            Set by: {{ $log->time_out_monitor_name ?: 'Monitor' }}
                                        </div>
                                    </div>
                                @else
                                    @if($log->time_out_remark === 'On Time')
                                        {{-- Show disabled message for On Time students --}}
                                        <div class="p-3 bg-blue-50 rounded-lg border border-blue-200">
                                            <div class="font-semibold text-sm text-blue-600">
                                                No Consideration Needed
                                            </div>
                                            <div class="text-xs text-blue-600 mt-1">
                                                Student was on time - no consideration required
                                            </div>
                                        </div>
                                    @else
                                        <div class="consideration-form space-y-3" data-log-id="{{ $log->id }}" data-type="time_out">
                                            {{-- Step 1: Choice Selection --}}
                                            <div class="choice-step">
                                                <select class="consideration-select w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500" onchange="handleChoiceSelection(this, {{ $log->id }}, 'time_out')">
                                                    <option value="">Select Consideration...</option>
                                                    <option value="Excused" class="text-green-600">✓ Valid</option>
                                                    <option value="Not Excused" class="text-red-600">✗ Not Valid</option>
                                                    {{-- Hide "Not going out" option if student has already logged out --}}
                                                    @if(!$log->time_out)
                                                        <option value="Not going out" class="text-gray-600">○ Not Going Out</option>
                                                    @endif
                                                </select>
                                                <button type="button" class="next-btn hidden w-full mt-2 px-3 py-2 text-white bg-blue-500 rounded-md hover:bg-blue-600 text-sm font-medium" onclick="showReasonStep({{ $log->id }}, 'time_out')">
                                                    Next →
                                                </button>
                                            </div>

                                        {{-- Step 2: Reason Input (Hidden Initially) --}}
                                        <div class="reason-step hidden">
                                            <div class="selected-choice-display mb-2 p-2 bg-gray-50 rounded border text-sm">
                                                <strong>Selected:</strong> <span class="choice-text"></span>
                                            </div>
                                            <textarea class="reason-input w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 resize-none"
                                                      rows="3"
                                                      placeholder="Enter reason for this consideration..."
                                                      maxlength="200"></textarea>
                                            <div class="text-xs text-gray-500 mt-1">Maximum 200 characters</div>
                                            <button type="button" class="save-btn w-full mt-2 px-3 py-2 text-white bg-orange-500 rounded-md hover:bg-orange-600 text-sm font-medium" onclick="saveConsideration({{ $log->id }}, 'time_out')">
                                                Save Consideration
                                            </button>
                                        </div>
                                        </div>
                                        <div id="timeout-error-{{ $log->id }}" class="hidden text-xs text-red-600 mt-2 p-2 bg-red-50 rounded"></div>
                                        <div id="timeout-success-{{ $log->id }}" class="hidden text-xs text-green-600 mt-2 p-2 bg-green-50 rounded"></div>
                                    @endif
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                @if($log->time_in)
                                    {{ $log->formatted_time_in }}
                                @elseif($log->time_out)
                                    <button onclick="performLogIn({{ $log->id }}, '{{ $log->studentDetail->student_id }}')"
                                            class="px-3 py-1 text-xs text-white bg-green-500 rounded hover:bg-green-600">
                                        Log In Student
                                    </button>
                                @else
                                    <span class="text-gray-400 text-xs">Log out first</span>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <span class="{{ $log->time_in_remark === 'Late' ? 'text-red-600 font-bold' : ($log->time_in_remark === 'On Time' ? 'text-blue-600 font-bold' : ($log->time_in_remark === 'Early' ? 'text-green-600 font-bold' : '')) }}">
                                    {{ $log->time_in_remark ?? '—' }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                {{-- Log In Consideration Form - Always Available --}}
                                @if($log->educator_consideration)
                                    <div class="p-3 bg-gray-50 rounded-lg border">
                                        <div class="font-semibold text-sm {{ $log->educator_consideration === 'Excused' ? 'text-green-600' : (($log->educator_consideration === 'Absent' || $log->educator_consideration === 'Not going out') ? 'text-gray-600' : 'text-red-600') }}">
                                            {{ $log->educator_consideration === 'Absent' ? 'Not going out' : $log->educator_consideration }}
                                        </div>
                                        @if($log->time_in_reason)
                                            <div class="text-xs text-gray-600 mt-1 italic">{{ $log->time_in_reason }}</div>
                                        @endif
                                        <div class="text-xs text-blue-600 mt-1 font-medium">
                                            Set by: {{ $log->time_in_monitor_name ?: 'Monitor' }}
                                        </div>
                                    </div>
                                @else
                                    @if($log->time_in_remark === 'On Time')
                                        {{-- Show disabled message for On Time students --}}
                                        <div class="p-3 bg-blue-50 rounded-lg border border-blue-200">
                                            <div class="font-semibold text-sm text-blue-600">
                                                No Consideration Needed
                                            </div>
                                            <div class="text-xs text-blue-600 mt-1">
                                                Student was on time - no consideration required
                                            </div>
                                        </div>
                                    @else
                                        <div class="consideration-form space-y-3" data-log-id="{{ $log->id }}" data-type="time_in">
                                            {{-- Step 1: Choice Selection --}}
                                            <div class="choice-step">
                                                <select class="consideration-select w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500" onchange="handleChoiceSelection(this, {{ $log->id }}, 'time_in')">
                                                    <option value="">Select Consideration...</option>
                                                    <option value="Excused" class="text-green-600">✓ Valid</option>
                                                    <option value="Not Excused" class="text-red-600">✗ Not Valid</option>
                                                    {{-- Hide "Not going out" option if student has already logged out --}}
                                                    @if(!$log->time_out)
                                                        <option value="Not going out" class="text-gray-600">○ Not going out</option>
                                                    @endif
                                                </select>
                                                <button type="button" class="next-btn hidden w-full mt-2 px-3 py-2 text-white bg-blue-500 rounded-md hover:bg-blue-600 text-sm font-medium" onclick="showReasonStep({{ $log->id }}, 'time_in')">
                                                    Next →
                                                </button>
                                            </div>

                                        {{-- Step 2: Reason Input (Hidden Initially) --}}
                                        <div class="reason-step hidden">
                                            <div class="selected-choice-display mb-2 p-2 bg-gray-50 rounded border text-sm">
                                                <strong>Selected:</strong> <span class="choice-text"></span>
                                            </div>
                                            <textarea class="reason-input w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-orange-500 resize-none"
                                                      rows="3"
                                                      placeholder="Enter reason for this consideration..."
                                                      maxlength="200"></textarea>
                                            <div class="text-xs text-gray-500 mt-1">Maximum 200 characters</div>
                                            <button type="button" class="save-btn w-full mt-2 px-3 py-2 text-white bg-orange-500 rounded-md hover:bg-orange-600 text-sm font-medium" onclick="saveConsideration({{ $log->id }}, 'time_in')">
                                                Save Consideration
                                            </button>
                                        </div>
                                        </div>
                                        <div id="timein-error-{{ $log->id }}" class="hidden text-xs text-red-600 mt-2 p-2 bg-red-50 rounded"></div>
                                        <div id="timein-success-{{ $log->id }}" class="hidden text-xs text-green-600 mt-2 p-2 bg-green-50 rounded"></div>
                                    @endif
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="13" class="px-6 py-8 text-center text-gray-500">
                                <i data-feather="inbox" class="w-8 h-8 mx-auto mb-2"></i>
                                <div>No going out logs found for the selected criteria.</div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
                </div>
            </div>
        </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Filter and search functionality
            const batchFilter = document.getElementById('batchFilter');
            const groupFilter = document.getElementById('groupFilter');
            const statusFilter = document.getElementById('statusFilter');
            const studentSearch = document.getElementById('studentSearch');
            const clearSearch = document.getElementById('clearSearch');
            const searchResults = document.getElementById('searchResults');
            const searchCount = document.getElementById('searchCount');
            const tableRows = document.querySelectorAll('tbody tr');

            // Disable group filter initially
            groupFilter.disabled = true;

            // Search functionality
            function performSearch() {
                const searchTerm = studentSearch.value.toLowerCase().trim();

                if (searchTerm === '') {
                    clearSearch.classList.add('hidden');
                    searchResults.classList.add('hidden');

                    // Reset all rows to visible when search is empty
                    tableRows.forEach(row => {
                        row.classList.remove('search-hidden');
                        row.style.display = '';
                    });

                    applyFilters();
                    return;
                }

                clearSearch.classList.remove('hidden');
                searchResults.classList.remove('hidden');

                let matchCount = 0;
                let firstMatch = null;

                tableRows.forEach(row => {
                    const studentId = row.getAttribute('data-student-id') || '';
                    const firstName = row.getAttribute('data-student-fname') || '';
                    const lastName = row.getAttribute('data-student-lname') || '';
                    const fullName = row.getAttribute('data-student-fullname') || '';

                    const isMatch = studentId.toLowerCase().includes(searchTerm) ||
                                   firstName.includes(searchTerm) ||
                                   lastName.includes(searchTerm) ||
                                   fullName.includes(searchTerm);

                    if (isMatch) {
                        matchCount++;
                        if (!firstMatch) firstMatch = row;
                        row.classList.remove('search-hidden');
                    } else {
                        row.classList.add('search-hidden');
                    }
                });

                searchCount.textContent = matchCount;

                // Scroll to first match
                if (firstMatch) {
                    firstMatch.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }

                // Apply filters after search
                applyFilters();
            }

            function clearSearchFunction() {
                studentSearch.value = '';
                clearSearch.classList.add('hidden');
                searchResults.classList.add('hidden');

                // Remove search-hidden class and reset all rows to visible
                tableRows.forEach(row => {
                    row.classList.remove('search-hidden');
                    // Reset row display to empty string to show all rows
                    row.style.display = '';
                });

                // Apply filters only for batch/group, not search
                applyFilters();
            }

            function updateGroupFilter(selectedBatch) {
                // Clear and disable group filter if no batch is selected
                if (!selectedBatch) {
                    groupFilter.value = '';
                    groupFilter.disabled = true;
                    return;
                }

                // Enable group filter
                groupFilter.disabled = false;

                // Get unique groups for the selected batch
                const availableGroups = new Set();
                tableRows.forEach(row => {
                    if (row.getAttribute('data-batch') === selectedBatch) {
                        availableGroups.add(row.getAttribute('data-group'));
                    }
                });

                // Update group filter options
                groupFilter.innerHTML = '<option value="">All Groups</option>';
                Array.from(availableGroups).sort().forEach(group => {
                    const option = document.createElement('option');
                    option.value = group;
                    option.textContent = group;
                    groupFilter.appendChild(option);
                });
            }

            function applyFilters() {
                const selectedBatch = batchFilter.value;
                const selectedGroup = groupFilter.value;
                const selectedStatus = statusFilter.value;
                const hasSearchTerm = studentSearch.value.trim() !== '';

                // Filter and reapply color coding
                let visibleRows = [];
                tableRows.forEach(row => {
                    const rowBatch = row.getAttribute('data-batch');
                    const rowGroup = row.getAttribute('data-group');

                    const batchMatch = !selectedBatch || rowBatch === selectedBatch;
                    const groupMatch = !selectedGroup || rowGroup === selectedGroup;

                    // Only apply search filter if there's an active search term
                    let shouldShow = batchMatch && groupMatch;

                     // Apply status filter
                    if (selectedStatus && shouldShow) {
                        shouldShow = shouldShow && checkStudentStatusGoingOut(row, selectedStatus);
                    }

                    // If there's a search term, also check if row is not hidden by search
                    if (hasSearchTerm) {
                        shouldShow = shouldShow && !row.classList.contains('search-hidden');
                    }

                    row.style.display = shouldShow ? '' : 'none';

                    if (shouldShow) {
                        visibleRows.push(row);
                    }
                });

                // First, remove all color classes from ALL rows (visible and hidden)
                tableRows.forEach(row => {
                    row.className = row.className.replace(/bg-orange-100|hover:bg-orange-150|border-l-4|border-orange-500|bg-gray-200|hover:bg-gray-250|border-gray-500|bg-yellow-800|bg-opacity-20|hover:bg-yellow-800|hover:bg-opacity-30|border-yellow-800/g, '').trim();

                    // Add base classes
                    if (!row.className.includes('border-b border-gray-200 hover:bg-gray-50')) {
                        row.className += ' border-b border-gray-200 hover:bg-gray-50';
                    }
                });

                // Filter visible rows that have actual activity (time_out or time_in)
                let activeVisibleRows = visibleRows.filter(row => {
                    const timeOutCell = row.cells[5]; // Log Out Time column
                    const timeInCell = row.cells[7]; // Log In Time column

                    // Check for actual time values using timestamp pattern detection
                    const timeOutText = timeOutCell ? timeOutCell.textContent.trim() : '';
                    const timeInText = timeInCell ? timeInCell.textContent.trim() : '';

                    // Use regex to detect actual timestamp patterns (e.g., "2:30 PM", "14:30", "02:30:45")
                    const timePattern = /\d{1,2}:\d{2}(\s*(AM|PM|am|pm))?/;

                    // Only consider it as activity if it contains actual timestamp
                    const hasTimeOut = timeOutText &&
                                      timeOutText !== '—' &&
                                      timeOutText !== '' &&
                                      timePattern.test(timeOutText);

                    const hasTimeIn = timeInText &&
                                     timeInText !== '—' &&
                                     timeInText !== '' &&
                                     timePattern.test(timeInText);

                    return hasTimeOut || hasTimeIn;
                });

                // Apply color coding ONLY to active visible rows
                activeVisibleRows.forEach((row, activeIndex) => {
                    if (activeIndex === 0) {
                        row.className += ' bg-orange-100 hover:bg-orange-100 border-l-4 border-orange-500'; // 1st latest - Orange
                    } else if (activeIndex === 1 && activeVisibleRows.length >= 2) {
                        row.className += ' bg-gray-200 hover:bg-gray-200 border-l-4 border-gray-500'; // 2nd latest - Silver
                    } else if (activeIndex === 2 && activeVisibleRows.length >= 3) {
                        row.className += ' bg-gray-100 hover:bg-gray-100 border-l-4 border-gray-500'; // 3rd latest - Brown
                    }
                });
            }

            // Search event listeners
            studentSearch.addEventListener('input', performSearch);
            clearSearch.addEventListener('click', clearSearchFunction);

            // When batch changes, update group filter and apply filters
            batchFilter.addEventListener('change', function() {
                const selectedBatch = this.value;
                updateGroupFilter(selectedBatch);
                applyFilters();
            });

            // When group changes, apply filters
            groupFilter.addEventListener('change', applyFilters);


            // When status changes, apply filters
            statusFilter.addEventListener('change', applyFilters);

            // Function to check student status for going-out filtering
            function checkStudentStatusGoingOut(row, statusFilter) {
                const timeOutCell = row.cells[8]; // Log Out Time column
                const timeInCell = row.cells[11]; // Log In Time column
                const timeOutRemarkCell = row.cells[9]; // Log Out Remarks column
                const timeInRemarkCell = row.cells[12]; // Log In Remarks column
                const timeOutConsiderationCell = row.cells[10]; // Log Out Consideration column
                const timeInConsiderationCell = row.cells[13]; // Log In Consideration column

                const timeOutText = timeOutCell ? timeOutCell.textContent.trim() : '';
                const timeInText = timeInCell ? timeInCell.textContent.trim() : '';
                const timeOutRemark = timeOutRemarkCell ? timeOutRemarkCell.textContent.trim() : '';
                const timeInRemark = timeInRemarkCell ? timeInRemarkCell.textContent.trim() : '';
                const timeOutConsideration = timeOutConsiderationCell ? timeOutConsiderationCell.textContent.trim() : '';
                const timeInConsideration = timeInConsiderationCell ? timeInConsiderationCell.textContent.trim() : '';

                // Use regex to detect actual timestamp patterns
                const timePattern = /\d{1,2}:\d{2}(\s*(AM|PM|am|pm))?/;
                const hasTimeOut = timeOutText && timePattern.test(timeOutText) && timeOutText !== '—';
                const hasTimeIn = timeInText && timePattern.test(timeInText) && timeInText !== '—';

                switch (statusFilter) {
                    case 'not-logged-in':
                        // Students who haven't logged in yet (no time_in)
                        return !hasTimeIn;

                    case 'late':
                        // Students who are late (either log out or log in)
                        return (timeOutRemark === 'Late') || (timeInRemark === 'Late');

                    case 'absent':
                        // Students who are marked as absent
                        // Only check for students who have "Absent" as their SAVED consideration
                        // Note: In going-out logs, "Absent" might be displayed as "Not going out"

                        // Look for the specific structure that indicates a saved "Absent" consideration
                        const timeOutAbsentElement = timeOutConsiderationCell.querySelector('.font-semibold');
                        const timeInAbsentElement = timeInConsiderationCell.querySelector('.font-semibold');

                        const timeOutAbsent = timeOutAbsentElement &&
                                            (timeOutAbsentElement.textContent.trim() === 'Absent' ||
                                             timeOutAbsentElement.textContent.trim() === 'Not going out');
                        const timeInAbsent = timeInAbsentElement &&
                                           (timeInAbsentElement.textContent.trim() === 'Absent' ||
                                            timeInAbsentElement.textContent.trim() === 'Not going out');

                        // Fallback: also check text content for simple cases
                        const timeOutAbsentFallback = (timeOutConsideration.includes('Absent') ||
                                                     timeOutConsideration.includes('Not going out')) &&
                                                    !timeOutConsiderationCell.querySelector('select'); // Not a dropdown
                        const timeInAbsentFallback = (timeInConsideration.includes('Absent') ||
                                                    timeInConsideration.includes('Not going out')) &&
                                                   !timeInConsiderationCell.querySelector('select'); // Not a dropdown

                        const isAbsent = timeOutAbsent || timeInAbsent || timeOutAbsentFallback || timeInAbsentFallback;

                        // Debug logging
                        if (isAbsent) {
                            console.log('Found absent student in going-out logs:', {
                                studentId: row.cells[0].textContent,
                                timeOutAbsent: timeOutAbsent,
                                timeInAbsent: timeInAbsent,
                                timeOutAbsentFallback: timeOutAbsentFallback,
                                timeInAbsentFallback: timeInAbsentFallback
                            });
                        }

                        return isAbsent;

                    default:
                        return true;
                }
            }


            // Apply initial filters (this will also apply initial colors)
            applyFilters();
        });

        // Step 1: Handle choice selection
        function handleChoiceSelection(selectElement, logId, considerationType) {
            const selectedValue = selectElement.value;
            const formContainer = selectElement.closest('.consideration-form');
            const nextBtn = formContainer.querySelector('.next-btn');

            if (selectedValue) {
                nextBtn.classList.remove('hidden');
            } else {
                nextBtn.classList.add('hidden');
            }
        }

        // Step 2: Show reason input step
        function showReasonStep(logId, considerationType) {
            const formContainer = document.querySelector(`[data-log-id="${logId}"][data-type="${considerationType}"]`);
            const choiceStep = formContainer.querySelector('.choice-step');
            const reasonStep = formContainer.querySelector('.reason-step');
            const selectedChoice = formContainer.querySelector('.consideration-select').value;
            const choiceText = formContainer.querySelector('.consideration-select option:checked').textContent;

            // Hide choice step and show reason step
            choiceStep.classList.add('hidden');
            reasonStep.classList.remove('hidden');

            // Display selected choice
            reasonStep.querySelector('.choice-text').textContent = choiceText;

            // Focus on textarea
            reasonStep.querySelector('.reason-input').focus();
        }

        // Step 3: Save consideration
        function saveConsideration(logId, considerationType) {
            const formContainer = document.querySelector(`[data-log-id="${logId}"][data-type="${considerationType}"]`);
            const selectedChoice = formContainer.querySelector('.consideration-select').value;
            const reason = formContainer.querySelector('.reason-input').value.trim();
            const saveBtn = formContainer.querySelector('.save-btn');
            const errorDiv = document.getElementById(`${considerationType === 'time_out' ? 'timeout' : 'timein'}-error-${logId}`);
            const successDiv = document.getElementById(`${considerationType === 'time_out' ? 'timeout' : 'timein'}-success-${logId}`);

            // Validation
            if (!selectedChoice) {
                alert('Please select a consideration.');
                return;
            }

            if (!reason) {
                alert('Please enter a reason for this consideration.');
                formContainer.querySelector('.reason-input').focus();
                return;
            }

            // Clear previous messages
            errorDiv.classList.add('hidden');
            successDiv.classList.add('hidden');

            // Disable save button
            saveBtn.disabled = true;
            saveBtn.textContent = 'Saving...';

            // Prepare form data
            const formData = new FormData();
            formData.append('consideration', selectedChoice);
            formData.append('reason', reason);
            formData.append('consideration_type', considerationType);
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            fetch('{{ route('monitor.goingout.consideration', ':id') }}'.replace(':id', logId), {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    let successMessage = 'Consideration saved successfully!';
                    if (selectedChoice === 'Not going out') {
                        successMessage = 'Student marked as Not going out - Both considerations updated!';
                    }

                    successDiv.innerHTML = `
                        <div class="flex items-center space-x-2">
                            <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-green-600 font-medium">${successMessage}</span>
                        </div>
                    `;
                    successDiv.classList.remove('hidden');

                    // If Not going out was selected, also update the other consideration form
                    if (selectedChoice === 'Not going out') {
                        const otherType = considerationType === 'time_out' ? 'time_in' : 'time_out';
                        const otherFormContainer = document.querySelector(`[data-log-id="${logId}"][data-type="${otherType}"]`);

                        if (otherFormContainer) {
                            // Replace the other form with Not going out consideration
                            setTimeout(() => {
                                otherFormContainer.outerHTML = `
                                    <div class="p-3 bg-gray-50 rounded-lg border">
                                        <div class="font-semibold text-sm text-gray-600">
                                            Not going out
                                        </div>
                                        <div class="text-xs text-gray-600 mt-1 italic">${reason}</div>
                                        <div class="text-xs text-blue-600 mt-1 font-medium">
                                            Set by: {{ session('user.user_fname') }} {{ session('user.user_lname') }} (Auto-synced)
                                        </div>
                                    </div>
                                `;
                            }, 500);
                        }
                    }

                    // Replace current form with saved consideration display
                    setTimeout(() => {
                        const colorClass = selectedChoice === 'Excused' ? 'text-green-600' :
                                         selectedChoice === 'Not going out' ? 'text-gray-600' : 'text-red-600';

                        formContainer.outerHTML = `
                            <div class="p-3 bg-gray-50 rounded-lg border">
                                <div class="font-semibold text-sm ${colorClass}">
                                    ${selectedChoice}
                                </div>
                                <div class="text-xs text-gray-600 mt-1 italic">${reason}</div>
                                <div class="text-xs text-blue-600 mt-1 font-medium">
                                    Set by: {{ session('user.user_fname') }} {{ session('user.user_lname') }}
                                </div>
                            </div>
                        `;
                    }, 2000);
                } else {
                    errorDiv.textContent = data.message || 'Failed to save consideration.';
                    errorDiv.classList.remove('hidden');
                    saveBtn.disabled = false;
                    saveBtn.textContent = 'Save Consideration';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                errorDiv.textContent = 'An error occurred while saving the consideration.';
                errorDiv.classList.remove('hidden');
                saveBtn.disabled = false;
                saveBtn.textContent = 'Save Consideration';
            });
        }

        // Show logout form with destination and purpose fields
        function showLogoutForm(logId) {
            document.getElementById(`logout-form-${logId}`).classList.add('hidden');
            document.getElementById(`logout-details-${logId}`).classList.remove('hidden');
            // Focus on destination field
            document.getElementById(`destination-${logId}`).focus();
        }

        // Hide logout form and show button again
        function hideLogoutForm(logId) {
            document.getElementById(`logout-form-${logId}`).classList.remove('hidden');
            document.getElementById(`logout-details-${logId}`).classList.add('hidden');
            // Clear the input fields
            document.getElementById(`destination-${logId}`).value = '';
            document.getElementById(`purpose-${logId}`).value = '';
        }

        // Monitor Log Out Function with destination and purpose
        function performLogOutWithDetails(logId, studentId) {
            const destination = document.getElementById(`destination-${logId}`).value.trim();
            const purpose = document.getElementById(`purpose-${logId}`).value.trim();

            // Validation
            if (!destination) {
                alert('Please enter a destination.');
                document.getElementById(`destination-${logId}`).focus();
                return;
            }

            if (!purpose) {
                alert('Please enter a purpose.');
                document.getElementById(`purpose-${logId}`).focus();
                return;
            }

            if (!confirm(`Are you sure you want to log out student ${studentId}?\nDestination: ${destination}\nPurpose: ${purpose}`)) {
                return;
            }

            const formData = new FormData();
            formData.append('destination', destination);
            formData.append('purpose', purpose);
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            fetch(`{{ route('monitor.goingout.logout', ':id') }}`.replace(':id', logId), {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the page to show updated data
                    location.reload();
                } else {
                    alert(data.message || 'Failed to log out student.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while logging out the student.');
            });
        }

        // Monitor Log In Function
        function performLogIn(logId, studentId) {
            if (!confirm(`Are you sure you want to log in student ${studentId}?`)) {
                return;
            }

            const formData = new FormData();
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

            fetch(`{{ route('monitor.goingout.login', ':id') }}`.replace(':id', logId), {
                method: 'POST',
                body: formData,
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload the page to show updated data
                    location.reload();
                } else {
                    alert(data.message || 'Failed to log in student.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while logging in the student.');
            });
        }
    </script>

    {{-- Custom CSS for Latest Activity Color Coding and Search --}}
    <style>
        /* Latest Activity Color Coding */
        .bg-orange-150 {
            background-color: #fed7aa;
        }
        .hover\:bg-orange-150:hover {
            background-color: #fdba74;
        }
        .bg-gray-250 {
            background-color: #e5e7eb;
        }
        .hover\:bg-gray-250:hover {
            background-color: #d1d5db;
        }

        /* Search functionality styles */
        .search-hidden {
            display: none !important;
        }
    </style>
</x-monitorLogLayout>
