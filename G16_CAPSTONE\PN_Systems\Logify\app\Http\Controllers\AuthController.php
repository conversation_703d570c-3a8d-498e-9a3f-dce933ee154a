<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use <PERSON><PERSON>\Sanctum\PersonalAccessToken;

class AuthController extends Controller
{
    public function dashboard(Request $request)
    {
        // 1️⃣ Grab the token coming in from ?token=.....
        $token = $request->query('token');

        // If the link was hit with no token at all, bail out early
        if (empty($token)) {
            // dd("token empty");
            return redirect(env('MAIN_SYSTEM_URL') . '/login')->with('error', 'Token missing');
        }

        // 2️⃣ Keep it in session for later API calls if you need it
        session(['api_token' => $token]);

        // 3️⃣ Ask the main system who this user is
        $response = Http::withToken($token)
            ->get(env('MAIN_SYSTEM_URL') . '/api/user');

        if (! $response->successful()) {
            // dd("token invalid");
            // Bad / expired / forged token — back to login
            return redirect(env('MAIN_SYSTEM_URL') . '/login')->with('error', 'Invalid token');
        }

        // 4️⃣ Parse the JSON we just got
        $userData = $response->json();

        // 5️⃣ Make sure they’re actually an educator
        if (!isset($userData['user_role']) || $userData['user_role'] !== 'educator') {
            // Not an educator? Kick them back to the main menu with an error flag
            return redirect()->to(
                env('MAIN_SYSTEM_URL') . "/main-menu?error=Access+denied&token={$token}"
            );
        }

        // 6️⃣ All good – stash the user in session (optional) and head to the dashboard
        session(['user' => $userData]);

        return redirect()->route('educator.dashboard');
    }


    public function logout(Request $request)
    {
        $tokenString = session('api_token');          // the plain text token
        PersonalAccessToken::findToken($tokenString)?->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->to(env('MAIN_SYSTEM_URL') . '/login');
    }
}
