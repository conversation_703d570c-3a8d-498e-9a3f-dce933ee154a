

.dashboard-container {
    padding: 20px;

    margin: 0 auto;
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #22bbea;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    color: white;
}

.stat-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.stat-number {
    margin: 0;
    font-size: 48px;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    margin: 0;
    font-size: 18px;
    text-decoration: none;
    color: black;
}



.recent-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.recent-item {
    display: grid;
    grid-template-columns: auto 1fr auto;
    align-items: center;
    gap: 12px;
    padding: 10px;
    border-radius: 6px;
    background: #f8f9fa;
    transition: background-color 0.2s;
}

.recent-item:hover {
    background: #e9ecef;
}

.recent-item i {
    color: #4CAF50;
    font-size: 1.2em;
}

.recent-item div {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.recent-item strong {
    color: #333;
    font-size: 0.95em;
}

.recent-item small {
    color: #666;
    font-size: 0.85em;
}

.recent-date {
    color: #888;
    font-size: 0.85em;
    white-space: nowrap;
}

.chart-card {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-top: 30px;
    text-align: center;
    width: 100%;
}

.chart-card h3 {
    margin: 0 0 20px;
    color: #333;
    text-align: center;
}

hr {
    border: 1px solid #ddd; /* Light gray border */
    margin: 20px 0; /* Add spacing above and below */
    width: 100%; /* Ensure it spans the full width */
}