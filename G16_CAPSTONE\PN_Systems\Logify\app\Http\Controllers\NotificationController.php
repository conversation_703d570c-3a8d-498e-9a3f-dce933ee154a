<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use App\Models\Academic;
use App\Models\Going_out;
use App\Models\NotificationView;
use App\Models\Visitor;
use Carbon\Carbon;

class NotificationController extends Controller
{
    /**
     * Get notification counts for academic, going out, visitor logs, and late students
     */
    public function getNotificationCounts()
    {
        try {
            $academicCounts = $this->getAcademicNotificationCounts();
            $goingOutCounts = $this->getGoingOutNotificationCounts();
            $visitorCounts = $this->getVisitorNotificationCounts();
            $lateCounts = $this->getLateStudentNotificationCounts();

            return response()->json([
                'academic' => $academicCounts,
                'goingout' => $goingOutCounts,
                'visitor' => $visitorCounts,
                'late' => $lateCounts,
                'success' => true
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'academic' => ['timeout' => 0, 'timein' => 0],
                'goingout' => ['timeout' => 0, 'timein' => 0],
                'visitor' => ['timeout' => 0, 'timein' => 0],
                'late' => ['count' => 0],
                'success' => false,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get academic logs notification counts (separate for time in and time out)
     */
    private function getAcademicNotificationCounts()
    {
        $lastViewed = NotificationView::getLastViewed('academic');
        $today = now()->format('Y-m-d');

        // Count time out activities
        $timeOutQuery = Academic::whereDate('academic_date', $today)
            ->whereNotNull('time_out');

        if ($lastViewed) {
            $timeOutQuery->where(function($q) use ($lastViewed) {
                $q->where('updated_at', '>', $lastViewed)
                  ->orWhere('created_at', '>', $lastViewed);
            });
        }

        // Count time in activities
        $timeInQuery = Academic::whereDate('academic_date', $today)
            ->whereNotNull('time_in');

        if ($lastViewed) {
            $timeInQuery->where(function($q) use ($lastViewed) {
                $q->where('updated_at', '>', $lastViewed)
                  ->orWhere('created_at', '>', $lastViewed);
            });
        }

        return [
            'timeout' => $timeOutQuery->count(),
            'timein' => $timeInQuery->count()
        ];
    }

    /**
     * Get going out logs notification counts (separate for time in and time out)
     */
    private function getGoingOutNotificationCounts()
    {
        $lastViewed = NotificationView::getLastViewed('goingout');
        $today = now()->format('Y-m-d');

        // Count time out activities
        $timeOutQuery = Going_out::whereDate('going_out_date', $today)
            ->whereNotNull('time_out');

        if ($lastViewed) {
            $timeOutQuery->where(function($q) use ($lastViewed) {
                $q->where('updated_at', '>', $lastViewed)
                  ->orWhere('created_at', '>', $lastViewed);
            });
        }

        // Count time in activities
        $timeInQuery = Going_out::whereDate('going_out_date', $today)
            ->whereNotNull('time_in');

        if ($lastViewed) {
            $timeInQuery->where(function($q) use ($lastViewed) {
                $q->where('updated_at', '>', $lastViewed)
                  ->orWhere('created_at', '>', $lastViewed);
            });
        }

        return [
            'timeout' => $timeOutQuery->count(),
            'timein' => $timeInQuery->count()
        ];
    }

    /**
     * Get visitor logs notification counts (separate for time in and time out)
     */
    private function getVisitorNotificationCounts()
    {
        $lastViewed = NotificationView::getLastViewed('visitor');
        $today = now()->format('Y-m-d');

        // Count time out activities (visitors who have left)
        $timeOutQuery = Visitor::whereDate('visitor_date', $today)
            ->whereNotNull('time_out');

        if ($lastViewed) {
            $timeOutQuery->where(function($q) use ($lastViewed) {
                $q->where('updated_at', '>', $lastViewed)
                  ->orWhere('created_at', '>', $lastViewed);
            });
        }

        // Count time in activities (new visitors who arrived)
        $timeInQuery = Visitor::whereDate('visitor_date', $today)
            ->whereNotNull('time_in');

        if ($lastViewed) {
            $timeInQuery->where(function($q) use ($lastViewed) {
                $q->where('updated_at', '>', $lastViewed)
                  ->orWhere('created_at', '>', $lastViewed);
            });
        }

        return [
            'timeout' => $timeOutQuery->count(),
            'timein' => $timeInQuery->count()
        ];
    }

    /**
     * Get late student notification counts
     */
    private function getLateStudentNotificationCounts()
    {
        $lastViewed = NotificationView::getLastViewed('late');
        $today = now()->format('Y-m-d');

        // Count late students from academic logs
        $academicLateQuery = Academic::whereDate('academic_date', $today)
            ->where('time_in_remark', 'Late');

        if ($lastViewed) {
            $academicLateQuery->where(function($q) use ($lastViewed) {
                $q->where('updated_at', '>', $lastViewed)
                  ->orWhere('created_at', '>', $lastViewed);
            });
        }

        // Count late students from going out logs
        $goingOutLateQuery = Going_out::whereDate('going_out_date', $today)
            ->where('time_in_remark', 'Late');

        if ($lastViewed) {
            $goingOutLateQuery->where(function($q) use ($lastViewed) {
                $q->where('updated_at', '>', $lastViewed)
                  ->orWhere('created_at', '>', $lastViewed);
            });
        }

        $totalLateCount = $academicLateQuery->count() + $goingOutLateQuery->count();

        return [
            'count' => $totalLateCount
        ];
    }

    /**
     * Mark academic logs as viewed
     */
    public function markAcademicAsViewed()
    {
        NotificationView::markAsViewed('academic');

        return response()->json([
            'success' => true,
            'message' => 'Academic notifications marked as viewed'
        ]);
    }

    /**
     * Mark going out logs as viewed
     */
    public function markGoingOutAsViewed()
    {
        NotificationView::markAsViewed('goingout');

        return response()->json([
            'success' => true,
            'message' => 'Going out notifications marked as viewed'
        ]);
    }

    /**
     * Mark visitor logs as viewed
     */
    public function markVisitorAsViewed()
    {
        NotificationView::markAsViewed('visitor');

        return response()->json([
            'success' => true,
            'message' => 'Visitor notifications marked as viewed'
        ]);
    }

    /**
     * Mark late student notifications as viewed
     */
    public function markLateAsViewed()
    {
        NotificationView::markAsViewed('late');

        return response()->json([
            'success' => true,
            'message' => 'Late student notifications marked as viewed'
        ]);
    }

    /**
     * Get late students with identification for red dots
     * Shows ALL students with late activity from today (for red dots)
     */
    public function getLateStudentsWithActivity()
    {
        try {
            $today = now()->format('Y-m-d');
            $lateStudents = [];

            // Get ALL late students from academic logs today (not filtered by last viewed)
            $academicLateStudents = Academic::with('studentDetail')
                ->whereDate('academic_date', $today)
                ->where('time_in_remark', 'Late')
                ->get();

            foreach ($academicLateStudents as $student) {
                if ($student->studentDetail) {
                    $lateStudents[] = [
                        'student_id' => $student->student_id,
                        'type' => 'academic',
                        'date' => $student->academic_date,
                        'batch' => $student->studentDetail->batch ?? null,
                        'group' => $student->studentDetail->group ?? null,
                        'time_in' => $student->time_in,
                        'created_at' => $student->created_at ? $student->created_at->format('Y-m-d H:i:s') : null
                    ];
                }
            }

            // Get ALL late students from going out logs today (not filtered by last viewed)
            $goingOutLateStudents = Going_out::with('studentDetail')
                ->whereDate('going_out_date', $today)
                ->where('time_in_remark', 'Late')
                ->get();

            foreach ($goingOutLateStudents as $student) {
                if ($student->studentDetail) {
                    $lateStudents[] = [
                        'student_id' => $student->student_id,
                        'type' => 'going_out',
                        'date' => $student->going_out_date,
                        'batch' => $student->studentDetail->batch ?? null,
                        'group' => $student->studentDetail->group ?? null,
                        'time_in' => $student->time_in,
                        'created_at' => $student->created_at ? $student->created_at->format('Y-m-d H:i:s') : null
                    ];
                }
            }

            // Remove duplicates (same student might be late in both academic and going out)
            $uniqueLateStudents = [];
            $seenStudents = [];

            foreach ($lateStudents as $student) {
                if (!in_array($student['student_id'], $seenStudents)) {
                    $uniqueLateStudents[] = $student;
                    $seenStudents[] = $student['student_id'];
                }
            }

            return response()->json([
                'success' => true,
                'students' => $uniqueLateStudents,
                'total_count' => count($uniqueLateStudents),
                'debug_info' => [
                    'academic_late' => count($academicLateStudents),
                    'going_out_late' => count($goingOutLateStudents),
                    'unique_students' => count($uniqueLateStudents)
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'students' => []
            ]);
        }
    }
}
