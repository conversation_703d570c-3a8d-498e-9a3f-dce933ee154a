<x-monitorLogLayout>
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <div class="p-8 bg-white shadow-md rounded-2xl">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-bold text-orange-700">VISITOR LOGS MONITOR</h1>
            <div class="flex items-center space-x-4">
                <form action="{{ route('monitor.visitor.logs') }}" method="GET" class="flex items-center space-x-2">
                    <input type="date" name="date" value="{{ $selectedDate }}"
                        class="px-3 py-2 text-sm border border-gray-300 rounded-sm focus:outline-none focus:ring-2 focus:ring-orange-500">
                    <button type="submit"
                        class="px-4 py-2 text-sm font-semibold text-white bg-orange-500 rounded-sm hover:bg-orange-600">
                        Filter
                    </button>
                </form>

                <a href="{{ route('monitor.dashboard') }}"
                    class="inline-flex items-center text-sm font-medium text-blue-600 hover:underline">
                    <i data-feather="arrow-left" class="w-5 h-5 mr-1"></i> Back to Dashboard
                </a>
            </div>
        </div>

        {{-- Search Bar Only --}}
        <div class="mb-6">
            <div class="flex items-center space-x-4">
                <label for="visitorSearch" class="text-sm font-medium text-gray-700">Search Visitors:</label>
                <div class="relative flex-1 max-w-md">
                    <input type="text" id="visitorSearch" placeholder="Search by Visitor Name, ID Number, or Purpose..."
                           class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 pr-8">
                    <button type="button" id="clearSearch" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 hidden">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div id="searchResults" class="text-sm text-gray-600 hidden">
                    <span id="searchCount">0</span> visitor(s) found
                </div>
            </div>
        </div>

        {{-- Table --}}
        <div class="relative w-full overflow-hidden border border-gray-200 rounded-lg">
                <div class="w-full overflow-x-auto table-container">
                    <div class="overflow-y-auto max-h-[calc(100vh-200px)] table-container">
                        <table class="w-full min-w-full text-sm text-left text-gray-700">
                    <thead class="text-xs font-semibold tracking-wider uppercase bg-gray-100">
                    <tr>
                        <th class="px-6 py-3 text-black">Visitor Pass</th>
                        <th class="px-6 py-3 text-black">Visitor Name</th>
                        <th class="px-6 py-3 text-black">Valid ID</th>
                        <th class="px-6 py-3 text-black">ID Number</th>
                        <th class="px-6 py-3 text-black">Relationship</th>
                        <th class="px-6 py-3 text-black">Purpose</th>
                        <th class="px-6 py-3 text-black">Date</th>
                        <th class="px-6 py-3 text-black">Time In</th>
                        <th class="px-6 py-3 text-black">Time Out</th>
                        <th class="px-6 py-3 text-black">Consideration</th>
                    </tr>
                </thead>
                <tbody class="text-sm text-gray-700">
                    @forelse($logs as $log)
                        <tr class="border-b border-gray-200 hover:bg-gray-50"
                            data-visitor-name="{{ strtolower($log->visitor_name ?? '') }}"
                            data-id-number="{{ strtolower($log->id_number ?? '') }}"
                            data-purpose="{{ strtolower($log->purpose ?? '') }}"
                            data-date="{{ $log->visitor_date }}"
                            data-status="{{ $log->time_out ? 'completed' : 'active' }}">
                            <td class="px-6 py-4 font-medium">{{ $log->visitor_pass ?? '—' }}</td>
                            <td class="px-6 py-4">{{ $log->visitor_name ?? '—' }}</td>
                            <td class="px-6 py-4">{{ $log->valid_id ?? '—' }}</td>
                            <td class="px-6 py-4">{{ $log->id_number ?? '—' }}</td>
                            <td class="px-6 py-4">{{ $log->relationship ?? '—' }}</td>
                            <td class="px-6 py-4">{{ $log->purpose ?? '—' }}</td>
                            <td class="px-6 py-4">{{ \Carbon\Carbon::parse($log->visitor_date)->format('M j, Y') }}</td>
                            <td class="px-6 py-4">{{ $log->formatted_time_in }}</td>
                            <td class="px-6 py-4">{{ $log->formatted_time_out }}</td>
                            <td class="px-6 py-4">
                                @if(!$log->time_out)
                                    <form action="{{ route('visitor.logOut', $log->id) }}" method="POST">
                                        @csrf
                                        <button type="submit"
                                            class="px-2 py-2 text-sm font-semibold text-white bg-blue-500 rounded hover:text-white hover:bg-blue-700">Log
                                            Out</button>
                                    </form>
                                @else
                                    <span class="text-sm text-gray-400">Completed</span>
                                @endif
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="10" class="px-6 py-8 text-center text-gray-500">
                                <i data-feather="inbox" class="w-8 h-8 mx-auto mb-2"></i>
                                <div>No visitor logs found for the selected criteria.</div>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Feather icons are already initialized in the layout

        document.addEventListener('DOMContentLoaded', function() {
            // Search functionality only
            const visitorSearch = document.getElementById('visitorSearch');
            const clearSearch = document.getElementById('clearSearch');
            const searchResults = document.getElementById('searchResults');
            const searchCount = document.getElementById('searchCount');
            const tableRows = document.querySelectorAll('tbody tr[data-visitor-name]');

            // Search functionality
            function performSearch() {
                const searchTerm = visitorSearch.value.toLowerCase().trim();

                if (searchTerm === '') {
                    clearSearch.classList.add('hidden');
                    searchResults.classList.add('hidden');

                    // Reset all rows to visible when search is empty
                    tableRows.forEach(row => {
                        row.style.display = '';
                    });
                    return;
                }

                clearSearch.classList.remove('hidden');
                searchResults.classList.remove('hidden');

                let matchCount = 0;
                let firstMatch = null;

                tableRows.forEach(row => {
                    const visitorName = row.getAttribute('data-visitor-name') || '';
                    const idNumber = row.getAttribute('data-id-number') || '';
                    const purpose = row.getAttribute('data-purpose') || '';

                    const isMatch = visitorName.includes(searchTerm) ||
                                   idNumber.includes(searchTerm) ||
                                   purpose.includes(searchTerm);

                    if (isMatch) {
                        matchCount++;
                        if (!firstMatch) firstMatch = row;
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });

                searchCount.textContent = matchCount;

                // Scroll to first match
                if (firstMatch) {
                    firstMatch.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }

            function clearSearchFunction() {
                visitorSearch.value = '';
                clearSearch.classList.add('hidden');
                searchResults.classList.add('hidden');

                // Reset all rows to visible
                tableRows.forEach(row => {
                    row.style.display = '';
                });
            }

            // Search event listeners
            visitorSearch.addEventListener('input', performSearch);
            clearSearch.addEventListener('click', clearSearchFunction);
        });

        function handleConsiderationSubmit(event, logId) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);
            const errorDiv = document.getElementById(`visitor-error-${logId}`);
            const successDiv = document.getElementById(`visitor-success-${logId}`);

            // Clear previous messages
            errorDiv.classList.add('hidden');
            successDiv.classList.add('hidden');

            // Disable form
            const submitButton = form.querySelector('button[type="submit"]');
            const select = form.querySelector('select');
            const textarea = form.querySelector('textarea');

            submitButton.disabled = true;
            select.disabled = true;
            textarea.disabled = true;

            fetch('{{ route('monitor.visitor.consideration', ':id') }}'.replace(':id', logId), {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    successDiv.textContent = data.message;
                    successDiv.classList.remove('hidden');

                    // Replace form with result display
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    errorDiv.textContent = data.message || 'An error occurred';
                    errorDiv.classList.remove('hidden');

                    // Re-enable form
                    submitButton.disabled = false;
                    select.disabled = false;
                    textarea.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                errorDiv.textContent = 'An error occurred while updating consideration';
                errorDiv.classList.remove('hidden');

                // Re-enable form
                submitButton.disabled = false;
                select.disabled = false;
                textarea.disabled = false;
            });

            return false;
        }
    </script>
</x-monitorLogLayout>
