<?php

use App\Http\Controllers\VisitorLogController;
use App\Http\Controllers\AcademicLogController;
use App\Http\Controllers\GoingOutLogController;
use App\Http\Controllers\RoleSwitchController;
use Illuminate\Support\Facades\Route;
use App\Models\PNUser;
use App\Http\Controllers\MonitorScheduleController;
use App\Http\Controllers\MonitorController;
use App\Http\Controllers\EducatorController;
use App\Http\Controllers\NotificationController;
use App\Models\StudentDetail;
use App\Http\Controllers\AuthController;
use App\Http\Middleware\SubsystemAuth;


Route::get('/', [AuthController::class, 'dashboard']);
Route::get('/login', function () {
    return redirect()->to(env('MAIN_SYSTEM_URL'). '/login');
})->name('login');
Route::post('logout', [AuthController::class, 'logout'])
    ->name('logout');

// Protected routes (require authentication)
Route::middleware([SubsystemAuth::class])->group(function () {

    // Role switching routes (for educators only)
    // Route::middleware(['can:isEducator'])->group(function () {
        Route::post('/role/switch', [RoleSwitchController::class, 'switchMode'])->name('role.switch');
        Route::get('/role/current-mode', [RoleSwitchController::class, 'getCurrentMode'])->name('role.current-mode');
    // });

    // Educator-specific routes
    // Route::middleware(['can:isEducator'])->group(function () {
        Route::get('/educator/dashboard', [EducatorController::class, 'show'])->name('educator.dashboard');

        Route::get('/educator/academic-monitor', [AcademicLogController::class, 'monitor'])->name('academic.monitor');
        Route::get('/educator/academic-monitor/past-logs', [AcademicLogController::class, 'pastLogs'])->name('academic.past_logs');
        Route::post('/educator/academic-monitor/{id}/consideration', [AcademicLogController::class, 'updateConsideration'])->name('academic.update_consideration');

        // Going Out Monitor Routes
        Route::get('/educator/goingout-monitor', [GoingOutLogController::class, 'monitor'])->name('goingout.monitor');
        Route::get('/educator/goingout-monitor/past-logs', [GoingOutLogController::class, 'pastLogs'])->name('goingout.past_logs');
        Route::post('/educator/goingout-monitor/{id}/consideration', [GoingOutLogController::class, 'updateConsideration'])->name('goingout.update_consideration');

        // Visitor Monitor Routes
        Route::get('/educator/visitor-monitor', [VisitorLogController::class, 'monitor'])->name('visitor.monitor');
        Route::get('/educator/visitor-monitor/past-logs', [VisitorLogController::class, 'pastLogs'])->name('visitor.past_logs');
        Route::post('/educator/visitor-monitor/{id}/accept', [VisitorLogController::class, 'accept'])->name('visitor.accept');
        Route::post('/educator/visitor-monitor/{id}/reject', [VisitorLogController::class, 'reject'])->name('visitor.reject');
        Route::post('/educator/visitor-monitor/{id}/consideration', [VisitorLogController::class, 'updateConsideration'])->name('visitor.update_consideration');

        // Dashboard Data Routes
        Route::get('/educator/today-attendance', [EducatorController::class, 'getTodayAttendance'])->name('educator.today-attendance');
        Route::get('/educator/goingout-attendance', [EducatorController::class, 'getGoingOutAttendance'])->name('educator.goingout-attendance');
        Route::get('/educator/academic-loginout-data', [EducatorController::class, 'getAcademicLogInOutData'])->name('educator.academic-loginout-data');
        Route::get('/educator/goingout-loginout-data', [EducatorController::class, 'getGoingOutLogInOutData'])->name('educator.goingout-loginout-data');
        Route::get('/educator/late-students-by-batch', [EducatorController::class, 'getLateStudentsByBatch'])->name('educator.late-students-by-batch');
        Route::get('/educator/time-inout-by-batch', [EducatorController::class, 'getTimeInOutByBatch'])->name('educator.time-inout-by-batch');
        Route::get('/educator/absent-students-by-batch', [EducatorController::class, 'getAbsentStudentsByBatch'])->name('educator.absent-students-by-batch');
        Route::get('/educator/student-data', [EducatorController::class, 'getStudentData'])->name('educator.student-data');
        Route::get('/educator/recent-activities', [EducatorController::class, 'getRecentActivities'])->name('educator.recent-activities');

        // Late Analytics Routes
        Route::get('/educator/late-analytics', function () {
            // Reset late notifications when educator accesses the page
            \App\Models\NotificationView::markAsViewed('late');
            return view('user-educator.late-and-analytics');
        })->name('educator.late-analytics');
        Route::get('/educator/late-analytics/data', [EducatorController::class, 'getLateAnalytics'])->name('educator.late-analytics.data');
        Route::get('/educator/student-late-history', [EducatorController::class, 'getStudentLateHistory'])->name('educator.student-late-history');

        // Absent Analytics Routes
        Route::get('/educator/absent-analytics/data', [EducatorController::class, 'getAbsentAnalytics'])->name('educator.absent-analytics.data');
        Route::get('/educator/student-absent-history', [EducatorController::class, 'getStudentAbsentHistory'])->name('educator.student-absent-history');

        // Notification Routes
        Route::get('/educator/notifications/counts', [NotificationController::class, 'getNotificationCounts'])->name('educator.notifications.counts');
        Route::post('/educator/notifications/academic/mark-viewed', [NotificationController::class, 'markAcademicAsViewed'])->name('educator.notifications.academic.mark-viewed');
        Route::post('/educator/notifications/goingout/mark-viewed', [NotificationController::class, 'markGoingOutAsViewed'])->name('educator.notifications.goingout.mark-viewed');
        Route::post('/educator/notifications/visitor/mark-viewed', [NotificationController::class, 'markVisitorAsViewed'])->name('educator.notifications.visitor.mark-viewed');
        Route::post('/educator/notifications/late/mark-viewed', [NotificationController::class, 'markLateAsViewed'])->name('educator.notifications.late.mark-viewed');

        //Monitoring
        Route::post('/switch', [RoleSwitchController::class, 'switchMode'])
            ->name('switch-mode');

        Route::get('/monitor/dashboard', [MonitorScheduleController::class, 'dashboard'])
            ->name('monitor.dashboard');

        Route::get('/monitor/get-groups', [MonitorScheduleController::class, 'getGroups'])
            ->name('monitor.get-groups');

        // Monitor dashboard data routes
        Route::get('/monitor/today-attendance', [MonitorController::class, 'getTodayAttendance'])->name('monitor.today-attendance');
        Route::get('/monitor/student-data', [MonitorController::class, 'getStudentData'])->name('monitor.student-data');
        Route::get('/monitor/late-students-by-batch', [MonitorController::class, 'getLateStudentsByBatch'])->name('monitor.late-students-by-batch');
        Route::get('/monitor/goingout-attendance', [MonitorController::class, 'getGoingOutAttendance'])->name('monitor.goingout-attendance');
        Route::get('/monitor/time-inout-by-batch', [MonitorController::class, 'getTimeInOutByBatch'])->name('monitor.time-inout-by-batch');
        Route::get('/monitor/academic-loginout-data', [MonitorController::class, 'getAcademicLogInOutData'])->name('monitor.academic-loginout-data');
        Route::get('/monitor/goingout-loginout-data', [MonitorController::class, 'getGoingOutLogInOutData'])->name('monitor.goingout-loginout-data');
        Route::get('/monitor/absent-students-by-batch', [MonitorController::class, 'getAbsentStudentsByBatch'])->name('monitor.absent-students-by-batch');

        // Monitor log routes
        Route::get('/monitor/academic/logs', [MonitorController::class, 'academicLogs'])->name('monitor.academic.logs');
        Route::get('/monitor/goingout/logs', [MonitorController::class, 'goingoutLogs'])->name('monitor.goingout.logs');
        Route::get('/monitor/visitor/logs', [MonitorController::class, 'visitorLogs'])->name('monitor.visitor.logs');

        // Monitor consideration routes
        Route::post('/monitor/academic/consideration/{id}', [MonitorController::class, 'updateAcademicConsideration'])->name('monitor.academic.consideration');
        Route::post('/monitor/academic/absent-validation/{id}', [MonitorController::class, 'updateAbsentValidation'])->name('monitor.academic.absent-validation');
        Route::post('/monitor/goingout/consideration/{id}', [MonitorController::class, 'updateGoingoutConsideration'])->name('monitor.goingout.consideration');
        Route::post('/monitor/visitor/consideration/{id}', [MonitorController::class, 'updateVisitorConsideration'])->name('monitor.visitor.consideration');

        // Monitor log in/out routes
        Route::post('/monitor/academic/logout/{id}', [MonitorController::class, 'performAcademicLogout'])->name('monitor.academic.logout');
        Route::post('/monitor/academic/login/{id}', [MonitorController::class, 'performAcademicLogin'])->name('monitor.academic.login');
        Route::post('/monitor/goingout/logout/{id}', [MonitorController::class, 'performGoingoutLogout'])->name('monitor.goingout.logout');
        Route::post('/monitor/goingout/login/{id}', [MonitorController::class, 'performGoingoutLogin'])->name('monitor.goingout.login');

        Route::get('/monitor/schedule/choose/{type}', function ($type) {
            if ($type === 'Irregular') {
                return redirect()->route('monitor.irregular-schedule.select');
            }

            $batches = StudentDetail::distinct()->pluck('batch')->sort();
            $genders = PNUser::distinct()->pluck('gender')->sort();
            $student_ids = StudentDetail::distinct()->pluck('student_id')->sort();
            $students = StudentDetail::all();
            return view('user-monitor.type', compact('type', 'batches', 'genders', 'student_ids', 'students'));
        })->name('monitor.schedule.choose');

        Route::get('/monitor/schedule', [MonitorScheduleController::class, 'show'])
            ->name('monitor.schedule');

        Route::post(
            '/monitor/schedule/store',
            [MonitorScheduleController::class, 'store']
        )
            ->name('monitor.schedule.store');

        Route::post(
            '/monitor/schedule/update-day',
            [MonitorScheduleController::class, 'updateDay']
        )
            ->name('monitor.schedule.update-day');

        Route::delete(
            '/monitor/schedule/delete',
            [MonitorScheduleController::class, 'delete']
        )
            ->name('monitor.schedule.delete');

        Route::patch(
            '/monitor/schedule/update-grace-period',
            [MonitorScheduleController::class, 'updateGracePeriod']
        )
            ->name('monitor.schedule.update-grace-period');

        // Grouped Routes for Irregular Schedules
        Route::prefix('monitor/irregular-schedule')->group(function () {
            Route::get('/select', [MonitorScheduleController::class, 'selectStudent'])
                ->name('monitor.irregular-schedule.select');
            Route::get('/{student_id}', [MonitorScheduleController::class, 'showIrregularSchedule'])
                ->name('monitor.irregular-schedule');
            Route::post('/{student_id}/store', [MonitorScheduleController::class, 'storeIrregularSchedule'])
                ->name('monitor.irregular-schedule.store');
        });

        // Individual Going Out Schedules routes
        Route::prefix('monitor/individual-goingout')->group(function () {
            Route::get('/students', [MonitorScheduleController::class, 'showIndividualGoingOutStudents'])
                ->name('monitor.individual-goingout.students');
            Route::post('/set-schedule', [MonitorScheduleController::class, 'setIndividualGoingOutSchedule'])
                ->name('monitor.individual-goingout.set-schedule');
        });
    });
// });

// Debug route for testing academic schedule logic
Route::get('/debug-academic-schedule', function () {
    $studentId = request('student_id', '2022-00001-TG-0'); // Default student ID
    $student = \App\Models\StudentDetail::where('student_id', $studentId)->first();

    if (!$student) {
        return response()->json(['error' => 'Student not found']);
    }

    $today = \Carbon\Carbon::now()->format('l');

    // Get ALL schedules for this student to see what exists
    $allStudentSchedules = \App\Models\Schedule::where('student_id', $studentId)->get();

    // Get ALL schedules for today to see what's available
    $allTodaySchedules = \App\Models\Schedule::where('day_of_week', $today)->get();

    // Check irregular schedule with detailed query
    $irregularQuery = \App\Models\Schedule::where('student_id', $studentId)
        ->where('day_of_week', $today)
        ->where('schedule_type', 'academic');

    $irregularScheduleAll = $irregularQuery->get(); // Get all matches first

    $irregularSchedule = $irregularQuery->where(function ($query) {
        $query->whereNull('valid_until')
            ->orWhereDate('valid_until', '>=', \Carbon\Carbon::today());
    })
    ->orderBy('updated_at', 'desc')
    ->orderBy('created_at', 'desc')
    ->first();

    // Check batch schedule
    $batchQuery = \App\Models\Schedule::where('batch', $student->batch)
        ->where('pn_group', $student->group)
        ->where('day_of_week', $today)
        ->where('schedule_type', 'academic');

    $batchScheduleAll = $batchQuery->get(); // Get all matches first

    $batchSchedule = $batchQuery->where(function ($query) {
        $query->whereNull('valid_until')
            ->orWhereDate('valid_until', '>=', \Carbon\Carbon::today());
    })
    ->orderBy('updated_at', 'desc')
    ->orderBy('created_at', 'desc')
    ->first();

    $schedule = $irregularSchedule ?: $batchSchedule;
    $scheduleType = $irregularSchedule ? 'irregular' : ($batchSchedule ? 'batch' : 'none');

    $currentTime = \Carbon\Carbon::parse('07:30:00'); // Simulate 7:30 AM logout

    $remark = 'On Time'; // Default
    if ($schedule) {
        $scheduleStartTime = \Carbon\Carbon::parse($schedule->time_out);
        $gracePeriodMinutes = $schedule->grace_period_logout_minutes;

        if ($gracePeriodMinutes !== null) {
            if ($currentTime->lessThan($scheduleStartTime->copy()->subMinutes($gracePeriodMinutes))) {
                $remark = 'Early';
            } elseif ($currentTime->greaterThan($scheduleStartTime)) {
                $remark = 'Late';
            } else {
                $remark = 'On Time';
            }
        } else {
            if ($currentTime->lessThan($scheduleStartTime)) {
                $remark = 'Early';
            } elseif ($currentTime->greaterThan($scheduleStartTime)) {
                $remark = 'Late';
            } else {
                $remark = 'On Time';
            }
        }
    }

    return response()->json([
        'student_info' => [
            'student_id' => $studentId,
            'name' => $student->user->user_fname . ' ' . $student->user->user_lname,
            'batch' => $student->batch,
            'group' => $student->group
        ],
        'search_criteria' => [
            'day' => $today,
            'today_date' => \Carbon\Carbon::today()->format('Y-m-d'),
            'current_time' => $currentTime->format('H:i:s')
        ],
        'all_student_schedules' => $allStudentSchedules->map(function($s) {
            return [
                'schedule_id' => $s->schedule_id,
                'student_id' => $s->student_id,
                'day_of_week' => $s->day_of_week,
                'schedule_type' => $s->schedule_type,
                'time_out' => $s->time_out,
                'time_in' => $s->time_in,
                'valid_until' => $s->valid_until,
                'created_at' => $s->created_at,
                'updated_at' => $s->updated_at
            ];
        }),
        'irregular_schedule_search' => [
            'all_matches' => $irregularScheduleAll->map(function($s) {
                return [
                    'schedule_id' => $s->schedule_id,
                    'valid_until' => $s->valid_until,
                    'is_valid' => is_null($s->valid_until) || $s->valid_until >= \Carbon\Carbon::today()->format('Y-m-d'),
                    'time_out' => $s->time_out,
                    'time_in' => $s->time_in
                ];
            }),
            'final_result' => $irregularSchedule ? [
                'schedule_id' => $irregularSchedule->schedule_id,
                'time_out' => $irregularSchedule->time_out,
                'time_in' => $irregularSchedule->time_in,
                'grace_period_logout' => $irregularSchedule->grace_period_logout_minutes,
                'grace_period_login' => $irregularSchedule->grace_period_login_minutes,
                'valid_until' => $irregularSchedule->valid_until
            ] : null
        ],
        'batch_schedule_search' => [
            'all_matches' => $batchScheduleAll->map(function($s) {
                return [
                    'schedule_id' => $s->schedule_id,
                    'batch' => $s->batch,
                    'pn_group' => $s->pn_group,
                    'valid_until' => $s->valid_until,
                    'is_valid' => is_null($s->valid_until) || $s->valid_until >= \Carbon\Carbon::today()->format('Y-m-d'),
                    'time_out' => $s->time_out,
                    'time_in' => $s->time_in
                ];
            }),
            'final_result' => $batchSchedule ? [
                'schedule_id' => $batchSchedule->schedule_id,
                'time_out' => $batchSchedule->time_out,
                'time_in' => $batchSchedule->time_in,
                'grace_period_logout' => $batchSchedule->grace_period_logout_minutes,
                'grace_period_login' => $batchSchedule->grace_period_login_minutes,
                'valid_until' => $batchSchedule->valid_until
            ] : null
        ],
        'final_decision' => [
            'schedule_used' => $scheduleType,
            'calculated_remark' => $remark,
            'schedule_details' => $schedule ? [
                'schedule_id' => $schedule->schedule_id,
                'schedule_time_out' => $schedule->time_out,
                'grace_period_minutes' => $schedule->grace_period_logout_minutes,
                'grace_window_start' => $schedule->grace_period_logout_minutes ?
                    \Carbon\Carbon::parse($schedule->time_out)->subMinutes($schedule->grace_period_logout_minutes)->format('H:i:s') :
                    null,
                'grace_window_end' => $schedule->time_out,
                'expected_result' => 'Should be Late since 07:30 > 07:23 (if irregular schedule exists)'
            ] : null
        ]
    ]);
});
