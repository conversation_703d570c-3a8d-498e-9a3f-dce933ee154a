<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Schedule extends Model
{
    protected $primaryKey = 'schedule_id';

    protected $fillable = [
        'student_id',
        'gender',
        'batch',
        'pn_group',
        'day_of_week',
        'schedule_type',
        'time_in',
        'time_out',
        'valid_until',
        'grace_period_logout_minutes',
        'grace_period_login_minutes',
        'created_by'
    ];

    protected $casts = [
        'valid_until' => 'date'
    ];

    // Mutators to handle time input - store as time format
    public function setTimeInAttribute($value)
    {
        $this->attributes['time_in'] = $value ? Carbon::parse($value)->format('H:i:s') : null;
    }

    public function setTimeOutAttribute($value)
    {
        $this->attributes['time_out'] = $value ? Carbon::parse($value)->format('H:i:s') : null;
    }

    // Helper methods to get formatted time for display purposes
    public function getFormattedTimeInAttribute()
    {
        return $this->attributes['time_in'] ? Carbon::parse($this->attributes['time_in'])->format('H:i') : null;
    }

    public function getFormattedTimeOutAttribute()
    {
        return $this->attributes['time_out'] ? Carbon::parse($this->attributes['time_out'])->format('H:i') : null;
    }

    // Helper methods to get raw time values for comparisons
    public function getRawTimeIn()
    {
        return $this->attributes['time_in'];
    }

    public function getRawTimeOut()
    {
        return $this->attributes['time_out'];
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(PNUser::class, 'created_by', 'user_id');
    }

    // Scopes for filtering by schedule type
    public function scopeAcademic($query)
    {
        return $query->where('schedule_type', 'academic');
    }

    public function scopeGoingOut($query)
    {
        return $query->where('schedule_type', 'going_out');
    }

    public function scopeForStudent($query, $studentId)
    {
        return $query->where('student_id', $studentId);
    }

    public function scopeForDay($query, $dayOfWeek)
    {
        return $query->where('day_of_week', $dayOfWeek);
    }

    public function scopeActive($query)
    {
        return $query->where(function ($query) {
            $query->whereNull('valid_until')
                ->orWhere('valid_until', '>=', Carbon::today());
        });
    }
}
