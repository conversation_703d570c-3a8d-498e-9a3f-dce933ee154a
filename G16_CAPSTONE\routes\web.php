<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\FeedbackController;
use App\Http\Controllers\TaskHistoryController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\GeneralTaskController;
use App\Http\Controllers\Student16Controller;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\AssignmentController;
use App\Http\Controllers\BatchController;
use App\Http\Controllers\MainStudentDASHController;
use App\Http\Controllers\DashboardRedirectController;

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request;

Route::get('/', [AuthController::class, 'dashboard']);
Route::get('/login', function () {
    return redirect()->to(env('MAIN_SYSTEM_URL'). '/login');
})->name('login');
Route::post('/logout', [AuthController::class, 'logout'])
    ->name('logout');

// Admin routes (educator/inspector only)
Route::middleware(['auth'])->group(function () {
    // Restrict admin dashboard to educator/inspector only
    Route::get('/AdminDashboard', function() {
            return view('dashboard');
    })->name('dashboard');

    Route::get('/Admindashboard', function() {
        $user = auth()->user();
        if (!in_array($user->user_role, ['educator', 'inspector'])) {
            return redirect()->route('mainstudentdash');
        }
        return app(TaskController::class)->dashboard();
    })->name('Admindashboard');
    Route::get('/roomtask/{room?}', [TaskController::class, 'roomtask'])->name('roomtask');
    Route::get('/generalTask', [GeneralTaskController::class, 'index'])->name('generalTask');

    // Submit feedback route
    Route::post('/submit-feedback', [FeedbackController::class, 'submitFeedback'])->name('submit.feedback');
    Route::get('/feedbacks', [FeedbackController::class, 'getRoomFeedbacks']);
    Route::post('/feedback/edit', [FeedbackController::class, 'editFeedback'])->name('feedback.edit');
    Route::post('/feedback/delete', [FeedbackController::class, 'deleteFeedback'])->name('feedback.delete');

    // Task routes
    Route::post('/update-task-status', [TaskController::class, 'updateTaskStatus']);
    Route::post('/mark-day-complete', [TaskController::class, 'markDayComplete']);
    Route::post('/save-task', [TaskController::class, 'saveTask'])->name('save.task');
    Route::get('/get-task-photos', [TaskController::class, 'getTaskPhotos']);
    Route::get('/task-history', [TaskController::class, 'showTaskHistory'])->name('task.history');
    Route::post('/get-task-statuses', [TaskController::class, 'getTaskStatuses']);
   // Route::post('/mark-day-completed', [App\Http\Controllers\TaskController::class, 'markDayAsCompleted'])->name('mark.day.completed');
    Route::post('/api/check-week-completion', [\App\Http\Controllers\TaskController::class, 'apiCheckWeekCompletion']);

    // Report Routes
    Route::prefix('reports')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('reports.index');
        Route::get('/create', [ReportController::class, 'create'])->name('reports.create');
        Route::post('/', [ReportController::class, 'store'])->name('reports.store');
        Route::get('/{report}', [ReportController::class, 'show'])->name('reports.show');
        Route::get('/{report}/edit', [ReportController::class, 'edit'])->name('reports.edit');
        Route::put('/{report}', [ReportController::class, 'update'])->name('reports.update');
        Route::delete('/{report}', [ReportController::class, 'destroy'])->name('reports.destroy');
        Route::get('/{report}/download', [ReportController::class, 'download'])->name('reports.download');
        Route::get('/export/excel', [ReportController::class, 'export'])->name('reports.export');
    });

    // API routes for dashboard student management
    Route::post('/api/room/add-student', [TaskController::class, 'apiAddStudent']);
    Route::post('/api/room/edit-student', [TaskController::class, 'apiEditStudent']);
    Route::post('/api/room/delete-student', [TaskController::class, 'apiDeleteStudent']);


    // General Task Dashboard
    Route::get('/GenTaskDashboard', [GeneralTaskController::class, 'index'])->name('genTaskDashboard');
    Route::get('/task-checklist', [GeneralTaskController::class, 'taskChecklist'])->name('task.checklist');
    Route::post('/task-checklist/update-status', [GeneralTaskController::class, 'updateTaskStatus'])->name('task.updateStatus');
    Route::post('/task-checklist/update-remarks', [GeneralTaskController::class, 'updateTaskRemarks'])->name('task.updateRemarks');
    Route::post('/task-checklist/update-dates', [GeneralTaskController::class, 'updateWeekDates'])->name('task.updateDates');
    Route::post('/task-checklist/save-all-statuses', [GeneralTaskController::class, 'saveAllTaskStatuses'])->name('task.saveAllStatuses');
    Route::post('/task-checklist/save-single-status', [GeneralTaskController::class, 'saveSingleTaskStatus'])->name('task.saveSingleStatus');
    Route::get('/task-checklist/get-statuses', [GeneralTaskController::class, 'getTaskStatusesForDate'])->name('task.getStatuses');

    // Dynamic Capstone Tasking System routes
    Route::post('/generalTask/set-student-counts', [GeneralTaskController::class, 'setStudentCounts'])->name('generalTask.setStudentCounts');
    Route::get('/generalTask/dynamic-tasks-matrix', [GeneralTaskController::class, 'getDynamicTasksMatrix'])->name('generalTask.dynamicTasksMatrix');
    Route::post('/generalTask/assign-students-to-task', [GeneralTaskController::class, 'assignStudentsToTask'])->name('generalTask.assignStudentsToTask');

    // Students
    Route::get('/students', [Student16Controller::class, 'index'])->name('students.index');
    Route::get('/students/create', [Student16Controller::class, 'create'])->name('students.create');
    Route::post('/students', [Student16Controller::class, 'store'])->name('students.store');
    Route::get('/student/{id}', [Student16Controller::class, 'show'])->name('student.show');
    Route::delete('/students/remove/{name}', [Student16Controller::class, 'removeStudent'])->name('students.remove');

    // Student Management (for View Members modal)
    Route::put('/students/{id}/update-name', [Student16Controller::class, 'updateName'])->name('students.updateName');
    Route::delete('/students/{id}', [Student16Controller::class, 'destroy'])->name('students.destroy');
    Route::post('/students/quick-add', [Student16Controller::class, 'quickAdd'])->name('students.quickAdd');
    Route::post('/students/quick-add-to-category', [Student16Controller::class, 'quickAddToCategory'])->name('students.quickAddToCategory');
    Route::get('/students/all-for-deletion', [Student16Controller::class, 'getAllForDeletion'])->name('students.getAllForDeletion');
    Route::post('/students/delete-multiple', [Student16Controller::class, 'deleteMultiple'])->name('students.deleteMultiple');

    // Batch Management
    Route::get('/batches', [BatchController::class, 'index'])->name('batches.index');
    Route::post('/batches', [BatchController::class, 'store'])->name('batches.store');
    Route::put('/batches/{batch}', [BatchController::class, 'update'])->name('batches.update');
    Route::delete('/batches/{batch}', [BatchController::class, 'destroy'])->name('batches.destroy');
    Route::get('/api/batches/active', [BatchController::class, 'getActiveBatches'])->name('batches.active');

    // Categories
    Route::get('/categories', [CategoryController::class, 'index'])->name('categories.index');
    Route::post('/categories', [CategoryController::class, 'store'])->name('categories.store');
    Route::delete('/categories/{category}', [CategoryController::class, 'destroy'])->name('categories.destroy');

    // Assignments
    Route::get('/assignments', [AssignmentController::class, 'index'])->name('assignments.index');
    Route::get('/assignments/create', [AssignmentController::class, 'create'])->name('assignments.create');
    Route::post('/assignments', [AssignmentController::class, 'store'])->name('assignments.store');
    Route::post('/assignments/auto-shuffle', [AssignmentController::class, 'autoShuffle'])->name('assignments.autoShuffle');

    // Debug route to test database connection
    Route::get('/test-db', function() {
        try {
            $students = \App\Models\StudentGen::count();
            $categories = \App\Models\Category::count();
            $assignments = \App\Models\Assignment::count();
            return response()->json([
                'status' => 'success',
                'students' => $students,
                'categories' => $categories,
                'assignments' => $assignments,
                'db_connection' => config('database.default')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => $e->getMessage(),
                'db_connection' => config('database.default')
            ]);
        }
    });
    Route::post('/assignments/cleanup-duplicates', [AssignmentController::class, 'cleanupDuplicates'])->name('assignments.cleanupDuplicates');
    Route::get('/assignments/category/{categoryId}/members', [AssignmentController::class, 'getCategoryMembers'])->name('assignments.getCategoryMembers');
    Route::post('/assignments/update-member-comment', [AssignmentController::class, 'updateMemberComment'])->name('assignments.updateMemberComment');
    Route::get('/assignments/category/{categoryId}/available-students', [AssignmentController::class, 'getAvailableStudents'])->name('assignments.getAvailableStudents');
    Route::get('/assignments/category/{categoryId}/current-members', [AssignmentController::class, 'getCurrentMembers'])->name('assignments.getCurrentMembers');
    Route::post('/assignments/category/{categoryId}/add-members', [AssignmentController::class, 'addMembers'])->name('assignments.addMembers');
        Route::post('/assignments/category/{categoryId}/remove-members', [AssignmentController::class, 'removeMembers'])->name('assignments.removeMembers');

        // Admin access to Student Dashboard views
        // Route::get('/StudentDashboard', [MainStudentDASHController::class, 'index'])->name('admin.student.dashboard');
        Route::get('/StudentGenTaskDashboard', [MainStudentDASHController::class, 'generalTask'])->name('admin.student.general.task');
});

// Student routes (student only)
Route::middleware(['auth'])->group(function () {
    // Restrict student dashboard to students only
    Route::get('/StudentDashboard', function(Request $request) {
            return view('StudentsDashboard.MainStudentDASH');
    })->name('mainstudentdash');

    Route::get('/StudentDashboard.student-dashboard', function() {
        $user = auth()->user();
        if ($user->user_role !== 'student') {
            return redirect()->route('mainstudentdash');
        }
        return app(MainStudentDASHController::class)->index(request());
    })->name('StudentSDashboard.student.dashboard');

    Route::get('/StudentGenTaskDashboard', function() {
        $user = auth()->user();
        if ($user->user_role !== 'student') {
            return redirect()->route('mainstudentdash');
        }
        return app(MainStudentDASHController::class)->generalTask();
    })->name('student.general.task');
    Route::get('/StudentsDashboard.dining-form', function() {
        return view('StudentsDashboard.dining-form');
    })->name('dining.form');
    Route::get('/StudentsDashboard.groundfloor-form', function() {
        return view('StudentsDashboard.groundfloor-form');
    })->name('groundfloor.form');
    Route::get('/StudentsDashboard.offices-form', function() {
        return view('StudentsDashboard.offices-form');
    })->name('offices.form');
    Route::get('/StudentsDashboard.garbage-form', function() {
        return view('StudentsDashboard.garbage-form');
    })->name('garbage.form');
    Route::get('/student-room-task-history', [MainStudentDASHController::class, 'roomTaskHistory'])->name('student.room.task.history');
    Route::get('/StudentsDashboard.task-history', [MainStudentDASHController::class, 'taskHistory'])->name('StudentsDashboard.task.history');
    Route::get('/StudentsDashboard.reports', function() {
        return view('StudentsDashboard.reports');
    })->name('student.reports');
});

// Shared routes for both admin and student
Route::middleware(['auth'])->group(function () {
    Route::get('/homepage', function () {
        // Always show the landing page (homepage) after login, regardless of role
        return view('homepage');
    })->name('homepage');

    // Route for Tasking Hub card - redirects based on user role
    Route::get('/tasking-hub', function () {
        $user = auth()->user();
        if (in_array($user->user_role, ['educator', 'inspector'])) {
            return redirect()->route('dashboard'); // AdminDashboard
        } elseif ($user->user_role === 'student') {
            return redirect()->route('mainstudentdash'); // StudentDashboard
        }
        // Fallback to login if role is not recognized
        return redirect()->route('auth.login');
    })->name('tasking.hub');
});
