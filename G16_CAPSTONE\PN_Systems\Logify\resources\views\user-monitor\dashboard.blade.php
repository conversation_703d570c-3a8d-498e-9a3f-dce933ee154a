<x-monitorLayout>
    {{-- Welcome Message --}}
    <div class="mb-10 text-center ">
        <p class="text-3xl font-bold text-blue-800 ">
            Welcome, Class Monitor!
            <br class="hidden sm:block" />
            <span class="block mt-2 text-sm font-medium text-gray-600">Ready to set today's schedule for the
                classes?</span>
            <span class="block w-24 h-1 mx-auto mt-3 bg-blue-500 rounded-full"></span>
        </p>
    </div>

    {{-- Quick Stats Container --}}
    <div class="p-8 mb-4 bg-white shadow-md rounded-2xl">
        <div class="relative perspective-1000">
            {{-- Flip Container --}}
            <div id="overviewFlipContainer" class="relative w-full transition-transform duration-500 transform-style-3d">
                {{-- Academic Overview (Front) --}}
                <div id="academicOverview" class="w-full backface-hidden">
                    <h3 class="mb-8 text-lg font-semibold text-gray-800">
                        <span id="overviewTitle">Today's Academic Overview</span>
                        <span class="ml-2 text-sm font-medium text-gray-600">(Class & Group based Schedule)</span>
                    </h3>
                    <div class="grid grid-cols-5 gap-3">
                        <!-- Total Students Card (spans 2 rows, keeps blue background) -->
                        <div class="row-span-2 relative p-6 bg-gradient-to-br from-blue-500 to-blue-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 200px;">
                            <div class="flex items-center h-full">
                                <div class="flex-shrink-0 mr-4">
                                    <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                        <i data-feather="users" class="w-8 h-8 text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <p class="mb-2 text-sm font-medium text-blue-100 opacity-90">Total Students</p>
                                    <p class="text-3xl font-bold text-white" id="totalStudentsOverview">0</p>
                                </div>
                            </div>
                        </div>

                        <!-- Row 1: Log Out and its breakdown -->
                        <!-- Log Out Card -->
                        <div class="relative p-4 bg-gradient-to-br from-orange-500 to-orange-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                            <div class="flex items-center h-full">
                                <div class="flex-shrink-0 mr-3">
                                    <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                        <i data-feather="log-out" class="w-6 h-6 text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <p class="mb-1 font-medium text-orange-100 text-xs">Log Out</p>
                                    <p class="text-2xl font-bold text-white" id="academicLogOutTotal">0</p>
                                </div>
                            </div>
                        </div>

                        <!-- Log Out Early Card -->
                        <div class="relative p-4 bg-gradient-to-br from-green-500 to-green-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                            <div class="flex items-center h-full">
                                <div class="flex-shrink-0 mr-3">
                                    <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                        <i data-feather="sunrise" class="w-6 h-6 text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <p class="mb-1 font-medium text-green-100 text-xs">Early</p>
                                    <p class="text-2xl font-bold text-white" id="academicLogOutEarly">0</p>
                                </div>
                            </div>
                        </div>

                        <!-- Log Out On Time Card -->
                        <div class="relative p-4 bg-gradient-to-br from-blue-500 to-blue-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                            <div class="flex items-center h-full">
                                <div class="flex-shrink-0 mr-3">
                                    <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                        <i data-feather="clock" class="w-6 h-6 text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <p class="mb-1 font-medium text-blue-100 text-xs">On Time</p>
                                    <p class="text-2xl font-bold text-white" id="academicLogOutOnTime">0</p>
                                </div>
                            </div>
                        </div>

                        <!-- Log Out Late Card -->
                        <div class="relative p-4 bg-gradient-to-br from-red-500 to-red-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                            <div class="flex items-center h-full">
                                <div class="flex-shrink-0 mr-3">
                                    <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                        <i data-feather="alert-circle" class="w-6 h-6 text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <p class="mb-1 font-medium text-red-100 text-xs">Late</p>
                                    <p class="text-2xl font-bold text-white" id="academicLogOutLate">0</p>
                                </div>
                            </div>
                        </div>

                        <!-- Row 2: Log In and its breakdown -->
                        <!-- Log In Card -->
                        <div class="relative p-4 bg-gradient-to-br from-indigo-500 to-indigo-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                            <div class="flex items-center h-full">
                                <div class="flex-shrink-0 mr-3">
                                    <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                        <i data-feather="log-in" class="w-6 h-6 text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <p class="mb-1 font-medium text-indigo-100 text-xs">Log In</p>
                                    <p class="text-2xl font-bold text-white" id="academicLogInTotal">0</p>
                                </div>
                            </div>
                        </div>

                        <!-- Log In Early Card -->
                        <div class="relative p-4 bg-gradient-to-br from-green-500 to-green-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                            <div class="flex items-center h-full">
                                <div class="flex-shrink-0 mr-3">
                                    <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                        <i data-feather="sunrise" class="w-6 h-6 text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <p class="mb-1 font-medium text-green-100 text-xs">Early</p>
                                    <p class="text-2xl font-bold text-white" id="academicLogInEarly">0</p>
                                </div>
                            </div>
                        </div>

                        <!-- Log In On Time Card -->
                        <div class="relative p-4 bg-gradient-to-br from-blue-500 to-blue-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                            <div class="flex items-center h-full">
                                <div class="flex-shrink-0 mr-3">
                                    <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                        <i data-feather="clock" class="w-6 h-6 text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <p class="mb-1 font-medium text-blue-100 text-xs">On Time</p>
                                    <p class="text-2xl font-bold text-white" id="academicLogInOnTime">0</p>
                                </div>
                            </div>
                        </div>

                        <!-- Log In Late Card -->
                        <div class="relative p-4 bg-gradient-to-br from-red-500 to-red-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                            <div class="flex items-center h-full">
                                <div class="flex-shrink-0 mr-3">
                                    <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                        <i data-feather="alert-circle" class="w-6 h-6 text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <p class="mb-1 font-medium text-red-100 text-xs">Late</p>
                                    <p class="text-2xl font-bold text-white" id="academicLogInLate">0</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button onclick="flipOverview()" class="absolute text-blue-600 transition-colors top-4 right-4 hover:text-blue-800 flip-button group">
                                <i data-feather="repeat" class="w-5 h-5"></i>
                                <div class="absolute right-0 z-50 invisible px-2 py-1 text-xs text-white bg-gray-900 rounded-md shadow-sm group-hover:visible -top-8 whitespace-nowrap">
                                    Switch Overview Type
                                </div>
                </div>

                {{-- Going Out Overview (Back) --}}
                <div id="goingOutOverview" class="absolute top-0 left-0 w-full backface-hidden rotate-y-180">
                    <h3 class="mb-8 text-lg font-semibold text-gray-800">
                        <span id="goingOutTitle">Today's Going Out Overview</span>
                        <span class="ml-2 text-sm font-medium text-gray-600">(Gender based Schedule)</span>
                    </h3>
                    <div class="grid grid-cols-5 gap-3">
                        <!-- Total Students Card (spans 2 rows, keeps blue background) -->
                        <div class="row-span-2 relative p-6 bg-gradient-to-br from-blue-500 to-blue-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 200px;">
                            <div class="flex items-center h-full">
                                <div class="flex-shrink-0 mr-4">
                                    <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                        <i data-feather="users" class="w-8 h-8 text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <p class="mb-2 text-sm font-medium text-blue-100 opacity-90">Total Students</p>
                                    <p class="text-3xl font-bold text-white" id="goingOutTotalStudents">0</p>
                                </div>
                            </div>
                        </div>

                        <!-- Row 1: Log Out and On Time -->
                        <!-- Log Out Card -->
                        <div class="relative col-span-2 p-4 bg-gradient-to-br from-orange-500 to-orange-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                            <div class="flex items-center h-full">
                                <div class="flex-shrink-0 mr-3">
                                    <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                        <i data-feather="log-out" class="w-6 h-6 text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <p class="mb-1 font-medium text-orange-100 text-xs">Log Out</p>
                                    <p class="text-2xl font-bold text-white" id="goingOutLogOutTotal">0</p>
                                </div>
                            </div>
                        </div>

                        <!-- Log Out On Time Card -->
                        <div class="relative col-span-2 p-4 bg-gradient-to-br from-green-500 to-green-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                            <div class="flex items-center h-full">
                                <div class="flex-shrink-0 mr-3">
                                    <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                        <i data-feather="clock" class="w-6 h-6 text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <p class="mb-1 font-medium text-green-100 text-xs">On Time</p>
                                    <p class="text-2xl font-bold text-white" id="goingOutLogOutOnTime">0</p>
                                </div>
                            </div>
                        </div>

                        <!-- Row 2: Log In and its breakdown (equal width cards filling remaining space) -->
                        <!-- Log In Card -->
                        <div class="grid grid-cols-3 col-span-4 gap-3">
                            <div class="relative p-4 bg-gradient-to-br from-indigo-500 to-indigo-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                                <div class="flex items-center h-full">
                                    <div class="flex-shrink-0 mr-3">
                                        <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                            <i data-feather="log-in" class="w-6 h-6 text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-1">
                                        <p class="mb-1 font-medium text-indigo-100 text-xs">Log In</p>
                                        <p class="text-2xl font-bold text-white" id="goingOutLogInTotal">0</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Log In On Time Card -->
                            <div class="relative p-4 bg-gradient-to-br from-green-500 to-green-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                                <div class="flex items-center h-full">
                                    <div class="flex-shrink-0 mr-3">
                                        <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                            <i data-feather="clock" class="w-6 h-6 text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-1">
                                        <p class="mb-1 font-medium text-green-100 text-xs">On Time</p>
                                        <p class="text-2xl font-bold text-white" id="goingOutLogInOnTime">0</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Log In Late Card -->
                            <div class="relative p-4 bg-gradient-to-br from-red-500 to-red-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                                <div class="flex items-center h-full">
                                    <div class="flex-shrink-0 mr-3">
                                        <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                            <i data-feather="alert-circle" class="w-6 h-6 text-white"></i>
                                        </div>
                                    </div>
                                    <div class="flex-1">
                                        <p class="mb-1 font-medium text-red-100 text-xs">Late</p>
                                        <p class="text-2xl font-bold text-white" id="goingOutLogInLate">0</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button onclick="flipOverview()" class="absolute text-blue-600 transition-colors top-4 right-4 hover:text-blue-800 flip-button group">
                                <i data-feather="repeat" class="w-5 h-5"></i>
                                <div class="absolute right-0 z-50 invisible px-2 py-1 text-xs text-white bg-gray-900 rounded-md shadow-sm group-hover:visible -top-8 whitespace-nowrap">
                                    Switch Overview Type
                                </div>
                </div>
            </div>
        </div>
    </div>

    {{-- Academic Chart Filter (Only visible when on Academic side) --}}
    <div id="academicChartFilter" class="mb-6 p-4 bg-white shadow-md rounded-xl">
        <div class="flex items-center justify-between">
            <h3 class="text-sm font-medium text-gray-600">Academic Analytics View</h3>
            <div class="flex items-center space-x-3">
                <label class="text-xs font-medium text-gray-700">Show:</label>
                <select id="academicChartSelector" class="px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                    <option value="timeinout">Time In & Time Out</option>
                    <option value="absent">Absent Students</option>
                </select>
            </div>
        </div>
    </div>

    {{-- Combined Analytics Container - Dynamic layout based on flip state --}}
    <div id="analyticsContainer" class="grid grid-cols-2 gap-6 mb-8 transition-all duration-500">
        {{-- Time In/Out Analytics Container --}}
        <div id="timeInOutContainer" class="p-6 bg-white shadow-md rounded-2xl">
            <div class="relative perspective-1000">
                {{-- Flip Container --}}
                <div id="timeInOutFlipContainer" class="relative w-full transition-transform duration-500 transform-style-3d">
                    {{-- Academic Time In/Out Overview (Front) --}}
                    <div id="academicTimeInOut" class="w-full backface-hidden">

                        <div class="h-80">
                            <canvas id="academicTimeInOutChart"></canvas>
                        </div>
                    </div>

                    {{-- Going Out Time In/Out Overview (Back) --}}
                    <div id="goingOutTimeInOut" class="absolute top-0 left-0 w-full backface-hidden rotate-y-180">
                        <div class="h-80">
                            <canvas id="goingOutTimeInOutChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Absent Students Analytics Container (Academic Only) --}}
        <div id="absentStudentsContainer" class="p-6 bg-white shadow-md rounded-2xl">
            <h5 class="mb-4 text-sm font-medium text-gray-600 text-center">Absent Students by Batch (Monthly Overview)</h5>
            <div class="h-80 overflow-x-auto">
                <div style="width: 1200px; height: 100%;">
                    <canvas id="academicAbsentChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    {{-- Feather Icons + DateTime Script --}}
    <script src="https://cdn.jsdelivr.net/npm/feather-icons/dist/feather.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/patternomaly@1.3.2/dist/patternomaly.min.js"></script>
    <style data-timestamp="{{ now()->timestamp }}">
        .perspective-1000 {
            perspective: 1000px;
        }
        .transform-style-3d {
            transform-style: preserve-3d;
        }
        .backface-hidden {
            backface-visibility: hidden;
        }
        .rotate-y-180 {
            transform: rotateY(180deg);
        }

        /* Custom scrollbar for absent chart */
        .overflow-x-auto::-webkit-scrollbar {
            height: 8px;
        }
        .overflow-x-auto::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }
        .overflow-x-auto::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }
        .overflow-x-auto::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        .flip-button {
            transition: all 0.3s ease;
        }
        .flip-button:hover {
            transform: scale(1.05);
        }
        .flip-button:active {
            transform: scale(0.95);
        }

        /* Small light gray scrollbars for all elements */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 3px;
            transition: background 0.2s ease;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }

        ::-webkit-scrollbar-corner {
            background: #f8f9fa;
        }

        /* Firefox scrollbar styling */
        * {
            scrollbar-width: thin;
            scrollbar-color: #d1d5db #f8f9fa;
        }

        /* Smooth scrolling behavior */
        html {
            scroll-behavior: smooth;
        }


    </style>
    <script>
        let isFlipped = false;
        let academicTimeInOutChart = null;
        let goingOutTimeInOutChart = null;
        let academicAbsentChart = null;

        // Function to create pattern colors
        function createPatterns(data, color) {
            return data.map(() => pattern.draw('diagonal', color));
        }

        function createDots(data, color) {
            return data.map(() => pattern.draw('dot', color));
        }

        function flipOverview() {
            const container = document.getElementById('overviewFlipContainer');
            const timeInOutContainer = document.getElementById('timeInOutFlipContainer');
            const frontButton = document.querySelector('#academicOverview .flip-button');
            const backButton = document.querySelector('#goingOutOverview .flip-button');
            const analyticsContainer = document.getElementById('analyticsContainer');
            const timeInOutMainContainer = document.getElementById('timeInOutContainer');
            const absentContainer = document.getElementById('absentStudentsContainer');
            const academicFilter = document.getElementById('academicChartFilter');

            isFlipped = !isFlipped;
            container.style.transform = isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)';
            timeInOutContainer.style.transform = isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)';

            // Change layout and filter visibility based on flip state
            if (isFlipped) {
                // Going Out side - hide filter and make chart container full width
                academicFilter.style.display = 'none';
                analyticsContainer.className = 'grid grid-cols-1 gap-6 mb-8 transition-all duration-500';
                timeInOutMainContainer.className = 'p-6 bg-white shadow-md rounded-2xl col-span-1';
                timeInOutMainContainer.style.display = 'block';
                absentContainer.style.display = 'none';
            } else {
                // Academic side - show filter and apply current filter setting
                academicFilter.style.display = 'block';
                // Reset container classes
                timeInOutMainContainer.className = 'p-6 bg-white shadow-md rounded-2xl';
                absentContainer.className = 'p-6 bg-white shadow-md rounded-2xl';
                // Apply current filter
                handleAcademicChartFilter();
            }

            // Update button icons with tooltips
            const buttonContent = `
                <i data-feather="repeat" class="w-5 h-5"></i>
                <div class="absolute right-0 z-50 invisible px-2 py-1 text-xs text-white bg-gray-900 rounded-md shadow-sm group-hover:visible -top-8 whitespace-nowrap">
                    Switch Overview Type
                </div>
            `;

            frontButton.innerHTML = buttonContent;
            backButton.innerHTML = buttonContent;

            // Update data based on flip state
            if (isFlipped) {
                updateGoingOutOverview();
                updateGoingOutTimeInOutChart();
            } else {
                updateTodayOverview();
                updateAcademicTimeInOutChart();
            }

            // Refresh Feather icons
            feather.replace();
        }

        // Function to update Today's Overview
        function updateTodayOverview() {
            // Check if it's Sunday
            const isSunday = new Date().getDay() === 0;
            const overviewTitle = document.getElementById('overviewTitle');

            // Always show "Today's Academic Overview" for the academic side (front)
            overviewTitle.textContent = "Today's Academic Overview";

            // If it's Sunday, show a message
            if (isSunday) {
                document.getElementById('academicOverview').querySelector('.grid').innerHTML = `
                    <div class="relative col-span-5 p-12 overflow-hidden text-center bg-gray-100 rounded-xl" style="min-height: 320px;">
                        <div class="flex flex-col items-center justify-center h-full">
                            <i data-feather="calendar" class="w-16 h-16 mb-4 text-orange-500"></i>
                            <p class="mb-2 text-2xl font-semibold text-gray-700">No Academic Schedule</p>
                            <p class="text-lg font-medium text-gray-600">on Sundays</p>
                        </div>
                        <div class="absolute bottom-0 left-0 w-full h-2 bg-orange-500 rounded-b-xl"></div>
                    </div>
                `;
                feather.replace(); // Re-initialize feather icons
                return;
            }

            // Fetch academic log in/out data
            fetch('{{ route('monitor.academic-loginout-data') }}')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error:', data.message);
                        return;
                    }

                    // Update Log In card with breakdown
                    document.getElementById('academicLogInTotal').textContent = data.logIn?.total || 0;
                    document.getElementById('academicLogInEarly').textContent = data.logIn?.early || 0;
                    document.getElementById('academicLogInOnTime').textContent = data.logIn?.onTime || 0;
                    document.getElementById('academicLogInLate').textContent = data.logIn?.late || 0;

                    // Update Log Out card with breakdown
                    document.getElementById('academicLogOutTotal').textContent = data.logOut?.total || 0;
                    document.getElementById('academicLogOutEarly').textContent = data.logOut?.early || 0;
                    document.getElementById('academicLogOutOnTime').textContent = data.logOut?.onTime || 0;
                    document.getElementById('academicLogOutLate').textContent = data.logOut?.late || 0;
                })
                .catch(error => {
                    console.error('Error:', error);
                    // Set all values to error state
                    document.getElementById('academicLogInTotal').textContent = '—';
                    document.getElementById('academicLogInEarly').textContent = '—';
                    document.getElementById('academicLogInOnTime').textContent = '—';
                    document.getElementById('academicLogInLate').textContent = '—';
                    document.getElementById('academicLogOutTotal').textContent = '—';
                    document.getElementById('academicLogOutEarly').textContent = '—';
                    document.getElementById('academicLogOutOnTime').textContent = '—';
                    document.getElementById('academicLogOutLate').textContent = '—';
                });

            // Fetch total students data
            fetch('{{ route('monitor.student-data') }}')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error:', data.message);
                        return;
                    }

                    if (!data.length && !data.data) {
                        return;
                    }

                    const studentData = data.data || data;
                    const studentCounts = studentData.map(item => item.total_students);
                    const totalStudents = studentCounts.reduce((sum, count) => sum + count, 0);
                    document.getElementById('totalStudentsOverview').textContent = totalStudents;
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('totalStudentsOverview').textContent = '—';
                });
        }

        // Function to update Going Out Overview
        function updateGoingOutOverview() {
            // Going Out operates on all days including Sundays - no special handling needed

            // Fetch going out log in/out data
            fetch('{{ route('monitor.goingout-loginout-data') }}')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error:', data.message);
                        return;
                    }

                    // Update Log In card with breakdown (total, on time, and late)
                    document.getElementById('goingOutLogInTotal').textContent = data.logIn?.total || 0;
                    document.getElementById('goingOutLogInOnTime').textContent = data.logIn?.onTime || 0;
                    document.getElementById('goingOutLogInLate').textContent = data.logIn?.late || 0;

                    // Update Log Out card (total and on time)
                    document.getElementById('goingOutLogOutTotal').textContent = data.logOut?.total || 0;
                    document.getElementById('goingOutLogOutOnTime').textContent = data.logOut?.onTime || 0;
                })
                .catch(error => {
                    console.error('Error:', error);
                    // Set all values to error state (for existing cards)
                    document.getElementById('goingOutLogInTotal').textContent = '—';
                    document.getElementById('goingOutLogInOnTime').textContent = '—';
                    document.getElementById('goingOutLogInLate').textContent = '—';
                    document.getElementById('goingOutLogOutTotal').textContent = '—';
                    document.getElementById('goingOutLogOutOnTime').textContent = '—';
                });

            // Fetch total students data
            fetch('{{ route('monitor.student-data') }}')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error:', data.message);
                        return;
                    }

                    if (!data.length && !data.data) {
                        return;
                    }

                    const studentData = data.data || data;
                    const studentCounts = studentData.map(item => item.total_students);
                    const totalStudents = studentCounts.reduce((sum, count) => sum + count, 0);
                    document.getElementById('goingOutTotalStudents').textContent = totalStudents;
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('goingOutTotalStudents').textContent = '—';
                });
        }

        // Function to update Academic Time In/Out Chart
        function updateAcademicTimeInOutChart() {
            const chartContainer = document.getElementById('academicTimeInOut').querySelector('.h-80');
            const canvas = document.getElementById('academicTimeInOutChart');

            fetch('{{ route('monitor.time-inout-by-batch') }}?type=academic')
                .then(response => response.json())
                .then(timeData => {
                    if (timeData.error) {
                        throw new Error(timeData.message || 'Error fetching data');
                    }

                    if (academicTimeInOutChart instanceof Chart) {
                        academicTimeInOutChart.destroy();
                    }

                    if (!timeData || timeData.length === 0) {
                        chartContainer.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">No academic time in/out data available for today</div>';
                        return;
                    }

                    const ctx = canvas.getContext('2d');
                    academicTimeInOutChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: timeData.map(item => `Class ${item.batch}`),
                            datasets: [
                                {
                                    label: 'Log Out Time',
                                    data: timeData.map(item => item.time_out_count),
                                    backgroundColor: ' rgb(255, 255, 255)', // Solid Orange
                                    borderColor: 'rgb(249, 115, 22)',
                                    borderWidth: 2,
                                    borderRadius: 3,
                                    barThickness: 35
                                },
                                {
                                    label: 'Log In Time',
                                    data: timeData.map(item => item.time_in_count),
                                    backgroundColor:  'rgb(255, 255, 255)', // Striped Blue
                                    borderColor: 'rgb(59, 130, 246)',
                                    borderWidth: 2,
                                    borderRadius: 3,
                                    barThickness: 35
                                },
                                {
                                    label: 'Late Students',
                                    data: timeData.map(item => item.late_count),
                                    backgroundColor: 'rgb(255, 255, 255)', // Dotted Red
                                    borderColor: 'rgb(239, 68, 68)',
                                    borderWidth: 2,
                                    borderRadius: 3,
                                    barThickness: 35
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            interaction: {
                                mode: 'index',
                                intersect: false
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        stepSize: 1,
                                        font: {
                                            size: 10,
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#6B7280'
                                    },
                                    grid: {
                                        color: 'rgba(0, 0, 0, 0.05)',
                                        drawBorder: false
                                    },
                                    title: {
                                        display: true,
                                        text: 'Number of Students',
                                        font: {
                                            size: 10,
                                            weight: '600',
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#374151',
                                        padding: { top: 8, bottom: 8 }
                                    }
                                },
                                x: {
                                    ticks: {
                                        font: {
                                            size: 10,
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#6B7280'
                                    },
                                    grid: {
                                        display: false
                                    },
                                    title: {
                                        display: true,
                                        text: 'Class',
                                        font: {
                                            size: 10,
                                            weight: '600',
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#374151',
                                        padding: { top: 8, bottom: 8 }
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    position: 'top',
                                    align: 'end',
                                    labels: {
                                        font: {
                                            size: 10,
                                            weight: '500',
                                            family: "'Inter', sans-serif"
                                        },
                                        padding: 15,
                                        usePointStyle: true,
                                        pointStyle: 'circle',
                                        color: '#374151'
                                    }
                                },
                                title: {
                                    display: true,
                                    text: 'Academic Log In/Out Time and Late Students Distribution by Batch',
                                    font: {
                                        size: 12,
                                        weight: '600',
                                        family: "'Inter', sans-serif"
                                    },
                                    color: '#111827',
                                    padding: {
                                        top: 8,
                                        bottom: 15
                                    }
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(17, 24, 39, 0.9)',
                                    titleFont: {
                                        size: 13,
                                        weight: '600',
                                        family: "'Inter', sans-serif"
                                    },
                                    bodyFont: {
                                        size: 12,
                                        family: "'Inter', sans-serif"
                                    },
                                    padding: 12,
                                    cornerRadius: 8,
                                    callbacks: {
                                        label: function(context) {
                                            return `${context.dataset.label}: ${context.raw} students`;
                                        }
                                    }
                                }
                            },
                            animation: {
                                duration: 750,
                                easing: 'easeInOutQuart'
                            }
                        }
                    });
                })
                .catch(error => {
                    console.error('Error:', error);
                    chartContainer.innerHTML = `
                        <div class="flex flex-col items-center justify-center h-full">
                            <p class="mb-2 font-semibold text-red-500">Error loading academic time in/out data</p>
                            <p class="text-sm text-gray-500">${error.message}</p>
                        </div>
                    `;
                });
        }

        // Function to update Academic Absent Students Chart
        function updateAcademicAbsentChart() {
            const chartContainer = document.getElementById('academicAbsentChart').parentElement;
            const canvas = document.getElementById('academicAbsentChart');

            fetch('{{ route('monitor.absent-students-by-batch') }}')
                .then(response => response.json())
                .then(monthlyData => {
                    if (monthlyData.error) {
                        throw new Error(monthlyData.message || 'Error fetching data');
                    }

                    if (academicAbsentChart instanceof Chart) {
                        academicAbsentChart.destroy();
                    }

                    if (!monthlyData || monthlyData.length === 0) {
                        chartContainer.innerHTML = `
                            <h5 class="mb-4 text-sm font-medium text-gray-600 text-center">Absent Students by Batch (Monthly Overview)</h5>
                            <div class="h-80 overflow-x-auto">
                                <div style="width: 1200px; height: 100%; display: flex; align-items: center; justify-content: center;">
                                    <div class="text-center text-gray-500">
                                        <i data-feather="calendar" class="w-10 h-10 mx-auto mb-3 text-orange-500"></i>
                                        <p class="text-sm">No data available for this year</p>
                                    </div>
                                </div>
                            </div>
                        `;
                        feather.replace();
                        return;
                    }

                    // Restore the canvas if it was replaced
                    if (!document.getElementById('academicAbsentChart')) {
                        chartContainer.innerHTML = `
                            <h5 class="mb-4 text-sm font-medium text-gray-600 text-center">Absent Students by Batch (Monthly Overview)</h5>
                            <div class="h-80 overflow-x-auto">
                                <div style="width: 1200px; height: 100%;">
                                    <canvas id="academicAbsentChart"></canvas>
                                </div>
                            </div>
                        `;
                    }

                    // Prepare data for the chart
                    const monthLabels = monthlyData.map(month => month.month_name);

                    // Get all unique batches
                    const allBatches = [...new Set(monthlyData.flatMap(month =>
                        month.batches.map(batch => batch.batch)
                    ))].sort();

                    // Create datasets for each batch
                    const datasets = allBatches.map((batch, index) => {
                        const colors = [
                            'rgba(220, 38, 38, 0.8)',   // Red
                            'rgba(249, 115, 22, 0.8)',  // Orange
                            'rgba(59, 130, 246, 0.8)',  // Blue
                            'rgba(16, 185, 129, 0.8)',  // Green
                            'rgba(139, 92, 246, 0.8)',  // Purple
                            'rgba(236, 72, 153, 0.8)',  // Pink
                            'rgba(245, 158, 11, 0.8)',  // Yellow
                            'rgba(107, 114, 128, 0.8)'  // Gray
                        ];

                        const borderColors = [
                            'rgb(220, 38, 38)',   // Red
                            'rgb(249, 115, 22)',  // Orange
                            'rgb(59, 130, 246)',  // Blue
                            'rgb(16, 185, 129)',  // Green
                            'rgb(139, 92, 246)',  // Purple
                            'rgb(236, 72, 153)',  // Pink
                            'rgb(245, 158, 11)',  // Yellow
                            'rgb(107, 114, 128)'  // Gray
                        ];

                        const data = monthlyData.map(month => {
                            const batchData = month.batches.find(b => b.batch === batch);
                            return batchData ? batchData.absent_count : 0;
                        });

                        return {
                            label: `Class ${batch}`,
                            data: data,
                            backgroundColor: colors[index % colors.length],
                            borderColor: borderColors[index % borderColors.length],
                            borderWidth: 1,
                            borderRadius: 3,
                            barThickness: 25
                        };
                    });

                    const ctx = document.getElementById('academicAbsentChart').getContext('2d');
                    academicAbsentChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: monthLabels,
                            datasets: datasets
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            layout: {
                                padding: {
                                    right: 20
                                }
                            },
                            interaction: {
                                mode: 'index',
                                intersect: false
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        stepSize: 1,
                                        font: {
                                            size: 10,
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#6B7280'
                                    },
                                    grid: {
                                        color: 'rgba(0, 0, 0, 0.05)',
                                        drawBorder: false
                                    },
                                    title: {
                                        display: true,
                                        text: 'Number of Absent Students',
                                        font: {
                                            size: 10,
                                            weight: '600',
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#374151',
                                        padding: { top: 8, bottom: 8 }
                                    }
                                },
                                x: {
                                    ticks: {
                                        font: {
                                            size: 12,
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#6B7280'
                                    },
                                    grid: {
                                        display: false
                                    },
                                    title: {
                                        display: true,
                                        text: 'Month',
                                        font: {
                                            size: 12,
                                            weight: '600',
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#374151',
                                        padding: { top: 10, bottom: 10 }
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    position: 'top',
                                    align: 'end',
                                    labels: {
                                        font: {
                                            size: 12,
                                            weight: '500',
                                            family: "'Inter', sans-serif"
                                        },
                                        padding: 20,
                                        usePointStyle: true,
                                        pointStyle: 'circle',
                                        color: '#374151'
                                    }
                                },
                                title: {
                                    display: false // Title is handled by the h3 element
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(17, 24, 39, 0.9)',
                                    titleFont: {
                                        size: 13,
                                        weight: '600',
                                        family: "'Inter', sans-serif"
                                    },
                                    bodyFont: {
                                        size: 12,
                                        family: "'Inter', sans-serif"
                                    },
                                    padding: 12,
                                    cornerRadius: 8,
                                    callbacks: {
                                        title: function(context) {
                                            return context[0].label; // Month name
                                        },
                                        label: function(context) {
                                            const count = context.raw;
                                            if (count === 0) {
                                                return `${context.dataset.label}: No absent students`;
                                            }
                                            return `${context.dataset.label}: ${count} absent student${count > 1 ? 's' : ''}`;
                                        }
                                    }
                                }
                            },
                            animation: {
                                duration: 750,
                                easing: 'easeInOutQuart'
                            }
                        }
                    });
                })
                .catch(error => {
                    console.error('Error:', error);
                    chartContainer.innerHTML = `
                        <h5 class="mb-4 text-base font-semibold text-gray-800">Absent Students by Batch (Monthly Overview)</h5>
                        <div class="h-80 overflow-x-auto">
                            <div style="width: 1200px; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                                <p class="mb-2 text-sm font-semibold text-red-500">Error loading absent students data</p>
                                <p class="text-xs text-gray-500">${error.message}</p>
                            </div>
                        </div>
                    `;
                });
        }

        // Function to update Going Out Time In/Out Chart
        function updateGoingOutTimeInOutChart() {
            const chartContainer = document.getElementById('goingOutTimeInOut').querySelector('.h-80');
            const canvas = document.getElementById('goingOutTimeInOutChart');

            fetch('{{ route('monitor.time-inout-by-batch') }}?type=going_out')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        throw new Error(data.message || 'Error fetching data');
                    }

                    console.log('Raw time data received:', data); // Debug log

                    if (goingOutTimeInOutChart instanceof Chart) {
                        goingOutTimeInOutChart.destroy();
                    }

                    if (!data || Object.keys(data).length === 0) {
                        chartContainer.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">No going out time in/out data available for today</div>';
                        return;
                    }

                    const ctx = canvas.getContext('2d');
                    goingOutTimeInOutChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['Male', 'Female'],
                            datasets: [
                                {
                                    label: 'Log Out Time',
                                    data: [data.Male.time_out_count, data.Female.time_out_count],
                                    backgroundColor: 'rgb(255, 255, 255)', // Solid Orange
                                    borderColor: 'rgb(249, 115, 22)',
                                    borderWidth: 2,
                                    borderRadius: 3,
                                    barThickness: 35
                                },
                                {
                                    label: 'Log In Time',
                                    data: [data.Male.time_in_count, data.Female.time_in_count],
                                    backgroundColor: 'rgb(255, 255, 255)', // Striped Blue
                                    borderColor: 'rgb(59, 130, 246)',
                                    borderWidth: 2,
                                    borderRadius: 3,
                                    barThickness: 35
                                },
                                {
                                    label: 'Late Students',
                                    data: [data.Male.late_count, data.Female.late_count],
                                    backgroundColor:  'rgb(255, 255, 255)', // Dotted Red
                                    borderColor: 'rgb(239, 68, 68)',
                                    borderWidth: 2,
                                    borderRadius: 3,
                                    barThickness: 35
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            interaction: {
                                mode: 'nearest',
                                intersect: true
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        stepSize: 1,
                                        font: {
                                            size: 10,
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#6B7280'
                                    },
                                    grid: {
                                        color: 'rgba(0, 0, 0, 0.05)',
                                        drawBorder: false
                                    },
                                    title: {
                                        display: true,
                                        text: 'Number of Students',
                                        font: {
                                            size: 10,
                                            weight: '600',
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#374151',
                                        padding: { top: 8, bottom: 8 }
                                    }
                                },
                                x: {
                                    ticks: {
                                        font: {
                                            size: 10,
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#6B7280'
                                    },
                                    grid: {
                                        display: false
                                    },
                                    title: {
                                        display: true,
                                        text: 'Gender',
                                        font: {
                                            size: 11,
                                            weight: '500',
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#374151',
                                        padding: { top: 15, bottom: 5 }
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    position: 'top',
                                    align: 'end',
                                    labels: {
                                        font: {
                                            size: 12,
                                            weight: '500',
                                            family: "'Inter', sans-serif"
                                        },
                                        padding: 20,
                                        usePointStyle: true,
                                        pointStyle: 'circle',
                                        color: '#374151'
                                    }
                                },
                                title: {
                                    display: true,
                                    text: 'Going Out Check In/Out Time and Late Students Distribution by Gender',
                                    font: {
                                        size: 12,
                                        weight: '600',
                                        family: "'Inter', sans-serif"
                                    },
                                    color: '#111827',
                                    padding: {
                                        top: 10,
                                        bottom: 20
                                    }
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(17, 24, 39, 0.95)',
                                    titleFont: {
                                        size: 14,
                                        weight: '700',
                                        family: "'Inter', sans-serif"
                                    },
                                    bodyFont: {
                                        size: 13,
                                        family: "'Inter', sans-serif"
                                    },
                                    padding: 12,
                                    cornerRadius: 8,
                                    callbacks: {
                                        label: function(context) {
                                            return `${context.dataset.label}: ${context.raw} students`;
                                        }
                                    }
                                }
                            },
                            animation: {
                                duration: 750,
                                easing: 'easeInOutQuart'
                            }
                        }
                    });
                })
                .catch(error => {
                    console.error('Error:', error);
                    chartContainer.innerHTML = `
                        <div class="flex flex-col items-center justify-center h-full">
                            <p class="mb-2 font-semibold text-red-500">Error loading going out time in/out data</p>
                            <p class="text-sm text-gray-500">${error.message}</p>
                        </div>
                    `;
                });
        }

        // Function to handle academic chart filter
        function handleAcademicChartFilter() {
            const selector = document.getElementById('academicChartSelector');
            const analyticsContainer = document.getElementById('analyticsContainer');
            const timeInOutContainer = document.getElementById('timeInOutContainer');
            const absentContainer = document.getElementById('absentStudentsContainer');

            const selectedValue = selector.value;

            switch(selectedValue) {
                case 'timeinout':
                    // Show only Time In/Out chart in full width
                    analyticsContainer.className = 'grid grid-cols-1 gap-6 mb-8 transition-all duration-500';
                    timeInOutContainer.style.display = 'block';
                    timeInOutContainer.className = 'p-6 bg-white shadow-md rounded-2xl col-span-1';
                    absentContainer.style.display = 'none';
                    break;

                case 'absent':
                    // Show only Absent chart in full width
                    analyticsContainer.className = 'grid grid-cols-1 gap-6 mb-8 transition-all duration-500';
                    timeInOutContainer.style.display = 'none';
                    absentContainer.style.display = 'block';
                    absentContainer.className = 'p-6 bg-white shadow-md rounded-2xl col-span-1';
                    break;
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Feather icons
            feather.replace();

            // Set up academic chart filter
            const academicChartSelector = document.getElementById('academicChartSelector');
            academicChartSelector.addEventListener('change', handleAcademicChartFilter);

            // Apply initial filter state (default to Time In/Out)
            handleAcademicChartFilter();

            // Initial data load
            updateTodayOverview();
            updateAcademicTimeInOutChart();
            updateAcademicAbsentChart();

            // Set up periodic refresh (every 5 minutes)
            setInterval(() => {
                if (!isFlipped) {
                    updateTodayOverview();
                    updateAcademicTimeInOutChart();
                } else {
                    updateGoingOutOverview();
                    updateGoingOutTimeInOutChart();
                }
                // Always update absent chart since it's separate from flip
                updateAcademicAbsentChart();
            }, 300000);
        });
    </script>

</x-monitorLayout>
