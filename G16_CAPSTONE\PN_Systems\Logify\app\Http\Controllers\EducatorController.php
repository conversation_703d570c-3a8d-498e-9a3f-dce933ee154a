<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\Academic;
use App\Models\Going_out;
use App\Models\Schedule;

class EducatorController extends Controller
{
    public function show()
    {
        $user = session('user');
        // dd($user);
        $academicSchedule = Schedule::whereNotNull('valid_until')
            ->whereNotNull('batch')
            ->whereNotNull('pn_group')
            ->get();
        // dd($academicSchedule);

        $goingOutSchedule = Schedule::whereNotNull('valid_until')
            ->whereNotNull('gender')
            ->get();

        $irregularSchedule = Schedule::whereNotNull('valid_until')
            ->whereNotNull('student_id')
            ->get();
        return view('user-educator.dashboard', compact('academicSchedule', 'goingOutSchedule', 'irregularSchedule', 'user'), );
    }
    public function getTodayAttendance()
    {
        try {
            $today = now()->format('Y-m-d');
            $isSunday = now()->isSunday();

            // If it's Sunday, return empty data since there's no academic schedule
            if ($isSunday) {
                return response()->json([
                    'present' => 0,
                    'onTime' => 0,
                    'late' => 0,
                    'isAcademic' => true
                ]);
            }

            // Get Academic attendance data for weekdays
            $present = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_out')
                ->count();

            $onTime = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'On Time')
                ->count();

            $late = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'Late')
                ->count();

            Log::info('Fetched academic attendance summary.', [
                'date' => $today,
                'present' => $present,
                'onTime' => $onTime,
                'late' => $late,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'present' => $present,
                'onTime' => $onTime,
                'late' => $late,
                'isAcademic' => true
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching academic attendance data:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to fetch academic attendance data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getGoingOutAttendance()
    {
        try {
            $today = now()->format('Y-m-d');

            // Get Going Out attendance data
            $present = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_out')
                ->count();

            $onTime = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'On Time')
                ->count();

            $late = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'Late')
                ->count();

            Log::info('Fetched going out attendance summary.', [
                'date' => $today,
                'present' => $present,
                'onTime' => $onTime,
                'late' => $late,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'present' => $present,
                'onTime' => $onTime,
                'late' => $late
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching going out attendance data:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to fetch going out attendance data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getAcademicLogInOutData()
    {
        try {
            $today = now()->format('Y-m-d');
            $isSunday = now()->isSunday();

            // If it's Sunday, return empty data since there's no academic schedule
            if ($isSunday) {
                return response()->json([
                    'logIn' => [
                        'total' => 0,
                        'onTime' => 0,
                        'late' => 0,
                        'early' => 0
                    ],
                    'logOut' => [
                        'total' => 0,
                        'onTime' => 0,
                        'late' => 0,
                        'early' => 0
                    ]
                ]);
            }

            // Get Academic log in data
            $logInTotal = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_in')
                ->count();

            $logInOnTime = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'On Time')
                ->count();

            $logInLate = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'Late')
                ->count();

            $logInEarly = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'Early')
                ->count();

            // Get Academic log out data
            $logOutTotal = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_out')
                ->count();

            $logOutOnTime = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_out')
                ->where('time_out_remark', 'On Time')
                ->count();

            $logOutLate = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_out')
                ->where('time_out_remark', 'Late')
                ->count();

            $logOutEarly = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_out')
                ->where('time_out_remark', 'Early')
                ->count();

            return response()->json([
                'logIn' => [
                    'total' => $logInTotal,
                    'onTime' => $logInOnTime,
                    'late' => $logInLate,
                    'early' => $logInEarly
                ],
                'logOut' => [
                    'total' => $logOutTotal,
                    'onTime' => $logOutOnTime,
                    'late' => $logOutLate,
                    'early' => $logOutEarly
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching academic log in/out data:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to fetch academic log in/out data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getGoingOutLogInOutData()
    {
        try {
            $today = now()->format('Y-m-d');

            // Get Going Out log in data
            $logInTotal = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_in')
                ->count();

            $logInOnTime = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'On Time')
                ->count();

            $logInLate = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'Late')
                ->count();

            $logInEarly = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'Early')
                ->count();

            // Get Going Out log out data
            $logOutTotal = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_out')
                ->count();

            $logOutOnTime = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_out')
                ->where('time_out_remark', 'On Time')
                ->count();

            $logOutLate = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_out')
                ->where('time_out_remark', 'Late')
                ->count();

            $logOutEarly = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_out')
                ->where('time_out_remark', 'Early')
                ->count();

            return response()->json([
                'logIn' => [
                    'total' => $logInTotal,
                    'onTime' => $logInOnTime,
                    'late' => $logInLate,
                    'early' => $logInEarly
                ],
                'logOut' => [
                    'total' => $logOutTotal,
                    'onTime' => $logOutOnTime,
                    'late' => $logOutLate,
                    'early' => $logOutEarly
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching going out log in/out data:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to fetch going out log in/out data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getLateStudentsByBatch()
    {
        try {
            $today = now()->format('Y-m-d');
            $isSunday = now()->isSunday();

            if ($isSunday) {
                // Get late students by gender for Sunday
                $maleLate = Going_out::whereDate('going_out_date', $today)
                    ->whereNotNull('time_in')
                    ->where('time_in_remark', 'Late')
                    ->whereHas('studentDetail.user', function($query) {
                        $query->where('gender', 'M');
                    })
                    ->count();

                $femaleLate = Going_out::whereDate('going_out_date', $today)
                    ->whereNotNull('time_in')
                    ->where('time_in_remark', 'Late')
                    ->whereHas('studentDetail.user', function($query) {
                        $query->where('gender', 'F');
                    })
                    ->count();

                return response()->json([
                    'male_late' => $maleLate,
                    'female_late' => $femaleLate
                ]);
            } else {
                // Get late students by batch for weekdays
                $lateStudentsByBatch = DB::table('student_details as s')
                    ->select(
                        's.batch',
                        DB::raw('COUNT(DISTINCT CASE WHEN a.time_in_remark = "Late" THEN a.student_id END) as academic_late_count'),
                        DB::raw('COUNT(DISTINCT CASE WHEN g.time_in_remark = "Late" THEN g.student_id END) as going_out_late_count')
                    )
                    ->leftJoin('academics as a', function($join) use ($today) {
                        $join->on('s.student_id', '=', 'a.student_id')
                            ->whereDate('a.academic_date', $today)
                            ->whereNotNull('a.time_in')
                            ->where('a.time_in_remark', 'Late');
                    })
                    ->leftJoin('going_outs as g', function($join) use ($today) {
                        $join->on('s.student_id', '=', 'g.student_id')
                            ->whereDate('g.going_out_date', $today)
                            ->whereNotNull('g.time_in')
                            ->where('g.time_in_remark', 'Late');
                    })
                    ->groupBy('s.batch')
                    ->orderBy('s.batch')
                    ->get();

                if ($lateStudentsByBatch->isEmpty()) {
                    return response()->json([]);
                }

                return response()->json($lateStudentsByBatch);
            }
        } catch (\Exception $e) {
            Log::error('Error fetching late students data:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to fetch late students data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getStudentData()
    {
        try {
            $studentData = \DB::table('student_details')
                ->join('pnph_users', 'student_details.user_id', '=', 'pnph_users.user_id')
                ->select('student_details.batch', \DB::raw('COUNT(*) as total_students'))
                ->where('pnph_users.user_role', 'student')
                ->where('pnph_users.status', 'active')
                ->groupBy('student_details.batch')
                ->orderBy('student_details.batch')
                ->get();

            \Log::info('Educator fetched student data by batch.', [
                'user_id' => auth()->id(),
                'data' => $studentData,
            ]);

            return response()->json($studentData);
        } catch (\Exception $e) {
            \Log::error('Educator error fetching student data:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'error' => 'Failed to fetch student data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getLateAnalytics(Request $request)
    {
        try {
            $month = $request->input('month', now()->format('m'));
            $year = $request->input('year', now()->format('Y'));
            $batch = $request->input('batch');

            Log::info('Starting late analytics query', [
                'month' => $month,
                'year' => $year,
                'batch' => $batch
            ]);

            // Get academic late records with separate time_out and time_in counts
            $academicQuery = DB::table('academics as a')
                ->join('student_details as s', 'a.student_id', '=', 's.student_id')
                ->join('pnph_users as u', 's.user_id', '=', 'u.user_id')
                ->select(
                    's.student_id',
                    'u.user_fname as first_name',
                    'u.user_lname as last_name',
                    's.batch',
                    's.group',
                    // Academic log out late counts
                    DB::raw('COUNT(DISTINCT CASE WHEN a.time_out_remark = "Late" THEN a.id END) as academic_logout_late_count'),
                    DB::raw('COUNT(DISTINCT CASE WHEN a.time_out_remark = "Late" AND a.time_out_consideration = "Excused" THEN a.id END) as academic_logout_excused_count'),
                    // Academic log in late counts
                    DB::raw('COUNT(DISTINCT CASE WHEN a.time_in_remark = "Late" THEN a.id END) as academic_login_late_count'),
                    DB::raw('COUNT(DISTINCT CASE WHEN a.time_in_remark = "Late" AND a.educator_consideration = "Excused" THEN a.id END) as academic_login_excused_count'),
                    DB::raw('"academic" as late_type')
                )
                ->whereMonth('a.academic_date', $month)
                ->whereYear('a.academic_date', $year)
                ->where(function($query) {
                    $query->where('a.time_out_remark', 'Late')
                          ->orWhere('a.time_in_remark', 'Late');
                })
                ->where('u.status', 'active');

            // Get going out late records with separate time_out and time_in counts
            $goingOutQuery = DB::table('going_outs as g')
                ->join('student_details as s', 'g.student_id', '=', 's.student_id')
                ->join('pnph_users as u', 's.user_id', '=', 'u.user_id')
                ->select(
                    's.student_id',
                    'u.user_fname as first_name',
                    'u.user_lname as last_name',
                    's.batch',
                    's.group',
                    // Going out log out late counts
                    DB::raw('COUNT(DISTINCT CASE WHEN g.time_out_remark = "Late" THEN g.id END) as goingout_logout_late_count'),
                    DB::raw('COUNT(DISTINCT CASE WHEN g.time_out_remark = "Late" AND g.time_out_consideration = "Excused" THEN g.id END) as goingout_logout_excused_count'),
                    // Going out log in late counts
                    DB::raw('COUNT(DISTINCT CASE WHEN g.time_in_remark = "Late" THEN g.id END) as goingout_login_late_count'),
                    DB::raw('COUNT(DISTINCT CASE WHEN g.time_in_remark = "Late" AND g.educator_consideration = "Excused" THEN g.id END) as goingout_login_excused_count'),
                    DB::raw('"going_out" as late_type')
                )
                ->whereMonth('g.going_out_date', $month)
                ->whereYear('g.going_out_date', $year)
                ->where(function($query) {
                    $query->where('g.time_out_remark', 'Late')
                          ->orWhere('g.time_in_remark', 'Late');
                })
                ->where('u.status', 'active');

            if ($batch) {
                $academicQuery->where('s.batch', $batch);
                $goingOutQuery->where('s.batch', $batch);
            }

            try {
                $academicLateStudents = $academicQuery->groupBy(
                    's.student_id',
                    'u.user_fname',
                    'u.user_lname',
                    's.batch',
                    's.group'
                )->get();

                Log::info('Academic late students query completed', [
                    'count' => $academicLateStudents->count()
                ]);
            } catch (\Exception $e) {
                Log::error('Error in academic late students query', [
                    'error' => $e->getMessage(),
                    'sql' => $academicQuery->toSql(),
                    'bindings' => $academicQuery->getBindings()
                ]);
                throw $e;
            }

            try {
                $goingOutLateStudents = $goingOutQuery->groupBy(
                    's.student_id',
                    'u.user_fname',
                    'u.user_lname',
                    's.batch',
                    's.group'
                )->get();

                Log::info('Going out late students query completed', [
                    'count' => $goingOutLateStudents->count()
                ]);
            } catch (\Exception $e) {
                Log::error('Error in going out late students query', [
                    'error' => $e->getMessage(),
                    'sql' => $goingOutQuery->toSql(),
                    'bindings' => $goingOutQuery->getBindings()
                ]);
                throw $e;
            }

            // Combine and merge the results with detailed breakdown
            $lateStudents = collect([...$academicLateStudents, ...$goingOutLateStudents])
                ->groupBy('student_id')
                ->map(function ($group) {
                    $first = $group->first();

                    // Initialize counters
                    $academic_logout_late = 0;
                    $academic_logout_excused = 0;
                    $academic_login_late = 0;
                    $academic_login_excused = 0;
                    $goingout_logout_late = 0;
                    $goingout_logout_excused = 0;
                    $goingout_login_late = 0;
                    $goingout_login_excused = 0;

                    // Sum up counts from both academic and going out records
                    foreach ($group as $record) {
                        if ($record->late_type === 'academic') {
                            $academic_logout_late += $record->academic_logout_late_count ?? 0;
                            $academic_logout_excused += $record->academic_logout_excused_count ?? 0;
                            $academic_login_late += $record->academic_login_late_count ?? 0;
                            $academic_login_excused += $record->academic_login_excused_count ?? 0;
                        } else {
                            $goingout_logout_late += $record->goingout_logout_late_count ?? 0;
                            $goingout_logout_excused += $record->goingout_logout_excused_count ?? 0;
                            $goingout_login_late += $record->goingout_login_late_count ?? 0;
                            $goingout_login_excused += $record->goingout_login_excused_count ?? 0;
                        }
                    }

                    // Calculate totals
                    $total_logout_late = $academic_logout_late + $goingout_logout_late;
                    $total_login_late = $academic_login_late + $goingout_login_late;
                    $total_logout_excused = $academic_logout_excused + $goingout_logout_excused;
                    $total_login_excused = $academic_login_excused + $goingout_login_excused;
                    $total_late = $total_logout_late + $total_login_late;
                    $total_excused = $total_logout_excused + $total_login_excused;

                    return [
                        'student_id' => $first->student_id,
                        'first_name' => $first->first_name,
                        'last_name' => $first->last_name,
                        'batch' => $first->batch,
                        'group' => $first->group,
                        'logout_late_count' => $total_logout_late,
                        'logout_excused_count' => $total_logout_excused,
                        'login_late_count' => $total_login_late,
                        'login_excused_count' => $total_login_excused,
                        'total_late_count' => $total_late,
                        'total_excused_count' => $total_excused,
                        'late_types' => $group->pluck('late_type')->toArray()
                    ];
                })
                ->values()
                ->sortByDesc('total_late_count');

            Log::info('Combined late students data', [
                'total_count' => $lateStudents->count()
            ]);

            $batches = DB::table('student_details')
                ->select('batch')
                ->distinct()
                ->orderBy('batch')
                ->pluck('batch');

            return response()->json([
                'lateStudents' => $lateStudents,
                'batches' => $batches,
                'currentMonth' => $month,
                'currentYear' => $year,
                'selectedBatch' => $batch
            ]);
        } catch (\Exception $e) {
            Log::error('Error in late analytics', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'month' => $month ?? null,
                'year' => $year ?? null,
                'batch' => $batch ?? null
            ]);

            return response()->json([
                'error' => 'Failed to fetch late analytics',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getStudentLateHistory(Request $request)
    {
        try {
            $studentId = $request->input('student_id');
            $month = $request->input('month', now()->format('m'));
            $year = $request->input('year', now()->format('Y'));

            Log::info('Fetching late history', [
                'student_id' => $studentId,
                'month' => $month,
                'year' => $year
            ]);

            if (!$studentId) {
                Log::warning('Student ID missing for late history request');
                return response()->json([
                    'success' => false,
                    'message' => 'Student ID is required'
                ], 400);
            }

            // Get academic late history - separate log out and log in records
            $academicLogoutLateHistory = DB::table('academics as a')
                ->join('student_details as s', 'a.student_id', '=', 's.student_id')
                ->join('pnph_users as u', 's.user_id', '=', 'u.user_id')
                ->select(
                    'a.academic_date',
                    'a.time_out_consideration as consideration',
                    DB::raw('"academic" as late_type'),
                    DB::raw('"Log Out" as logging_process')
                )
                ->where('s.student_id', $studentId)
                ->whereMonth('a.academic_date', $month)
                ->whereYear('a.academic_date', $year)
                ->where('a.time_out_remark', 'Late');

            $academicLoginLateHistory = DB::table('academics as a')
                ->join('student_details as s', 'a.student_id', '=', 's.student_id')
                ->join('pnph_users as u', 's.user_id', '=', 'u.user_id')
                ->select(
                    'a.academic_date',
                    'a.educator_consideration as consideration',
                    DB::raw('"academic" as late_type'),
                    DB::raw('"Log In" as logging_process')
                )
                ->where('s.student_id', $studentId)
                ->whereMonth('a.academic_date', $month)
                ->whereYear('a.academic_date', $year)
                ->where('a.time_in_remark', 'Late');

            // Get going out late history - separate log out and log in records
            $goingOutLogoutLateHistory = DB::table('going_outs as g')
                ->join('student_details as s', 'g.student_id', '=', 's.student_id')
                ->join('pnph_users as u', 's.user_id', '=', 'u.user_id')
                ->select(
                    'g.going_out_date as academic_date',
                    'g.time_out_consideration as consideration',
                    DB::raw('"going_out" as late_type'),
                    DB::raw('"Log Out" as logging_process')
                )
                ->where('s.student_id', $studentId)
                ->whereMonth('g.going_out_date', $month)
                ->whereYear('g.going_out_date', $year)
                ->where('g.time_out_remark', 'Late');

            $goingOutLoginLateHistory = DB::table('going_outs as g')
                ->join('student_details as s', 'g.student_id', '=', 's.student_id')
                ->join('pnph_users as u', 's.user_id', '=', 'u.user_id')
                ->select(
                    'g.going_out_date as academic_date',
                    'g.educator_consideration as consideration',
                    DB::raw('"going_out" as late_type'),
                    DB::raw('"Log In" as logging_process')
                )
                ->where('s.student_id', $studentId)
                ->whereMonth('g.going_out_date', $month)
                ->whereYear('g.going_out_date', $year)
                ->where('g.time_in_remark', 'Late');

            // Combine all late history records
            $lateHistory = $academicLogoutLateHistory
                ->union($academicLoginLateHistory)
                ->union($goingOutLogoutLateHistory)
                ->union($goingOutLoginLateHistory)
                ->orderBy('academic_date', 'desc')
                ->orderBy('logging_process', 'desc') // Log Out first, then Log In for same date
                ->get();

            Log::info('Late history query completed', [
                'student_id' => $studentId,
                'record_count' => $lateHistory->count(),
                'month' => $month,
                'year' => $year
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'lateHistory' => $lateHistory
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching student late history', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'student_id' => $studentId ?? null,
                'month' => $month ?? null,
                'year' => $year ?? null
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch student late history: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getAbsentStudentsByBatch()
    {
        try {
            $currentYear = now()->year;
            $currentMonth = now()->month;

            // Get all distinct batches
            $allBatches = DB::table('student_details')
                ->distinct()
                ->pluck('batch')
                ->sort()
                ->values();

            // Get absent students data for all months of the current year (excluding valid absences)
            $absentData = DB::table('academics as a')
                ->join('student_details as s', 'a.student_id', '=', 's.student_id')
                ->select(
                    DB::raw('MONTH(a.academic_date) as month'),
                    's.batch',
                    DB::raw('COUNT(CASE
                        WHEN (a.educator_consideration = "Absent" AND (a.time_in_absent_validation IS NULL OR a.time_in_absent_validation = "not_valid"))
                        OR (a.time_out_consideration = "Absent" AND (a.time_out_absent_validation IS NULL OR a.time_out_absent_validation = "not_valid"))
                        THEN 1 END) as absent_count')
                )
                ->whereYear('a.academic_date', $currentYear)
                ->groupBy(DB::raw('MONTH(a.academic_date)'), 's.batch')
                ->having('absent_count', '>', 0)
                ->orderBy(DB::raw('MONTH(a.academic_date)'))
                ->orderBy('s.batch')
                ->get();

            // Create a complete dataset with all months and all batches
            $monthNames = [
                1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
                5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
                9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
            ];

            $completeData = [];

            // Initialize all 12 months
            for ($month = 1; $month <= 12; $month++) {
                $monthData = [
                    'month' => $month,
                    'month_name' => $monthNames[$month],
                    'batches' => []
                ];

                // Initialize all batches for this month
                foreach ($allBatches as $batch) {
                    $existingData = $absentData->where('month', $month)->where('batch', $batch)->first();
                    $monthData['batches'][] = [
                        'batch' => $batch,
                        'absent_count' => $existingData ? $existingData->absent_count : 0
                    ];
                }

                $completeData[] = $monthData;
            }

            Log::info('Educator fetched absent students by batch (monthly).', [
                'year' => $currentYear,
                'months_included' => 12, // All 12 months
                'current_month' => $currentMonth,
                'batches_count' => count($allBatches),
                'user_id' => auth()->user()->user_id ?? null,
            ]);

            return response()->json($completeData);
        } catch (\Exception $e) {
            Log::error('Educator error fetching absent students by batch:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to fetch absent students data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getAbsentAnalytics(Request $request)
    {
        try {
            $month = $request->input('month', now()->format('m'));
            $year = $request->input('year', now()->format('Y'));
            $batch = $request->input('batch');

            Log::info('Starting absent analytics query', [
                'month' => $month,
                'year' => $year,
                'batch' => $batch
            ]);

            // Query for absent students from academics table only (excluding valid absences)
            $academicAbsentQuery = DB::table('academics as a')
                ->join('student_details as s', 'a.student_id', '=', 's.student_id')
                ->join('pnph_users as u', 's.user_id', '=', 'u.user_id')
                ->select(
                    'a.student_id',
                    'u.user_fname as first_name',
                    'u.user_lname as last_name',
                    's.batch',
                    's.group',
                    DB::raw('COUNT(CASE
                        WHEN (a.educator_consideration = "Absent" AND (a.time_in_absent_validation IS NULL OR a.time_in_absent_validation = "not_valid"))
                        OR (a.time_out_consideration = "Absent" AND (a.time_out_absent_validation IS NULL OR a.time_out_absent_validation = "not_valid"))
                        THEN 1 END) as academic_absent_count')
                )
                ->whereMonth('a.academic_date', $month)
                ->whereYear('a.academic_date', $year)
                ->where(function($query) {
                    $query->where(function($subQuery) {
                        // Time in absent that is not valid
                        $subQuery->where('a.educator_consideration', 'Absent')
                                ->where(function($validationQuery) {
                                    $validationQuery->whereNull('a.time_in_absent_validation')
                                                  ->orWhere('a.time_in_absent_validation', 'not_valid');
                                });
                    })->orWhere(function($subQuery) {
                        // Time out absent that is not valid
                        $subQuery->where('a.time_out_consideration', 'Absent')
                                ->where(function($validationQuery) {
                                    $validationQuery->whereNull('a.time_out_absent_validation')
                                                  ->orWhere('a.time_out_absent_validation', 'not_valid');
                                });
                    });
                })
                ->groupBy('a.student_id', 'u.user_fname', 'u.user_lname', 's.batch', 's.group')
                ->having('academic_absent_count', '>', 0);

            // Apply batch filter if provided
            if ($batch) {
                $academicAbsentQuery->where('s.batch', $batch);
            }

            $absentStudents = $academicAbsentQuery->get();

            Log::info('Absent students query executed', [
                'academic_count' => $absentStudents->count()
            ]);

            // Convert to collection and sort by academic absent count
            $allAbsentStudents = $absentStudents->map(function ($student) {
                return [
                    'student_id' => $student->student_id,
                    'first_name' => $student->first_name,
                    'last_name' => $student->last_name,
                    'batch' => $student->batch,
                    'group' => $student->group,
                    'academic_absent_count' => $student->academic_absent_count
                ];
            })->sortByDesc('academic_absent_count')->values();

            Log::info('Combined absent students data', [
                'total_count' => $allAbsentStudents->count()
            ]);

            $batches = DB::table('student_details')
                ->select('batch')
                ->distinct()
                ->orderBy('batch')
                ->pluck('batch');

            return response()->json([
                'absentStudents' => $allAbsentStudents,
                'batches' => $batches,
                'filters' => [
                    'month' => $month,
                    'year' => $year,
                    'batch' => $batch
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error in absent analytics', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'month' => $month ?? null,
                'year' => $year ?? null,
                'batch' => $batch ?? null
            ]);

            return response()->json([
                'error' => 'Failed to fetch absent analytics data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getStudentAbsentHistory(Request $request)
    {
        try {
            $studentId = $request->input('student_id');
            $month = $request->input('month', now()->format('m'));
            $year = $request->input('year', now()->format('Y'));

            Log::info('Fetching student absent history', [
                'student_id' => $studentId,
                'month' => $month,
                'year' => $year
            ]);

            if (!$studentId) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student ID is required'
                ], 400);
            }

            // Academic absent history (both educator and timeout considerations) with validation
            $academicAbsentHistory = DB::table('academics as a')
                ->join('student_details as s', 'a.student_id', '=', 's.student_id')
                ->join('pnph_users as u', 's.user_id', '=', 'u.user_id')
                ->select(
                    'a.academic_date',
                    DB::raw('CASE
                        WHEN a.educator_consideration = "Absent" THEN a.educator_consideration
                        WHEN a.time_out_consideration = "Absent" THEN a.time_out_consideration
                        ELSE "Absent"
                    END as consideration'),
                    DB::raw('"academic" as absent_type'),
                    DB::raw('CASE
                        WHEN a.educator_consideration = "Absent" THEN "Time In"
                        WHEN a.time_out_consideration = "Absent" THEN "Time Out"
                        ELSE "Academic"
                    END as logging_process'),
                    DB::raw('CASE
                        WHEN a.educator_consideration = "Absent" THEN a.time_in_absent_validation
                        WHEN a.time_out_consideration = "Absent" THEN a.time_out_absent_validation
                        ELSE NULL
                    END as validation')
                )
                ->where('s.student_id', $studentId)
                ->whereMonth('a.academic_date', $month)
                ->whereYear('a.academic_date', $year)
                ->where(function($query) {
                    $query->where('a.educator_consideration', 'Absent')
                          ->orWhere('a.time_out_consideration', 'Absent');
                })
                ->orderBy('a.academic_date', 'desc')
                ->get();

            $absentHistory = $academicAbsentHistory;

            Log::info('Absent history query completed', [
                'student_id' => $studentId,
                'record_count' => $absentHistory->count(),
                'month' => $month,
                'year' => $year
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'absentHistory' => $absentHistory
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching student absent history', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'student_id' => $studentId ?? null,
                'month' => $month ?? null,
                'year' => $year ?? null
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch student absent history: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getTimeInOutByBatch(Request $request)
    {
        try {
            $today = now()->format('Y-m-d');
            $type = $request->input('type', 'academic'); // Default to academic if not specified

            if ($type === 'going_out') {
                // Get Going Out time in/out data
                $timeData = DB::table('going_outs as g')
                    ->join('student_details as s', 'g.student_id', '=', 's.student_id')
                    ->join('pnph_users as u', 's.user_id', '=', 'u.user_id')
                    ->select(
                        's.batch',
                        'u.gender',
                        DB::raw('COUNT(DISTINCT CASE WHEN g.time_out IS NOT NULL THEN g.student_id END) as time_out_count'),
                        DB::raw('COUNT(DISTINCT CASE WHEN g.time_in IS NOT NULL THEN g.student_id END) as time_in_count'),
                        DB::raw('COUNT(DISTINCT CASE WHEN g.time_in_remark = "Late" THEN g.student_id END) as late_count')
                    )
                    ->whereDate('g.going_out_date', $today)
                    ->groupBy('s.batch', 'u.gender')
                    ->orderBy('s.batch')
                    ->get();

                // Process the data to group by gender
                $processedData = [
                    'Male' => [
                        'time_out_count' => 0,
                        'time_in_count' => 0,
                        'late_count' => 0,
                        'batches' => []
                    ],
                    'Female' => [
                        'time_out_count' => 0,
                        'time_in_count' => 0,
                        'late_count' => 0,
                        'batches' => []
                    ]
                ];

                foreach ($timeData as $record) {
                    $gender = $record->gender === 'M' ? 'Male' : 'Female';

                    // Add to total counts
                    $processedData[$gender]['time_out_count'] += $record->time_out_count;
                    $processedData[$gender]['time_in_count'] += $record->time_in_count;
                    $processedData[$gender]['late_count'] += $record->late_count;

                    // Add batch details
                    $processedData[$gender]['batches'][] = [
                        'batch' => $record->batch,
                        'time_out_count' => $record->time_out_count,
                        'time_in_count' => $record->time_in_count,
                        'late_count' => $record->late_count
                    ];
                }

                return response()->json($processedData);
            } else {
                // Get Academic time in/out data
                $timeData = DB::table('academics as a')
                    ->join('student_details as s', 'a.student_id', '=', 's.student_id')
                    ->select(
                        's.batch',
                        DB::raw('COUNT(DISTINCT CASE WHEN a.time_out IS NOT NULL THEN a.student_id END) as time_out_count'),
                        DB::raw('COUNT(DISTINCT CASE WHEN a.time_in IS NOT NULL THEN a.student_id END) as time_in_count'),
                        DB::raw('COUNT(DISTINCT CASE WHEN a.time_in_remark = "Late" THEN a.student_id END) as late_count')
                    )
                    ->whereDate('a.academic_date', $today)
                    ->groupBy('s.batch')
                    ->orderBy('s.batch')
                    ->get();

                return response()->json($timeData);
            }
        } catch (\Exception $e) {
            Log::error('Error fetching time in/out data:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to fetch time in/out data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get recent student time in/out activities for real-time notifications
     * Uses the same logic as navigation badges to ensure accuracy
     */
    public function getRecentActivities(Request $request)
    {
        try {
            $today = now()->format('Y-m-d');
            $activities = [];

            // Get the last viewed timestamps for each log type (same as navigation badges)
            $academicLastViewed = \App\Models\NotificationView::getLastViewed('academic');
            $goingOutLastViewed = \App\Models\NotificationView::getLastViewed('goingout');
            $visitorLastViewed = \App\Models\NotificationView::getLastViewed('visitor');

            Log::info('Checking for recent activities using navigation badge logic', [
                'academic_last_viewed' => $academicLastViewed,
                'goingout_last_viewed' => $goingOutLastViewed,
                'visitor_last_viewed' => $visitorLastViewed,
                'today' => $today
            ]);

            // Get academic activities (same logic as NotificationController)
            $academicTimeOutQuery = \App\Models\Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_out')
                ->with(['studentDetail.user']);

            if ($academicLastViewed) {
                $academicTimeOutQuery->where(function($q) use ($academicLastViewed) {
                    $q->where('updated_at', '>', $academicLastViewed)
                      ->orWhere('created_at', '>', $academicLastViewed);
                });
            }

            $academicTimeInQuery = \App\Models\Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_in')
                ->with(['studentDetail.user']);

            if ($academicLastViewed) {
                $academicTimeInQuery->where(function($q) use ($academicLastViewed) {
                    $q->where('updated_at', '>', $academicLastViewed)
                      ->orWhere('created_at', '>', $academicLastViewed);
                });
            }

            // Get the activities
            $academicTimeOuts = $academicTimeOutQuery->orderBy('updated_at', 'desc')->get();
            $academicTimeIns = $academicTimeInQuery->orderBy('updated_at', 'desc')->get();

            Log::info('Found academic activities', [
                'time_outs' => $academicTimeOuts->count(),
                'time_ins' => $academicTimeIns->count()
            ]);

            // Process academic activities - determine most recent action per student
            $academicStudentActions = [];

            // Collect time out activities
            foreach ($academicTimeOuts as $activity) {
                $studentKey = $activity->student_id;
                $timeOutTimestamp = \Carbon\Carbon::parse($activity->time_out);

                if (!isset($academicStudentActions[$studentKey]) ||
                    $timeOutTimestamp->greaterThan(\Carbon\Carbon::parse($academicStudentActions[$studentKey]['timestamp']))) {
                    $academicStudentActions[$studentKey] = [
                        'id' => $activity->id,
                        'student_id' => $activity->student_id,
                        'student_name' => $activity->studentDetail->user->user_fname . ' ' . $activity->studentDetail->user->user_lname,
                        'batch' => $activity->studentDetail->batch,
                        'type' => 'academic',
                        'action' => 'time_out',
                        'time' => $timeOutTimestamp->format('g:i A'),
                        'timestamp' => $activity->updated_at,
                        'action_timestamp' => $timeOutTimestamp
                    ];
                }
            }

            // Collect time in activities and compare with time out
            foreach ($academicTimeIns as $activity) {
                $studentKey = $activity->student_id;
                $timeInTimestamp = \Carbon\Carbon::parse($activity->time_in);

                if (!isset($academicStudentActions[$studentKey]) ||
                    $timeInTimestamp->greaterThan($academicStudentActions[$studentKey]['action_timestamp'])) {
                    $academicStudentActions[$studentKey] = [
                        'id' => $activity->id,
                        'student_id' => $activity->student_id,
                        'student_name' => $activity->studentDetail->user->user_fname . ' ' . $activity->studentDetail->user->user_lname,
                        'batch' => $activity->studentDetail->batch,
                        'type' => 'academic',
                        'action' => 'time_in',
                        'time' => $timeInTimestamp->format('g:i A'),
                        'timestamp' => $activity->updated_at,
                        'action_timestamp' => $timeInTimestamp,
                        'is_late' => $activity->time_in_remark === 'Late'
                    ];
                }
            }

            // Add academic activities to main array
            foreach ($academicStudentActions as $studentAction) {
                unset($studentAction['action_timestamp']); // Remove helper field
                $activities[] = $studentAction;
            }

            // Get going out activities (same logic as NotificationController)
            $goingOutTimeOutQuery = \App\Models\Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_out')
                ->with(['studentDetail.user']);

            if ($goingOutLastViewed) {
                $goingOutTimeOutQuery->where(function($q) use ($goingOutLastViewed) {
                    $q->where('updated_at', '>', $goingOutLastViewed)
                      ->orWhere('created_at', '>', $goingOutLastViewed);
                });
            }

            $goingOutTimeInQuery = \App\Models\Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_in')
                ->with(['studentDetail.user']);

            if ($goingOutLastViewed) {
                $goingOutTimeInQuery->where(function($q) use ($goingOutLastViewed) {
                    $q->where('updated_at', '>', $goingOutLastViewed)
                      ->orWhere('created_at', '>', $goingOutLastViewed);
                });
            }

            // Get the activities
            $goingOutTimeOuts = $goingOutTimeOutQuery->orderBy('updated_at', 'desc')->get();
            $goingOutTimeIns = $goingOutTimeInQuery->orderBy('updated_at', 'desc')->get();

            Log::info('Found going out activities', [
                'time_outs' => $goingOutTimeOuts->count(),
                'time_ins' => $goingOutTimeIns->count()
            ]);

            // Process going out activities - determine most recent action per student
            $goingOutStudentActions = [];

            // Collect time out activities
            foreach ($goingOutTimeOuts as $activity) {
                $studentKey = $activity->student_id;
                $timeOutTimestamp = \Carbon\Carbon::parse($activity->time_out);

                if (!isset($goingOutStudentActions[$studentKey]) ||
                    $timeOutTimestamp->greaterThan(\Carbon\Carbon::parse($goingOutStudentActions[$studentKey]['timestamp']))) {
                    $goingOutStudentActions[$studentKey] = [
                        'id' => $activity->id,
                        'student_id' => $activity->student_id,
                        'student_name' => $activity->studentDetail->user->user_fname . ' ' . $activity->studentDetail->user->user_lname,
                        'batch' => $activity->studentDetail->batch,
                        'type' => 'going_out',
                        'action' => 'time_out',
                        'time' => $timeOutTimestamp->format('g:i A'),
                        'timestamp' => $activity->updated_at,
                        'action_timestamp' => $timeOutTimestamp
                    ];
                }
            }

            // Collect time in activities and compare with time out
            foreach ($goingOutTimeIns as $activity) {
                $studentKey = $activity->student_id;
                $timeInTimestamp = \Carbon\Carbon::parse($activity->time_in);

                if (!isset($goingOutStudentActions[$studentKey]) ||
                    $timeInTimestamp->greaterThan($goingOutStudentActions[$studentKey]['action_timestamp'])) {
                    $goingOutStudentActions[$studentKey] = [
                        'id' => $activity->id,
                        'student_id' => $activity->student_id,
                        'student_name' => $activity->studentDetail->user->user_fname . ' ' . $activity->studentDetail->user->user_lname,
                        'batch' => $activity->studentDetail->batch,
                        'type' => 'going_out',
                        'action' => 'time_in',
                        'time' => $timeInTimestamp->format('g:i A'),
                        'timestamp' => $activity->updated_at,
                        'action_timestamp' => $timeInTimestamp,
                        'is_late' => $activity->time_in_remark === 'Late'
                    ];
                }
            }

            // Add going out activities to main array
            foreach ($goingOutStudentActions as $studentAction) {
                unset($studentAction['action_timestamp']); // Remove helper field
                $activities[] = $studentAction;
            }

            // Get visitor activities (same logic as NotificationController)
            $visitorTimeOutQuery = \App\Models\Visitor::whereDate('visitor_date', $today)
                ->whereNotNull('time_out');

            if ($visitorLastViewed) {
                $visitorTimeOutQuery->where(function($q) use ($visitorLastViewed) {
                    $q->where('updated_at', '>', $visitorLastViewed)
                      ->orWhere('created_at', '>', $visitorLastViewed);
                });
            }

            $visitorTimeInQuery = \App\Models\Visitor::whereDate('visitor_date', $today)
                ->whereNotNull('time_in');

            if ($visitorLastViewed) {
                $visitorTimeInQuery->where(function($q) use ($visitorLastViewed) {
                    $q->where('updated_at', '>', $visitorLastViewed)
                      ->orWhere('created_at', '>', $visitorLastViewed);
                });
            }

            // Get the activities
            $visitorTimeOuts = $visitorTimeOutQuery->orderBy('updated_at', 'desc')->get();
            $visitorTimeIns = $visitorTimeInQuery->orderBy('updated_at', 'desc')->get();

            Log::info('Found visitor activities', [
                'time_outs' => $visitorTimeOuts->count(),
                'time_ins' => $visitorTimeIns->count()
            ]);

            // Process visitor activities - determine most recent action per visitor
            $visitorActions = [];

            // Collect time out activities
            foreach ($visitorTimeOuts as $activity) {
                $visitorKey = $activity->id;
                $timeOutTimestamp = \Carbon\Carbon::parse($activity->time_out);

                if (!isset($visitorActions[$visitorKey]) ||
                    $timeOutTimestamp->gt(\Carbon\Carbon::parse($visitorActions[$visitorKey]['action_timestamp']))) {

                    $visitorActions[$visitorKey] = [
                        'type' => 'visitor',
                        'visitor_id' => $activity->id,
                        'visitor_name' => $activity->visitor_name,
                        'action' => 'time_out',
                        'time' => $timeOutTimestamp->format('g:i A'),
                        'timestamp' => $timeOutTimestamp->toISOString(),
                        'action_timestamp' => $timeOutTimestamp->toISOString(),
                        'is_late' => false // Visitors don't have late status
                    ];
                }
            }

            // Collect time in activities
            foreach ($visitorTimeIns as $activity) {
                $visitorKey = $activity->id;
                $timeInTimestamp = \Carbon\Carbon::parse($activity->time_in);

                if (!isset($visitorActions[$visitorKey]) ||
                    $timeInTimestamp->gt(\Carbon\Carbon::parse($visitorActions[$visitorKey]['action_timestamp']))) {

                    $visitorActions[$visitorKey] = [
                        'type' => 'visitor',
                        'visitor_id' => $activity->id,
                        'visitor_name' => $activity->visitor_name,
                        'action' => 'time_in',
                        'time' => $timeInTimestamp->format('g:i A'),
                        'timestamp' => $timeInTimestamp->toISOString(),
                        'action_timestamp' => $timeInTimestamp->toISOString(),
                        'is_late' => false // Visitors don't have late status
                    ];
                }
            }

            // Add visitor activities to main array
            foreach ($visitorActions as $visitorAction) {
                unset($visitorAction['action_timestamp']); // Remove helper field
                $activities[] = $visitorAction;
            }

            // Sort activities by timestamp (newest first)
            usort($activities, function($a, $b) {
                return strtotime($b['timestamp']) - strtotime($a['timestamp']);
            });

            // Limit to last 10 activities to prevent overwhelming the UI
            $activities = array_slice($activities, 0, 10);

            Log::info('Fetched recent activities for notifications (synced with navigation badges)', [
                'academic_last_viewed' => $academicLastViewed,
                'goingout_last_viewed' => $goingOutLastViewed,
                'visitor_last_viewed' => $visitorLastViewed,
                'activities_count' => count($activities),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => true,
                'activities' => $activities,
                'academic_last_viewed' => $academicLastViewed,
                'goingout_last_viewed' => $goingOutLastViewed,
                'visitor_last_viewed' => $visitorLastViewed
            ]);

        } catch (\Exception $e) {
            Log::error('Error fetching recent activities:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'academic_last_viewed' => $academicLastViewed ?? null,
                'goingout_last_viewed' => $goingOutLastViewed ?? null,
                'visitor_last_viewed' => $visitorLastViewed ?? null
            ]);

            return response()->json([
                'success' => false,
                'error' => 'Failed to fetch recent activities',
                'message' => $e->getMessage(),
                'activities' => []
            ], 500);
        }
    }
}
