<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PN Dashboard</title>
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="{{ asset('css/dashboard.css') }}">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body>
  <header>
    <div class="logo">
        <img src="{{ asset('images/pnlogo-header.png') }}" alt="PN Logo">
    </div>
     <div class="user-info" style="position: absolute; right: 24px; top: 60px; display: flex; align-items: center; gap: 16px; color: black;">
        <span style="font-weight: 600; font-size: 20px;">
            Logged in as: {{ $user->name ?? 'User' }} &nbsp; Role: {{ ucfirst($user->role ?? '') }}
        </span>
        <form method="POST" action="{{ route('logout') }}" style="margin: 0;">
            @csrf
            <button type="submit" style="background: #e61515; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                Log Out
            </button>
        </form>
    </div>
</header>
<div class="container-fluid">
  <div class="row">
    <nav class="col-md-2 d-none d-md-block sidebar bg-light">
      <ul class="nav flex-column">
        <li class="nav-item"><a href="{{ route('dashboard') }}" class="nav-link sidebar-link">
           <img src="{{ asset('images/dashboard.png')}}" class="sidebar-icon">Dashboard </a>
        </li>
        <li class="nav-item"><a href="{{ route('roomtask') }}" class="nav-link">
           <img src="{{ asset('images/checklist.png')}}" class="sidebar-icon">Room Tasks</a>
          </li>
        <li class="nav-item"><a href="#" class="nav-link">
          <img src="{{ asset('images/assign.png')}}" class="sidebar-icon">General Tasks</a>
        </li>
         <li class="nav-item"><a href="{{ route('task.history') }}" class="nav-link">
           <img src="{{ asset('images/history.png')}}" class="sidebar-icon"> Room Task History</a>
        </li>
        <li class="nav-item"><a href="#" class="nav-link">
          <img src="{{ asset('images/complaint.png')}}" class="sidebar-icon">Reports</a></li>
        <li class="nav-item"><a href="#" class="nav-link">Log Out</a></li>
      </ul>
    </nav>
    </ul>
  </div>
  <div class="main-content">
    <!-- Welcome Card: above the two main cards, double width -->
    <div style="width: 100%; display: flex; justify-content: center; margin-top: 40px; margin-bottom: 0;">
      <div class="welcomeCard">
        <h2 style="font-size: 2.2rem; font-weight: 700; margin-bottom: 0.5rem;">
          Welcome, {{ $user->name ?? 'User' }}!
        </h2>
        <p style="font-size: 1.1rem; margin-bottom: 0;">
          You're logged in to the PN Tasking Hub System.<br>
          Use the cards below to view and manage checklist tasks for each room efficiently.
        </p>
      </div>
    </div>
    <div class="main-cards" style="display: flex; flex-direction: row; gap: 24px; align-items: stretch;">
      <div class="card" id="addRoomCard" style="flex: 1;">
        <h2>General Tasking</h2>
        <p>View and manage general tasks, assignments, and schedules.</p>
        <a href="/generalTask" class="card-button">Click to view task assignments</a>
      </div>
      <div class="card" style="flex: 1;">
        <h2>Room Tasking</h2>
        <p>Manage room assignments, comments, and upload pictures.</p>
        <button id="toggleFloorsBtn" class="card-button">Click to view 2nd - 7th floors</button>
        <div id="floorButtons" class="floor-buttons hidden">
          <button class="floor-btn" data-floor="2">2nd Floor</button>
          <button class="floor-btn" data-floor="3">3rd Floor</button>
          <button class="floor-btn" data-floor="4">4th Floor</button>
          <button class="floor-btn" data-floor="5">5th Floor</button>
          <button class="floor-btn" data-floor="6">6th Floor</button>
          <button class="floor-btn" data-floor="7">7th Floor</button>
        </div>
      </div>
    </div>
    <div id="roomsContainer" class="rooms-container hidden"></div>
  </div>
</div>

  <!-- Student Modal -->
  <div id="studentModal" class="modal">
    <div class="modal-content">
      <span class="close">&times;</span>
      <h2 id="modalTitle">Add Student</h2>
      <form id="studentForm">
        <input type="hidden" id="roomNumber" name="roomNumber">
        <input type="hidden" id="actionType" name="actionType" value="add">
        <div id="editStudentSelect" class="form-group" style="display: none;">
          <label for="existingStudent">Select Student:</label>
          <select id="existingStudent" name="existingStudent" class="form-control">
            <option value="">-- Select a student --</option>
          </select>
        </div>
        <div class="form-group">
          <label for="studentName">Student Name:</label>
          <input type="text" id="studentName" name="studentName" required>
        </div>
        <div class="form-actions">
          <button type="submit" class="submit-btn">Save</button>
          <button type="button" class="cancel-btn">Cancel</button>
        </div>
      </form>
    </div>
  </div>

  <!-- New Room Modal -->
  <div id="newRoomModal" class="modal">
    <div class="modal-content">
      <span class="close">&times;</span>
      <h2>Add New Room</h2>
      <form id="newRoomForm">
        <div class="form-group">
          <label for="newRoomNumber">Room Number:</label>
          <input type="number" id="newRoomNumber" name="room_number" required min="200" max="799">
        </div>
        <div class="form-actions">
          <button type="submit" class="submit-btn">Add Room</button>
          <button type="button" class="cancel-btn">Cancel</button>
        </div>
      </form>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const toggleFloorsBtn = document.getElementById('toggleFloorsBtn');
      const floorButtons = document.getElementById('floorButtons');
      const roomsContainer = document.getElementById('roomsContainer');
      const studentModal = document.getElementById('studentModal');
      const newRoomModal = document.getElementById('newRoomModal');

      // Toggle floor buttons
      toggleFloorsBtn.addEventListener('click', function() {
        floorButtons.classList.toggle('hidden');
        if (roomsContainer.classList.contains('hidden')) {
          document.querySelector('.floor-btn[data-floor="2"]').click();
        }
      });

      // Floor data with student assignments
      const floorData = {
        2: {
          rooms: ['202', '204', '205'],
          students: {
            '202': ['Carlo Pacunla', 'Jhon Mark Torres', 'Cristian Virtudazo', 'Gerald Reyes', 'Rex Hemoros'],
            '204': ['Zhards Nathan Hagonoy', 'Joshua Escala', 'Ivan Justine Alaud', 'Julius Goles', 'Adriane Montano', 'Edemar Vicaran', 'Adrian Fabrigar'],
            '205': ['Jhon Michael Mansanades', 'Rohann James Matt Naveo', 'Klint Ruales', 'Kent John Navarro', 'Albert Reboquio', 'Vinzon Areliano', 'Jhon Paul Orozco']
          }
        },
        3: {
          rooms: ['302', '304', '305'],
          students: {
            '302': ['Dionmar Paner', 'Jasper Ybañez', 'John Arnel Condez', 'Michael Jovita', 'Junrel Ejurango'],
            '304': ['Christopher Gilles', 'Mohammad Dimpas', 'Evans Est Escoro', 'Vhenz Cernal', 'Myco Cisneros', 'Jasper Ursal', 'Van Johanzel Quijano'],
            '305': ['Melvin Sabandal', 'Reil Jake Engañia', 'Jhon Xander Pila', 'Justine Diendo', 'Ronald Erebras', 'Seth Andrey Jabagat', 'Bryle Solon']
          }
        },
        4: {
          rooms: ['402', '403', '404', '405'],
          students: {
            '402': ['Mark Kevin CHavez', 'Renz Godinez', 'Janno Crisostomo', 'Gabriel Ceniza', 'Nathaniel Kiskisan'],
            '403': ['Joshua Baguio', 'Jincent Caritan', 'Fierce Vladimir Tio', 'Deniel Mendoza', 'Angelito Milabo'],
            '404': ['Aioie Cadorna', 'Eduard John Sarmiento', 'Angelo Parocho', 'Alfe Pagunsan', 'Josh Harvie Calub', 'Jun Clark Catibud'],
            '405': ['Jhon Paul Casaldan', 'Ricky Casas', 'Radel Agsalud', 'Freddie Novicio', 'Norkent Ricacho', 'Cyrelle Mascarinas']
          }
        },
        5: {
          rooms: ['502', '504', '505'],
          students: {
            '502' : ['Hazel Mae Fernandez', 'Joan Canillo', 'Niña Kathleen Barro', 'Justine Mae Belia', 'Kristel Jean Judico', 'Gina Gamboa'],
            '504' : ['Judy Mae Torechilla', 'Jela Mae Gesim', 'Aiza Villadolid', 'Mary Gwen Sacnasas', 'Joanne Joy Lagaras', 'Anna Rhea Villadolid'],
            '505' : ['Monique Cantarona', 'Danica Gelbolingo', 'Kristel Veligano', 'Angel Marie Soco', 'Michelle Anne Villarosa', 'Ereca Joy Eria'],
          }
        },
        6: {
          rooms: ['601', '602', '603', '604', '605'],
          students: {
            '601' : ['Reynelyn Cañares', 'Luane Marie Edaño', 'Jassy Faburada', 'Angela Lourene Bugais', 'Mariel Silorio', 'Neziel Jean Rellita'],
            '602' : ['Angel Aliviado', 'Cleofe Mae Villegas', 'Cherry Ann Sigbat', 'Mariel Ann Bawic', 'Athena Grazia Gumanoy', 'Alyssa Joyce Lumayaga'],
            '603' : ['April Jane Mangyao', 'Nadezhda Jade Yncierto', 'Precious Dignos', 'Angelica Panangganan', 'Elgine Monion', 'Chris Jane Diaz'],
            '604' : ['Cheed Loraine Veligaño', 'Vina Aduana', 'Cherry Rose Tenepre', 'Renalyn Bontilao', 'Aisa De Los Santos', 'Leslie Marie Lumapac'],
            '605' : ['Bianca Duhaylungsod', 'Wendolyn Dante', 'Jossel De Los Santos', 'Sheila Belarmino', 'Ruvy Ann Lacaba','Charlene Cuenca'],
          }
        },
        7: {
          rooms: ['701', '702', '703', '704', '705'],
          students: {
            '701': ['Marie Dasian', 'Jane Grace Bautista', 'Fiona Samantha Escarro', 'Rosana Jane Wandasan', 'Lorie  Catamisan', 'Jessa Mae Bugal', 'Merrydel Sombrio'],
            '702': ['Alyssa Faith Rodrigo', 'Eupe Estelloro', 'Genelyn Legañia', 'Mae Matanog', 'Janice Casagan', 'Angela Mae Villaester'],
            '703': ['Geralyn Monares', 'Ashley Nicole Oco', 'Valerie Ysulan', 'Glaiza Bejec', 'Rhea Joy Magallen', 'Jenvier Montaño'],
            '704': ['Jane Kyla Ruben', 'Rolynne Decadiz', 'Sofia Nicole Moreno', 'Dona Mae Negrido', 'Ivy Dela Cerna', 'Gerlie Ann Daga-as'],
            '705': ['Sarah Mae Jomuad', 'Gwyn Apawan', 'Gee Ann Pulod', 'Jessa Mae Vergara', 'Elsa Mae Legista', 'Lotchene Balcorza']
          }
        }
      };

      // Handle floor button clicks
      document.querySelectorAll('.floor-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          const floor = this.dataset.floor;
          roomsContainer.classList.remove('hidden');
          roomsContainer.innerHTML = '';

          if (floorData[floor]) {
            const roomsWrapper = document.createElement('div');
            roomsWrapper.className = 'rooms-wrapper';
            roomsWrapper.style.display = 'grid';
            roomsWrapper.style.gridTemplateColumns = 'repeat(auto-fill, minmax(300px, 1fr))';
            roomsWrapper.style.gap = '20px';

            floorData[floor].rooms.forEach(roomNumber => {
              const roomBox = createRoomBox(roomNumber, floor);
              roomsWrapper.appendChild(roomBox);
            });

            // Add Room button
            const addRoomBtn = document.createElement('button');
            addRoomBtn.className = 'add-room-btn';
            addRoomBtn.innerHTML = `
              <img src="{{ asset('images/add-icon.png') }}" alt="Add" class="icon">
              Add Room
            `;
            addRoomBtn.onclick = openNewRoomModal;
            roomsWrapper.appendChild(addRoomBtn);

            roomsContainer.appendChild(roomsWrapper);
            setupRoomEventListeners();
          }
        });
      });

      function createRoomBox(roomNumber, floor) {
        const roomBox = document.createElement('div');
        roomBox.className = 'room-box';
        roomBox.setAttribute('data-room-number', roomNumber);
        roomBox.innerHTML = `
          <h3>Room ${roomNumber}</h3>
          <div class="student-occupants">
            <h4>
              Student Occupants
              <div class="action-buttons">
                <button class="edit-btn">
                  <img src="{{ asset('images/edit-icon.png') }}" alt="Edit" class="icon">
                </button>
                <button class="delete-btn">
                  <img src="{{ asset('images/delete-icon.png') }}" alt="Delete" class="icon">
                </button>
              </div>
            </h4>
            <ul class="occupants-list">
              ${floorData[floor].students[roomNumber].map(student => 
                `<li>${student}</li>`
              ).join('')}
            </ul>
          </div>
          <div class="room-footer">
            <button class="add-student-btn">
              <img src="{{ asset('images/add-icon.png') }}" alt="Add" class="icon">
              Add Student
            </button>
            <a href="/roomtask/${roomNumber}" class="continue-btn">Checklist</a>
          </div>
        `;
        return roomBox;
      }

      function setupRoomEventListeners() {
        // Add Student button
        document.querySelectorAll('.add-student-btn').forEach(btn => {
          btn.addEventListener('click', function(e) {
            e.preventDefault();
            const roomBox = this.closest('.room-box');
            const roomNumber = roomBox.getAttribute('data-room-number');
            openModal(roomNumber, 'add');
          });
        });

        // Edit Student button
        document.querySelectorAll('.edit-btn').forEach(btn => {
          btn.addEventListener('click', function(e) {
            e.preventDefault();
            const roomBox = this.closest('.room-box');
            const roomNumber = roomBox.getAttribute('data-room-number');
            openModal(roomNumber, 'edit');
          });
        });

        // Delete Student button
        document.querySelectorAll('.delete-btn').forEach(btn => {
          btn.addEventListener('click', function(e) {
            e.preventDefault();
            const roomBox = this.closest('.room-box');
            const roomNumber = roomBox.getAttribute('data-room-number');
            openModal(roomNumber, 'delete');
          });
        });

        // Add Room button
        const addRoomBtn = document.querySelector('.add-room-btn');
        if (addRoomBtn) {
          addRoomBtn.addEventListener('click', openNewRoomModal);
        }
      }

      function openModal(roomNumber, action) {
        const modal = document.getElementById('studentModal');
        const modalTitle = document.getElementById('modalTitle');
        const roomNumberInput = document.getElementById('roomNumber');
        const actionTypeInput = document.getElementById('actionType');
        const studentNameInput = document.getElementById('studentName');
        const existingStudentSelect = document.getElementById('existingStudent');
        const editStudentSelectDiv = document.getElementById('editStudentSelect');

        modalTitle.textContent = action === 'add' ? 'Add Student' :
                               action === 'edit' ? 'Edit Student' : 'Delete Student';
        actionTypeInput.value = action;
        roomNumberInput.value = roomNumber;

        if (action === 'add') {
          editStudentSelectDiv.style.display = 'none';
          studentNameInput.parentElement.style.display = 'block';
          studentNameInput.required = true;
        } else {
          editStudentSelectDiv.style.display = 'block';
          populateStudentSelect(roomNumber);
          if (action === 'edit') {
            studentNameInput.parentElement.style.display = 'block';
            studentNameInput.required = true;
          } else {
            studentNameInput.parentElement.style.display = 'none';
            studentNameInput.required = false;
          }
        }

        modal.style.display = 'block';
      }

      function openNewRoomModal() {
        newRoomModal.style.display = 'block';
      }

      function closeModal(modal) {
        modal.style.display = 'none';
        if (modal === studentModal) {
          document.getElementById('studentForm').reset();
        } else {
          document.getElementById('newRoomForm').reset();
        }
      }

      // Close button handlers
      document.querySelectorAll('.close, .cancel-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          const modal = this.closest('.modal');
          closeModal(modal);
        });
      });

      // Close modal when clicking outside
      window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
          closeModal(event.target);
        }
      });

      function populateStudentSelect(roomNumber) {
        const select = document.getElementById('existingStudent');
        select.innerHTML = '<option value="">-- Select a student --</option>';
        
        const roomBox = document.querySelector(`[data-room-number="${roomNumber}"]`);
        if (roomBox) {
          const students = Array.from(roomBox.querySelectorAll('.occupants-list li'))
            .map(li => li.textContent.trim());
          
          students.forEach(student => {
            const option = document.createElement('option');
            option.value = student;
            option.textContent = student;
            select.appendChild(option);
          });
        }
      }

      // Form submission handlers
      document.getElementById('studentForm').addEventListener('submit', handleStudentFormSubmit);
      document.getElementById('newRoomForm').addEventListener('submit', handleNewRoomFormSubmit);

      function handleStudentFormSubmit(event) {
        event.preventDefault();
        const form = event.target;
        const roomNumber = form.roomNumber.value;
        const action = form.actionType.value;
        const roomBox = document.querySelector(`[data-room-number="${roomNumber}"]`);
        const occupantsList = roomBox.querySelector('.occupants-list');

        if (action === 'add') {
          const studentName = form.studentName.value;
          if (!studentName) {
            alert('Please enter a student name.');
            return;
          }

          const li = document.createElement('li');
          li.textContent = studentName;
          occupantsList.appendChild(li);
        } else if (action === 'edit') {
          const oldName = form.existingStudent.value;
          const newName = form.studentName.value;

          if (!oldName || !newName) {
            alert('Please select a student and enter a new name.');
            return;
          }

          const studentToUpdate = Array.from(occupantsList.children).find(
            li => li.textContent.trim() === oldName
          );
          if (studentToUpdate) {
            studentToUpdate.textContent = newName;
          }
        } else if (action === 'delete') {
          const studentName = form.existingStudent.value;
          if (!studentName) {
            alert('Please select a student to delete.');
            return;
          }

          if (!confirm(`Are you sure you want to delete ${studentName}?`)) {
            return;
          }

          const studentToRemove = Array.from(occupantsList.children).find(
            li => li.textContent.trim() === studentName
          );
          if (studentToRemove) {
            studentToRemove.remove();
          }
        }

        closeModal(studentModal);
      }

      function handleNewRoomFormSubmit(event) {
        event.preventDefault();
        const roomNumber = document.getElementById('newRoomNumber').value;
        const floor = Math.floor(roomNumber / 100);

        if (!roomNumber) {
          alert('Please enter a room number.');
          return;
        }

        const existingRoom = document.querySelector(`[data-room-number="${roomNumber}"]`);
        if (existingRoom) {
          alert('This room already exists.');
          return;
        }

        // Add the new room to the floorData
        if (floorData[floor]) {
          floorData[floor].rooms.push(roomNumber);
          floorData[floor].students[roomNumber] = [];
          
          // Trigger click on the floor button to refresh the display
          document.querySelector(`[data-floor="${floor}"]`).click();
        }

        closeModal(newRoomModal);
      }
    });
  </script>
</body>
</html>