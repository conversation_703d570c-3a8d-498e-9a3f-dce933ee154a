
.page-container {
    padding: 20px;
    max-width: 100%;
}

.header-section {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
}

.header-section h2 {
    font-size: 24px;
    color: #333;
    margin: 0;
}

.form-container {
    background: white;
    padding: 24px;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 500;
}

.form-select,
.form-group input[type="text"] {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.students-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px;
    background: #f8f9fa;
}

.student-checkbox {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 4px;
}

.student-checkbox:hover {
    background: #e9ecef;
    border-radius: 4px;
}

.student-checkbox input[type="checkbox"] {
    margin-right: 8px;
}

.student-checkbox label {
    margin-bottom: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.batch-tag {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.85em;
    color: #495057;
}

.filter-section {
    margin-bottom: 12px;
}

.filter-section select {
    width: 200px;
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    font-size: 14px;
}


.form-select:focus,
.form-group input[type="text"]:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.students-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 12px;
}

.student-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #eee;
}

.student-item:last-child {
    border-bottom: none;
}

.student-item label {
    margin: 0;
    padding-left: 8px;
}

.form-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;
}

.btn-submit {
    background: #4CAF50;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-cancel {
    background: #dc3545;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    text-decoration: none;
}

.error-message {
    color: #dc3545;
    font-size: 12px;
    margin-top: 4px;
}

/* Student Conflict Alert Styles */
.student-conflicts-alert {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-left: 4px solid #dc3545;
    border-radius: 8px;
    padding: 0;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.1);
}

.alert-header {
    background: #dc3545;
    color: white;
    padding: 12px 16px;
    border-radius: 4px 4px 0 0;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.alert-header i {
    font-size: 16px;
}

.alert-body {
    padding: 16px;
}

.alert-body p {
    margin: 0 0 12px 0;
    color: #721c24;
    font-weight: 500;
}

.conflict-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.conflict-list li {
    background: white;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 10px 12px;
    margin-bottom: 8px;
    color: #721c24;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.conflict-list li:last-child {
    margin-bottom: 0;
}

.conflict-list li i {
    color: #dc3545;
    font-size: 14px;
    flex-shrink: 0;
}

.alert-footer {
    background: #f1f3f4;
    padding: 10px 16px;
    border-radius: 0 0 4px 4px;
    border-top: 1px solid #f5c6cb;
}

.alert-footer small {
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    line-height: 1.4;
}

.alert-footer i {
    color: #17a2b8;
    font-size: 12px;
    flex-shrink: 0;
}
