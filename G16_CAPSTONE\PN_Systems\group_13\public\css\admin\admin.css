* {
    font-family: 'Poppins', sans-serif !important;
}

/* Preserve icon fonts */
.fas, .far, .fal, .fab, .fa,
[class*="fa-"],
.material-icons,
.glyphicon {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 5 Free", "Font Awesome 5 Pro", "Material Icons", "Glyphicons Halflings" !important;
}

body {
    margin: 0;
    font-family: 'Poppins', sans-serif !important;
    background-color: #f8f9fa;
    width: 100%;
}

header {
    padding: 10px 20px;
    background-color: #22bbea;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000; /* Ensures the header stays above other elements */
    margin-bottom: 50px;
}

.nav {
    margin: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo img {
    width: 150px; 
}

.nav-links svg {
    cursor: pointer;
    width: 30px;
    height: 30px;
    color: #333;
    margin-left: -70px;
}

.sidebar {
    position: fixed;
    left: 0;
    width: 170px;
    height: calc(100vh - 60px); /* Sidebar height minus the header height */
    background-color: #fff;
    border-right: 2px solid #ccc;
    padding: 20px;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: left;
}

.sidebar a{
    text-decoration: none;
}

.nav-name {
    display: flex;
    align-items: center;
    margin-top: 50px;
}

.nav-name:hover{
    border: 1px solid #22bbea;
    cursor: pointer;
    padding: 0;
    filter: drop-shadow(0 0 0.75rem #ff9933);
}

.logo svg {
    margin-right: 10px;
}

.nav-name p {
    font-size: 16px;
    font-weight: 500;
    color: #333;

}

.content {
    margin-left: 220px; /* Adjust to account for the sidebar width */
    padding: 20px;
    margin-top: 60px; /* Adjust to account for the header height */
}





/*user list*/

.icon-back{
    margin-top: 60px;
}


/*manage users css*/

/* Table Styling */
.users-container {
    margin-top: 20px;
    overflow-x: auto; /* Ensure the table is scrollable on smaller screens */
}

.users-container table {
    width: 100%; /* Make the table take up the full width of its container */
    border-collapse: collapse; /* Remove gaps between table cells */
    font-size: 16px;
    text-align: left;
    background-color: #fff;
    border: 1px solid #ccc; /* Add a border around the table */
}

.users-container th, .users-container td {
    padding: 12px 15px; /* Add padding to table cells */
    border: 1px solid #ddd; /* Add a border between rows and columns */
}

.users-container th {
    background-color: #f4f4f4; /* Light gray background for table headers */
    font-weight: bold;
    color: #333;
}

.users-container tr:nth-child(even) {
    background-color: #f9f9f9; /* Light gray for even rows */
}

.users-container tr:nth-child(odd) {
    background-color: #fff; /* White for odd rows */
}

.users-container tr:hover {
    background-color: #eaeaea; /* Highlight row on hover */
    cursor: pointer; /* Optional: Add a pointer cursor on hover */
}


/* Button Styling */





/*MANAGE USERS CSS*/

.users-container {
    margin-top: 3%;
    overflow-x: auto; /* Ensure the table is scrollable on smaller screens */
}

.users-container h1{
    font-weight: 400;
}

/* Add User Button */
.add-user-btn {
    background-color: #ff9933;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    margin-bottom: 20px;
    
}

.add-user-btn:hover {
    background-color: #ffc107;
}

.add-user-btn a{
    text-decoration: none;
    text-align: center;
    color: black;
}



/* Table Styling */
.users-table-container {
    overflow-x: auto; /* Enable horizontal scrolling for smaller screens */
}

.table {
    width: 100%;
    border-collapse: collapse;
    font-size: 16px;
    text-align: center;

    
}

.table th, .table td {
    padding: 12px 15px;
    border: 1px solid #ddd;
}

.table th {
    background-color: #f4f4f4;
    font-weight: 300;
}

.table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.table tr:nth-child(odd) {
    background-color: #fff;
}

.table tr:hover {
    background-color: #eaeaea;
}

/* Action Buttons */


.edit-btn {
    background-color: #ffc107;
    color: white;
}

.edit-btn a{
    text-decoration: none;
}

.edit-btn:hover {
    background-color: #e0a800;
}

.delete-btn {
    background-color: #dc3545;
    color: white;
}

.delete-btn:hover {
    background-color: #c82333;
}




/* Create User Form */
.create-user-container {
    max-width: 70%; /* Adjust the width to fit smaller screens */
    margin: 0 auto; /* Center the form horizontally */
    margin-top: 5%; /* Reduce spacing from the top */
 

}

.create-user-container h1 {
    font-size: 20px; /* Reduce font size for the heading */
    font-weight: 500;
    margin-bottom: 15px; /* Reduce spacing below the heading */
}

.create-user-container form {
    background-color: #ffffff; /* White background for the form */
    padding: 15px; /* Reduce padding for smaller screens */
    border-radius: 8px; /* Slightly smaller border radius */
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1); /* Subtle shadow for the form */
    width:  50%;
    margin: 0 auto; /* Center the form */
}

.create-user-container form .form-group {
    margin-bottom: 10px; /* Reduce spacing between form fields */
}

.create-user-container form .form-label {
    font-weight: 400; /* Use lighter font weight */
    color: #333;
    display: block; /* Ensure the label is above the input */
    margin-bottom: 3px; /* Reduce spacing below the label */
    text-align: left; /* Align labels to the left */
    font-size: 14px; /* Reduce font size for labels */
}

.create-user-container form .form-control {
    width: 100%; /* Make input fields take up the full width */
    border: 1px solid #ccc;
    border-radius: 4px; /* Slightly smaller border radius */
    padding: 8px; /* Reduce padding for input fields */
    font-size: 13px; /* Reduce font size for input text */
    box-sizing: border-box; /* Include padding in width calculation */
}

.create-user-container form .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 3px rgba(0, 123, 255, 0.5); /* Subtle focus shadow */
    outline: none; /* Remove default outline */
}

.create-user-container form button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 12px; /* Reduce padding for the button */
    border-radius: 4px; /* Slightly smaller border radius */
    font-size: 14px; /* Reduce font size for the button */
    cursor: pointer;
    width: 100%; /* Make the button span the full width */
}

.create-user-container form button:hover {
    background-color: #0056b3;
}
