<x-studentLayout>
    <div class="px-4 py-6 full-width-container">
        <div class="p-8 bg-white border border-gray-200 shadow-xl rounded-1xl">

            {{-- Filters --}}
            <div class="p-6 mb-8 bg-white shadow-md rounded-2xl">
                <div class="flex justify-end mb-4">
                    <a href="{{ route('educator.dashboard') }}" class="inline-flex items-center text-sm font-medium text-blue-600 hover:underline">
                        <i data-feather="arrow-left" class="w-5 h-5 mr-1"></i> Back to Dashboard
                    </a>
                </div>
                <div class="flex flex-wrap items-end gap-4">
                    <div class="w-48">
                        <label for="monthFilter" class="block mb-2 text-sm font-bold text-orange-700">Month</label>
                        <select id="monthFilter" class="w-full py-2 text-sm bg-gray-100 border-gray-300 rounded-sm shadow-sm focus:border-orange-500 focus:ring-orange-500">
                            @foreach(range(1, 12) as $m)
                                <option value="{{ $m }}" {{ $m == now()->format('m') ? 'selected' : '' }}>
                                    {{ date('F', mktime(0, 0, 0, $m, 1)) }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="w-48">
                        <label for="yearFilter" class="block mb-2 text-sm font-bold text-orange-700">Year</label>
                        <select id="yearFilter" class="w-full py-2 text-sm bg-gray-100 border-gray-300 shadow-sm rounded-sm focus:border-orange-500 focus:ring-orange-500">
                            @foreach(range(now()->year - 2, now()->year) as $y)
                                <option value="{{ $y }}" {{ $y == now()->year ? 'selected' : '' }}>{{ $y }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="w-48">
                        <label for="batchFilter" class="block mb-2 text-sm font-bold text-orange-700">Batch</label>
                        <select id="batchFilter" class="w-full py-2 text-sm bg-gray-100 border-gray-300 rounded-sm shadow-sm focus:border-orange-500 focus:ring-orange-500">
                            <option value="">All Batches</option>
                        </select>
                    </div>
                    <button id="applyFilters" class="px-3 py-2 text-sm text-white transition-colors bg-orange-600 rounded-sm hover:bg-orange-700">
                        Apply Filters
                    </button>


                </div>
            </div>


            {{-- Rankings Table Container with Flip Functionality --}}
            <div class="relative p-6 bg-white shadow-md rounded-2xl" style="perspective: 1000px;">
                {{-- Flip Button --}}
                <button onclick="flipRankings()" class="absolute text-blue-600 transition-colors top-4 right-4 hover:text-blue-800 flip-button group">
                    <i data-feather="repeat" class="w-5 h-5"></i>
                    <div class="absolute right-0 z-50 invisible px-2 py-1 text-xs text-white bg-gray-900 rounded-md shadow-sm group-hover:visible -top-8 whitespace-nowrap">
                        Switch Ranking Type
                    </div>
                </button>

                {{-- Flip Container --}}
                <div id="rankingsFlipContainer" class="relative transition-transform duration-700" style="transform-style: preserve-3d;">

                    {{-- Late Students Ranking (Front) --}}
                    <div id="lateRankings" class="w-full backface-hidden">
                        <h3 class="mb-6 text-2xl font-semibold text-gray-800">Late Students Ranking</h3>

                        {{-- Table --}}
                        <div class="relative w-full overflow-hidden border border-gray-200 rounded-lg">
                            <div class="w-full overflow-x-auto table-container">
                                <div class="overflow-y-auto max-h-[calc(100vh-200px)] table-container">
                                    <table class="w-full min-w-full text-sm text-left text-gray-700">
                                        <thead class="text-xs font-semibold tracking-wider uppercase bg-gray-100">
                                            <tr>
                                                <th class="px-6 py-3 text-black">Rank</th>
                                                <th class="px-6 py-3 text-black">Name</th>
                                                <th class="px-6 py-3 text-black">Batch</th>
                                                <th class="px-6 py-3 text-black">Group</th>
                                                <th class="px-6 py-3 text-black">Log out Late</th>
                                                <th class="px-6 py-3 text-black">Number of Excused</th>
                                                <th class="px-6 py-3 text-black">Log in Late</th>
                                                <th class="px-6 py-3 text-black">Number of Excused</th>
                                                <th class="px-6 py-3 text-black">Total Late</th>
                                                <th class="px-6 py-3 text-black">Total Excused</th>
                                                <th class="px-6 py-3 text-black">Status</th>
                                            </tr>
                                        </thead>
                                        <tbody id="lateStudentsTable" class="divide-y divide-gray-200">
                                            <!-- Data will be populated here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    {{-- Absent Students Ranking (Back) --}}
                    <div id="absentRankings" class="absolute top-0 left-0 w-full backface-hidden rotate-y-180">
                        <h3 class="mb-6 text-2xl font-semibold text-gray-800">Absent Students Ranking</h3>

                        {{-- Table --}}
                        <div class="relative w-full overflow-hidden border border-gray-200 rounded-lg">
                            <div class="w-full overflow-x-auto table-container">
                                <div class="overflow-y-auto max-h-[calc(100vh-200px)] table-container">
                                    <table class="w-full min-w-full text-sm text-left text-gray-700">
                                        <thead class="text-xs font-semibold tracking-wider uppercase bg-gray-100">
                                            <tr>
                                                <th class="px-6 py-3 text-black">Rank</th>
                                                <th class="px-6 py-3 text-black">Name</th>
                                                <th class="px-6 py-3 text-black">Batch</th>
                                                <th class="px-6 py-3 text-black">Group</th>
                                                <th class="px-6 py-3 text-black">Academic Absent</th>
                                                <th class="px-6 py-3 text-black">Status</th>
                                            </tr>
                                        </thead>
                                        <tbody id="absentStudentsTable" class="divide-y divide-gray-200">
                                            <!-- Data will be populated here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- History Modal (for both Late and Absent) --}}
    <div id="historyModal" class="fixed inset-0 hidden w-full h-full overflow-y-auto bg-gray-600 bg-opacity-50" style="z-index: 50;">
        <div class="relative w-11/12 p-5 mx-auto bg-white border rounded-md shadow-lg top-20 md:w-3/4 lg:w-1/2">
            <div class="flex items-center justify-between pb-3">
                <h3 class="text-2xl font-semibold text-orange-700" id="modalStudentName"></h3>
                <button onclick="closeHistoryModal()" class="text-gray-400 hover:text-gray-500">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="mt-4">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead id="historyTableHead" class="bg-gray-50">
                            <!-- Table headers will be populated dynamically -->
                        </thead>
                        <tbody id="historyTableBody" class="bg-white divide-y divide-gray-200">
                            <!-- History data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        let currentData = null;
        let currentAbsentData = null;
        let isFlipped = false;

        function flipRankings() {
            const container = document.getElementById('rankingsFlipContainer');

            isFlipped = !isFlipped;
            container.style.transform = isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)';

            // Load appropriate data based on flip state
            if (isFlipped) {
                loadAbsentData();
            } else {
                loadLateData();
            }
        }

        function showHistory(studentId, studentName, type = 'late') {
            const modal = document.getElementById('historyModal');
            const modalStudentName = document.getElementById('modalStudentName');
            const tableHead = document.getElementById('historyTableHead');
            const tableBody = document.getElementById('historyTableBody');

            modalStudentName.textContent = studentName;

            // Set up table headers based on type
            if (type === 'absent') {
                tableHead.innerHTML = `
                    <tr>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Date</th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Type of Logs</th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Consideration</th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Validation</th>
                    </tr>
                `;
                tableBody.innerHTML = '<tr><td colspan="4" class="px-6 py-4 text-center">Loading...</td></tr>';
            } else {
                tableHead.innerHTML = `
                    <tr>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Date</th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Log In/Out</th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Type of Logs</th>
                        <th class="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Consideration</th>
                    </tr>
                `;
                tableBody.innerHTML = '<tr><td colspan="4" class="px-6 py-4 text-center">Loading...</td></tr>';
            }

            modal.classList.remove('hidden');

            // Get the current filter values
            const month = document.getElementById('monthFilter').value;
            const year = document.getElementById('yearFilter').value;

            console.log(`Fetching ${type} history for:`, {
                studentId,
                month,
                year
            });

            // Determine endpoint based on type
            const endpoint = type === 'absent'
                ? `/educator/student-absent-history?student_id=${encodeURIComponent(studentId)}&month=${month}&year=${year}`
                : `/educator/student-late-history?student_id=${encodeURIComponent(studentId)}&month=${month}&year=${year}`;

            // Fetch history by student_id
            fetch(endpoint)
                .then(response => {
                    console.log('Response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Received data:', data);

                    if (!data.success) {
                        throw new Error(data.message || `Failed to fetch ${type} history`);
                    }

                    const historyKey = type === 'absent' ? 'absentHistory' : 'lateHistory';
                    if (!data.data || !data.data[historyKey]) {
                        throw new Error('Invalid data format received');
                    }

                    if (data.data[historyKey].length === 0) {
                        const colspanCount = 4; // Both absent and late now have 4 columns
                        tableBody.innerHTML = `
                            <tr>
                                <td colspan="${colspanCount}" class="px-6 py-4 text-center text-gray-500">
                                    <div class="flex flex-col items-center justify-center">
                                        <svg class="w-12 h-12 mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                        <p class="text-sm font-medium">No ${type} history found for this period</p>
                                        <p class="mt-1 text-xs text-gray-500">Try selecting a different month or year</p>
                                    </div>
                                </td>
                            </tr>
                        `;
                        return;
                    }

                    tableBody.innerHTML = '';
                    data.data[historyKey].forEach(record => {
                        console.log('Processing record:', record);
                        const row = document.createElement('tr');
                        const date = new Date(record.academic_date).toLocaleDateString();

                        // Determine consideration status and styling
                        let statusClass = 'text-gray-800 bg-gray-100';
                        let statusText = 'No Consideration';

                        if (record.consideration) {
                            if (record.consideration === 'Excused') {
                                statusClass = 'text-green-800 bg-green-100';
                                statusText = 'Excused';
                            } else if (record.consideration === 'Not Excused') {
                                statusClass = 'text-red-800 bg-red-100';
                                statusText = 'Not Excused';
                            } else if (record.consideration === 'Absent' || record.consideration === 'Not going out') {
                                statusClass = 'text-gray-800 bg-gray-100';
                                statusText = record.consideration;
                            } else {
                                statusText = record.consideration;
                            }
                        }

                        // Determine log type badge styling
                        const typeKey = type === 'absent' ? 'absent_type' : 'late_type';
                        const typeBadgeClass = record[typeKey] === 'going_out' ? 'bg-orange-100 text-orange-800' : 'bg-blue-100 text-blue-800';
                        const typeText = record[typeKey] === 'going_out' ? 'Going Out' : 'Academic';

                        if (type === 'absent') {
                            // For absent history, show validation status instead of logging process
                            let validationClass = 'text-gray-800 bg-gray-100';
                            let validationText = 'Not Validated';

                            if (record.validation) {
                                if (record.validation === 'valid') {
                                    validationClass = 'text-green-800 bg-green-100';
                                    validationText = 'Valid Absence';
                                } else if (record.validation === 'not_valid') {
                                    validationClass = 'text-red-800 bg-red-100';
                                    validationText = 'Invalid Absence';
                                }
                            }

                            row.innerHTML = `
                                <td class="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">${date}</td>
                                <td class="px-6 py-4 text-sm whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-semibold ${typeBadgeClass} rounded-full">
                                        ${typeText}
                                    </span>
                                </td>
                                <td class="px-6 py-4 text-sm whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-semibold ${statusClass} rounded-full">
                                        ${statusText}
                                    </span>
                                </td>
                                <td class="px-6 py-4 text-sm whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-semibold ${validationClass} rounded-full">
                                        ${validationText}
                                    </span>
                                </td>
                            `;
                        } else {
                            // For late history, show all columns including logging process
                            const processBadgeClass = record.logging_process === 'Log Out' || record.logging_process === 'Time Out'
                                ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800';
                            const processText = record.logging_process;

                            row.innerHTML = `
                                <td class="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">${date}</td>
                                <td class="px-6 py-4 text-sm whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-semibold ${processBadgeClass} rounded-full">
                                        ${processText}
                                    </span>
                                </td>
                                <td class="px-6 py-4 text-sm whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-semibold ${typeBadgeClass} rounded-full">
                                        ${typeText}
                                    </span>
                                </td>
                                <td class="px-6 py-4 text-sm whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-semibold ${statusClass} rounded-full">
                                        ${statusText}
                                    </span>
                                </td>
                            `;
                        }
                        tableBody.appendChild(row);
                    });
                })
                .catch(error => {
                    console.error(`Error in ${type} history:`, error);
                    const colspanCount = 4; // Both absent and late now have 4 columns
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="${colspanCount}" class="px-6 py-4 text-center">
                                <div class="flex flex-col items-center justify-center text-red-500">
                                    <svg class="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                    <p class="mb-1 font-semibold">Error loading ${type} history</p>
                                    <p class="text-sm text-gray-600">${error.message}</p>
                                    <button onclick="showHistory('${studentId}', '${studentName}', '${type}')" class="px-4 py-2 mt-4 text-white transition-colors bg-orange-600 rounded hover:bg-orange-700">
                                        Try Again
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                });
        }

        // Backward compatibility functions
        function showLateHistory(studentId, studentName) {
            showHistory(studentId, studentName, 'late');
        }

        function showAbsentHistory(studentId, studentName) {
            showHistory(studentId, studentName, 'absent');
        }

        function closeHistoryModal() {
            document.getElementById('historyModal').classList.add('hidden');
        }

        function updateLateTable(data) {
            const tableBody = document.getElementById('lateStudentsTable');
            tableBody.innerHTML = '';

            // Check if data exists and has lateStudents
            if (!data || !data.lateStudents || data.lateStudents.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="11" class="px-6 py-4 text-center text-gray-500">
                            <div class="flex flex-col items-center justify-center">
                                <svg class="w-12 h-12 mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <p class="text-sm font-medium">No late students found for the selected period</p>
                                <p class="mt-1 text-xs text-gray-500">Try selecting a different month or year</p>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            // Sort students by total late count in descending order
            const sortedStudents = [...data.lateStudents].sort((a, b) => b.total_late_count - a.total_late_count);

            sortedStudents.forEach((student, index) => {
                const row = document.createElement('tr');
                row.className = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';

                // Determine status based on total late count
                let statusClass, statusText;
                if (student.total_late_count >= 5) {
                    row.classList.add('bg-red-100');
                    statusClass = 'text-red-800 bg-red-100';
                    statusText = 'Critical';
                } else if (student.total_late_count >= 3) {
                    row.classList.add('bg-orange-50');
                    statusClass = 'text-orange-800 bg-orange-100';
                    statusText = 'Warning';
                } else {
                    statusClass = 'text-green-800 bg-green-100';
                    statusText = 'Normal';
                }

                row.innerHTML = `
                    <td class="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">${index + 1}</td>
                    <td class="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">
                        <a href="#" onclick="showLateHistory('${student.student_id}', '${student.first_name} ${student.last_name}')" class="text-blue-600 hover:text-blue-800">
                            ${student.first_name} ${student.last_name}
                        </a>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">${student.batch || '-'}</td>
                    <td class="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">${student.group || '-'}</td>
                    <td class="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">${student.logout_late_count || 0}</td>
                    <td class="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">${student.logout_excused_count || 0}</td>
                    <td class="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">${student.login_late_count || 0}</td>
                    <td class="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">${student.login_excused_count || 0}</td>
                    <td class="px-6 py-4 text-sm text-red-600 whitespace-nowrap font-semibold">${student.total_late_count || 0}</td>
                    <td class="px-6 py-4 text-sm text-gray-900 whitespace-nowrap font-semibold">${student.total_excused_count || 0}</td>
                    <td class="px-6 py-4 text-sm whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-semibold ${statusClass} rounded-full">${statusText}</span>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        function updateAbsentTable(data) {
            const tableBody = document.getElementById('absentStudentsTable');
            tableBody.innerHTML = '';

            // Check if data exists and has absentStudents
            if (!data || !data.absentStudents || data.absentStudents.length === 0) {
                tableBody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                            <div class="flex flex-col items-center justify-center">
                                <svg class="w-12 h-12 mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <p class="text-sm font-medium">No absent students found for the selected period</p>
                                <p class="mt-1 text-xs text-gray-500">Try selecting a different month or year</p>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            // Sort students by academic absent count in descending order (only academic absences)
            const sortedStudents = [...data.absentStudents].sort((a, b) => b.academic_absent_count - a.academic_absent_count);

            sortedStudents.forEach((student, index) => {
                const row = document.createElement('tr');
                row.className = index % 2 === 0 ? 'bg-white' : 'bg-gray-50';

                // Determine status based on academic absent count (same thresholds as late)
                let statusClass, statusText;
                if (student.academic_absent_count >= 5) {
                    row.classList.add('bg-red-100');
                    statusClass = 'text-red-800 bg-red-100';
                    statusText = 'Critical';
                } else if (student.academic_absent_count >= 3) {
                    row.classList.add('bg-orange-50');
                    statusClass = 'text-orange-800 bg-orange-100';
                    statusText = 'Warning';
                } else {
                    statusClass = 'text-green-800 bg-green-100';
                    statusText = 'Normal';
                }

                row.innerHTML = `
                    <td class="px-6 py-4 text-sm font-medium text-gray-900 whitespace-nowrap">${index + 1}</td>
                    <td class="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">
                        <a href="#" onclick="showAbsentHistory('${student.student_id}', '${student.first_name} ${student.last_name}')" class="text-blue-600 hover:text-blue-800">
                            ${student.first_name} ${student.last_name}
                        </a>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">${student.batch || '-'}</td>
                    <td class="px-6 py-4 text-sm text-gray-900 whitespace-nowrap">${student.group || '-'}</td>
                    <td class="px-6 py-4 text-sm text-red-600 whitespace-nowrap font-semibold">${student.academic_absent_count || 0}</td>
                    <td class="px-6 py-4 text-sm whitespace-nowrap">
                        <span class="px-2 py-1 text-xs font-semibold ${statusClass} rounded-full">${statusText}</span>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        }

        function loadLateData() {
            const month = document.getElementById('monthFilter').value;
            const year = document.getElementById('yearFilter').value;
            const batch = document.getElementById('batchFilter').value;

            // Show loading state
            const tableBody = document.getElementById('lateStudentsTable');
            tableBody.innerHTML = `
                <tr>
                    <td colspan="11" class="px-6 py-4 text-center">
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-3 text-orange-600 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Loading late data...
                        </div>
                    </td>
                </tr>
            `;

            fetch(`/educator/late-analytics/data?month=${month}&year=${year}&batch=${batch}`)
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(err => {
                            throw new Error(err.message || 'Network response was not ok');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Raw late data received:', data);

                    if (!data) {
                        throw new Error('No data received from server');
                    }

                    // Ensure data.lateStudents is an array
                    if (!Array.isArray(data.lateStudents)) {
                        if (typeof data.lateStudents === 'object' && data.lateStudents !== null) {
                            data.lateStudents = Object.values(data.lateStudents);
                        } else {
                            data.lateStudents = [];
                        }
                    }

                    currentData = data;
                    updateBatchFilter(data);
                    updateLateTable(data);
                })
                .catch(error => {
                    console.error('Error loading late data:', error);
                    const tableBody = document.getElementById('lateStudentsTable');
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="11" class="px-6 py-4 text-center">
                                <div class="flex flex-col items-center justify-center text-red-500">
                                    <svg class="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                    <p class="mb-1 font-semibold">Error loading late data</p>
                                    <p class="text-sm text-gray-600">${error.message}</p>
                                    <button onclick="loadLateData()" class="px-4 py-2 mt-4 text-white transition-colors bg-orange-600 rounded hover:bg-orange-700">
                                        Try Again
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                });
        }

        function loadAbsentData() {
            const month = document.getElementById('monthFilter').value;
            const year = document.getElementById('yearFilter').value;
            const batch = document.getElementById('batchFilter').value;

            // Show loading state
            const tableBody = document.getElementById('absentStudentsTable');
            tableBody.innerHTML = `
                <tr>
                    <td colspan="11" class="px-6 py-4 text-center">
                        <div class="flex items-center justify-center">
                            <svg class="w-5 h-5 mr-3 text-orange-600 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Loading absent data...
                        </div>
                    </td>
                </tr>
            `;

            fetch(`/educator/absent-analytics/data?month=${month}&year=${year}&batch=${batch}`)
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(err => {
                            throw new Error(err.message || 'Network response was not ok');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Raw absent data received:', data);

                    if (!data) {
                        throw new Error('No data received from server');
                    }

                    // Ensure data.absentStudents is an array
                    if (!Array.isArray(data.absentStudents)) {
                        if (typeof data.absentStudents === 'object' && data.absentStudents !== null) {
                            data.absentStudents = Object.values(data.absentStudents);
                        } else {
                            data.absentStudents = [];
                        }
                    }

                    currentAbsentData = data;
                    updateBatchFilter(data);
                    updateAbsentTable(data);
                })
                .catch(error => {
                    console.error('Error loading absent data:', error);
                    const tableBody = document.getElementById('absentStudentsTable');
                    tableBody.innerHTML = `
                        <tr>
                            <td colspan="11" class="px-6 py-4 text-center">
                                <div class="flex flex-col items-center justify-center text-red-500">
                                    <svg class="w-12 h-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                    <p class="mb-1 font-semibold">Error loading absent data</p>
                                    <p class="text-sm text-gray-600">${error.message}</p>
                                    <button onclick="loadAbsentData()" class="px-4 py-2 mt-4 text-white transition-colors bg-orange-600 rounded hover:bg-orange-700">
                                        Try Again
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                });
        }

        function updateBatchFilter(data) {
            const batchFilter = document.getElementById('batchFilter');
            const currentValue = batchFilter.value;
            batchFilter.innerHTML = '<option value="">All Batches</option>';

            if (data.batches && Array.isArray(data.batches)) {
                data.batches.forEach(batch => {
                    const option = document.createElement('option');
                    option.value = batch;
                    option.textContent = `Batch ${batch}`;
                    if (batch === currentValue) {
                        option.selected = true;
                    }
                    batchFilter.appendChild(option);
                });
            }
        }

        function loadData() {
            if (isFlipped) {
                loadAbsentData();
            } else {
                loadLateData();
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Initial load
            loadData();

            // Apply filters button
            document.getElementById('applyFilters').addEventListener('click', loadData);
        });
    </script>
    @endpush

        </div>
    </div>

    {{-- Custom CSS for Full Width and Flip Functionality --}}
    <style>
        /* Override parent layout constraints for full width */
        .full-width-container {
            width: 100vw;
            max-width: none;
            margin-left: calc(-50vw + 50%);
            margin-right: calc(-50vw + 50%);
        }

        /* Ensure table cells don't wrap unnecessarily */
        .table-cell-nowrap {
            white-space: nowrap;
        }

        /* Flip functionality CSS */
        .backface-hidden {
            backface-visibility: hidden;
        }

        .rotate-y-180 {
            transform: rotateY(180deg);
        }

        /* Small light gray scrollbars for all elements */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 3px;
            transition: background 0.2s ease;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }

        ::-webkit-scrollbar-corner {
            background: #f8f9fa;
        }

        /* Firefox scrollbar styling */
        * {
            scrollbar-width: thin;
            scrollbar-color: #d1d5db #f8f9fa;
        }
    </style>

</x-studentLayout>
