<x-monitorLayout>
    <div class="w-full min-h-screen bg-gray-50">
        <!-- Header Section -->
        <div class="w-full bg-white border-b border-gray-200">
            <div class="w-full px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-4">
                        <a href="{{ route('monitor.dashboard') }}" 
                           class="flex items-center gap-2 px-4 py-2 text-blue-600 transition-all duration-200 bg-blue-50 border border-blue-200 rounded-lg hover:bg-blue-100 hover:border-blue-300">
                            <i data-feather="arrow-left" class="w-4 h-4"></i>
                            <span class="font-medium">Back to Dashboard</span>
                        </a>
                        <div class="w-px h-8 bg-gray-300"></div>
                        <h1 class="text-2xl font-bold text-orange-600">Individual Going Out Schedules</h1>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="w-full px-6 py-6">
            <!-- Search and Filter Section -->
            <div class="mb-6 bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6">
                    <div class="flex flex-col lg:flex-row gap-4">
                        <!-- Search Input -->
                        <div class="flex-1">
                            <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
                                <i data-feather="search" class="inline w-4 h-4 mr-1"></i>
                                Search by ID or Name
                            </label>
                            <input type="text"
                                   id="search-input"
                                   placeholder="Search by Student ID, First Name, or Last Name"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200">
                        </div>

                        <!-- Batch Filter -->
                        <div class="lg:w-48">
                            <label for="batch-filter" class="block text-sm font-medium text-gray-700 mb-2">
                                <i data-feather="users" class="inline w-4 h-4 mr-1"></i>
                                Batch
                            </label>
                            <select id="batch-filter"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200">
                                <option value="">All Batches</option>
                                @foreach($batches as $batch)
                                    <option value="{{ $batch }}">{{ $batch }}</option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Group Filter -->
                        <div class="lg:w-48">
                            <label for="group-filter" class="block text-sm font-medium text-gray-700 mb-2">
                                <i data-feather="tag" class="inline w-4 h-4 mr-1"></i>
                                Group
                            </label>
                            <select id="group-filter"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200">
                                <option value="">All Groups</option>
                                @foreach($groups as $group)
                                    <option value="{{ $group }}">{{ $group }}</option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Schedule Status Filter -->
                        <div class="lg:w-48">
                            <label for="schedule-filter" class="block text-sm font-medium text-gray-700 mb-2">
                                <i data-feather="calendar" class="inline w-4 h-4 mr-1"></i>
                                Schedule Status
                            </label>
                            <select id="schedule-filter"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-all duration-200">
                                <option value="">All Students</option>
                                <option value="with-schedule">With Schedule</option>
                                <option value="without-schedule">Without Schedule</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Students Selection Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-semibold text-gray-800">
                            <i data-feather="users" class="inline w-5 h-5 mr-2 text-orange-500"></i>
                            Select Students
                        </h2>
                        <div class="flex items-center gap-4">
                            <span class="text-sm text-gray-600">
                                Total: <span class="font-semibold">{{ $students->count() }}</span> students
                            </span>
                            <span class="text-sm text-gray-600">
                                Selected: <span id="selected-count" class="font-semibold text-orange-600">0</span>
                            </span>
                        </div>
                    </div>

                    @if($students->count() > 0)
                        <form id="schedule-form" method="POST" action="{{ route('monitor.individual-goingout.set-schedule') }}">
                            @csrf
                            
                            <!-- Selection Controls -->
                            <div class="mb-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center gap-4">
                                        <label class="flex items-center gap-2 cursor-pointer">
                                            <input type="checkbox" id="select-all" class="w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                            <span class="text-sm font-medium text-gray-700">Select All</span>
                                        </label>
                                    </div>
                                    <button type="button" 
                                            id="proceed-btn"
                                            disabled
                                            class="px-6 py-2 bg-orange-600 text-white font-medium rounded-lg hover:bg-orange-700 focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-all duration-200 disabled:bg-gray-300 disabled:cursor-not-allowed">
                                        <i data-feather="arrow-right" class="inline w-4 h-4 mr-1"></i>
                                        Proceed to Set Schedule
                                    </button>
                                </div>
                            </div>

                            <!-- Students Table -->
                            <div class="overflow-hidden border border-gray-200 rounded-lg">
                                <div class="overflow-x-auto">
                                    <table class="w-full text-sm text-left text-gray-700">
                                        <thead class="text-xs font-semibold tracking-wider uppercase bg-gray-100">
                                            <tr>
                                                <th class="px-6 py-3 text-black">Student ID</th>
                                                <th class="px-6 py-3 text-black">Name</th>
                                                <th class="px-6 py-3 text-black">Batch</th>
                                                <th class="px-6 py-3 text-black">Group</th>
                                                <th class="px-6 py-3 text-black text-center">Current Schedule</th>
                                                <th class="px-6 py-3 text-black text-center">Select</th>
                                            </tr>
                                        </thead>
                                        <tbody class="divide-y divide-gray-200">
                                            @foreach($students as $student)
                                                @php
                                                    $studentSchedules = $existingSchedules->get($student->student_id, collect());
                                                    $hasSchedule = $studentSchedules->isNotEmpty();
                                                @endphp
                                                <tr class="hover:bg-gray-50 transition-colors duration-150" data-has-schedule="{{ $hasSchedule ? 'true' : 'false' }}">
                                                    <td class="px-6 py-4 font-medium text-gray-900">
                                                        {{ $student->student_id }}
                                                    </td>
                                                    <td class="px-6 py-4">
                                                        {{ $student->user->user_fname }} {{ $student->user->user_lname }}
                                                    </td>
                                                    <td class="px-6 py-4">
                                                        <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                                            {{ $student->batch }}
                                                        </span>
                                                    </td>
                                                    <td class="px-6 py-4">
                                                        <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                                            {{ $student->group }}
                                                        </span>
                                                    </td>
                                                    <td class="px-6 py-4 text-center">
                                                        @if($hasSchedule)
                                                            <div class="flex flex-wrap gap-1 justify-center">
                                                                @foreach($studentSchedules as $schedule)
                                                                    <button type="button"
                                                                            class="schedule-day-btn px-2 py-1 text-xs font-medium bg-orange-100 text-orange-800 rounded-full hover:bg-orange-200 transition-colors cursor-pointer"
                                                                            data-student-id="{{ $student->student_id }}"
                                                                            data-day="{{ $schedule->day_of_week }}"
                                                                            data-time-out="{{ $schedule->time_out }}"
                                                                            data-time-in="{{ $schedule->time_in }}"
                                                                            data-valid-until="{{ $schedule->valid_until }}"
                                                                            title="Click to view schedule details">
                                                                        {{ substr($schedule->day_of_week, 0, 3) }}
                                                                    </button>
                                                                @endforeach
                                                            </div>
                                                        @else
                                                            <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-600 rounded-full">
                                                                No Schedule
                                                            </span>
                                                        @endif
                                                    </td>
                                                    <td class="px-6 py-4 text-center">
                                                        <label class="inline-flex items-center cursor-pointer">
                                                            <input type="checkbox"
                                                                   name="student_ids[]"
                                                                   value="{{ $student->student_id }}"
                                                                   class="student-checkbox w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                                        </label>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Day Selection Section (Initially Hidden) -->
                            <div id="day-selection-section" class="hidden mt-8 p-6 bg-orange-50 border border-orange-200 rounded-lg">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                                    <i data-feather="calendar" class="inline w-5 h-5 mr-2 text-orange-500"></i>
                                    Select Day(s) for Schedule
                                </h3>

                                <div class="mb-6">
                                    <p class="text-sm text-gray-600 mb-4">
                                        Choose which day(s) you want to set individual going-out schedule for the selected students.
                                        <span class="font-medium text-orange-600">Note: Schedule will automatically expire at midnight of the selected day.</span>
                                    </p>

                                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3">
                                        @foreach(['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'] as $day)
                                            <label class="flex items-center gap-2 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-white hover:border-orange-300 transition-all duration-200">
                                                <input type="checkbox" name="selected_days[]" value="{{ $day }}"
                                                       class="day-checkbox w-4 h-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500">
                                                <span class="text-sm font-medium text-gray-700 capitalize">{{ $day }}</span>
                                            </label>
                                        @endforeach
                                    </div>
                                </div>

                                <div class="flex justify-end">
                                    <button type="button" id="proceed-to-schedule-btn" disabled
                                            class="px-6 py-2 bg-orange-600 text-white font-medium rounded-lg hover:bg-orange-700 focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 transition-all duration-200 disabled:bg-gray-300 disabled:cursor-not-allowed">
                                        <i data-feather="arrow-right" class="inline w-4 h-4 mr-1"></i>
                                        Proceed to Set Schedule
                                    </button>
                                </div>
                            </div>

                            <!-- Schedule Setting Section (Initially Hidden) -->
                            <div id="schedule-section" class="hidden mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                                    <i data-feather="clock" class="inline w-5 h-5 mr-2 text-blue-500"></i>
                                    Set Going Out Schedule
                                </h3>

                                <!-- Selected Days Info -->
                                <div class="mb-6 p-4 bg-white border border-blue-200 rounded-lg">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Selected Day(s):</h4>
                                    <div id="selected-days-display" class="flex flex-wrap gap-2"></div>
                                </div>

                                <!-- Days Schedule (Dynamic) -->
                                <div id="schedule-cards-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    <!-- Schedule cards will be dynamically generated here -->
                                </div>

                                <!-- Submit Button -->
                                <div class="mt-6 flex justify-end">
                                    <button type="submit"
                                            class="px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200">
                                        <i data-feather="save" class="inline w-4 h-4 mr-2"></i>
                                        Set Schedule for Selected Students
                                    </button>
                                </div>
                            </div>
                        </form>
                    @else
                        <div class="text-center py-12">
                            <div class="text-gray-400 mb-4">
                                <i data-feather="users" class="w-16 h-16 mx-auto"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No students found</h3>
                            <p class="text-gray-600">Try adjusting your search criteria or filters.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Schedule Details Modal -->
    <div id="schedule-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-800">
                            <i data-feather="calendar" class="inline w-5 h-5 mr-2 text-orange-500"></i>
                            Schedule Details
                        </h3>
                        <button type="button" id="close-modal" class="text-gray-400 hover:text-gray-600">
                            <i data-feather="x" class="w-5 h-5"></i>
                        </button>
                    </div>

                    <div id="modal-content" class="space-y-4">
                        <!-- Content will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const selectAllCheckbox = document.getElementById('select-all');
            const studentCheckboxes = document.querySelectorAll('.student-checkbox');
            const selectedCountSpan = document.getElementById('selected-count');
            const proceedBtn = document.getElementById('proceed-btn');
            const daySelectionSection = document.getElementById('day-selection-section');
            const scheduleSection = document.getElementById('schedule-section');
            const dayCheckboxes = document.querySelectorAll('.day-checkbox');
            const proceedToScheduleBtn = document.getElementById('proceed-to-schedule-btn');
            const selectedDaysDisplay = document.getElementById('selected-days-display');
            const scheduleCardsContainer = document.getElementById('schedule-cards-container');

            // Search and filter elements
            const searchInput = document.getElementById('search-input');
            const batchFilter = document.getElementById('batch-filter');
            const groupFilter = document.getElementById('group-filter');
            const scheduleFilter = document.getElementById('schedule-filter');
            const studentRows = document.querySelectorAll('tbody tr');

            // Modal elements
            const scheduleModal = document.getElementById('schedule-modal');
            const closeModalBtn = document.getElementById('close-modal');
            const modalContent = document.getElementById('modal-content');

            // Update selected count and proceed button state
            function updateSelectionState() {
                // Count all selected students (both visible and hidden)
                const allSelectedCount = document.querySelectorAll('.student-checkbox:checked').length;

                // Count only visible selected students for select-all logic
                const visibleRows = Array.from(studentRows).filter(row => row.style.display !== 'none');
                const visibleSelectedCount = visibleRows.filter(row => {
                    const checkbox = row.querySelector('.student-checkbox');
                    return checkbox && checkbox.checked;
                }).length;

                // Update selected count display (show total selected, including hidden)
                const hiddenSelectedCount = allSelectedCount - visibleSelectedCount;
                if (hiddenSelectedCount > 0) {
                    selectedCountSpan.innerHTML = `${allSelectedCount} <span class="text-xs text-orange-600">(${hiddenSelectedCount} hidden)</span>`;
                } else {
                    selectedCountSpan.textContent = allSelectedCount;
                }
                proceedBtn.disabled = allSelectedCount === 0;

                // Update select all checkbox state based on visible students only
                const visibleCheckboxes = visibleRows.map(row => row.querySelector('.student-checkbox')).filter(cb => cb);

                if (visibleSelectedCount === 0) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = false;
                } else if (visibleSelectedCount === visibleCheckboxes.length && visibleCheckboxes.length > 0) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = true;
                } else {
                    selectAllCheckbox.indeterminate = true;
                }
            }

            // Update day selection state
            function updateDaySelectionState() {
                const selectedDays = document.querySelectorAll('.day-checkbox:checked');
                proceedToScheduleBtn.disabled = selectedDays.length === 0;
            }

            // Live search and filter functionality
            function filterStudents() {
                const searchTerm = searchInput.value.toLowerCase().trim();
                const selectedBatch = batchFilter.value;
                const selectedGroup = groupFilter.value;
                const selectedScheduleStatus = scheduleFilter.value;
                let visibleCount = 0;
                let visibleSelectedCount = 0;

                studentRows.forEach(row => {
                    const studentId = row.querySelector('td:nth-child(1)').textContent.toLowerCase();
                    const studentName = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                    const studentBatchElement = row.querySelector('td:nth-child(3) span');
                    const studentGroupElement = row.querySelector('td:nth-child(4) span');
                    const studentBatch = studentBatchElement ? studentBatchElement.textContent.trim() : '';
                    const studentGroup = studentGroupElement ? studentGroupElement.textContent.trim() : '';
                    const hasSchedule = row.getAttribute('data-has-schedule') === 'true';
                    const checkbox = row.querySelector('.student-checkbox');

                    // Check search criteria
                    const matchesSearch = searchTerm === '' ||
                                        studentId.includes(searchTerm) ||
                                        studentName.includes(searchTerm);

                    // Check batch filter
                    const matchesBatch = selectedBatch === '' || studentBatch === selectedBatch;

                    // Check group filter
                    const matchesGroup = selectedGroup === '' || studentGroup === selectedGroup;

                    // Check schedule status filter
                    const matchesScheduleStatus = selectedScheduleStatus === '' ||
                                                (selectedScheduleStatus === 'with-schedule' && hasSchedule) ||
                                                (selectedScheduleStatus === 'without-schedule' && !hasSchedule);

                    // Show/hide row based on all criteria
                    if (matchesSearch && matchesBatch && matchesGroup && matchesScheduleStatus) {
                        row.style.display = '';
                        visibleCount++;

                        // Count visible selected students
                        if (checkbox && checkbox.checked) {
                            visibleSelectedCount++;
                        }
                    } else {
                        row.style.display = 'none';
                        // DO NOT uncheck hidden students - preserve their selection
                        // This allows users to maintain their selections across filters
                    }
                });

                // Update total count display (showing visible students)
                const totalCountElement = document.querySelector('.flex.items-center.gap-4 .text-sm.text-gray-600');
                if (totalCountElement) {
                    const totalStudents = studentRows.length;
                    if (visibleCount === totalStudents) {
                        totalCountElement.innerHTML = `Total: <span class="font-semibold">${visibleCount}</span> students`;
                    } else {
                        totalCountElement.innerHTML = `Showing: <span class="font-semibold">${visibleCount}</span> of <span class="font-semibold">${totalStudents}</span> students`;
                    }
                }

                // Update selection state after filtering
                updateSelectionState();
            }

            // Generate schedule cards for selected days
            function generateScheduleCards() {
                const selectedDays = document.querySelectorAll('.day-checkbox:checked');
                scheduleCardsContainer.innerHTML = '';
                selectedDaysDisplay.innerHTML = '';

                // Display selected days
                selectedDays.forEach(checkbox => {
                    const dayName = checkbox.value;
                    const badge = document.createElement('span');
                    badge.className = 'px-3 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full capitalize';
                    badge.textContent = dayName;
                    selectedDaysDisplay.appendChild(badge);
                });

                // Generate schedule cards
                selectedDays.forEach(checkbox => {
                    const dayName = checkbox.value;
                    const card = document.createElement('div');
                    card.className = 'p-4 bg-white border border-gray-200 rounded-lg';
                    card.innerHTML = `
                        <h4 class="font-semibold text-gray-800 mb-3 capitalize">${dayName}</h4>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">
                                    <i data-feather="log-out" class="inline w-3 h-3 mr-1 text-red-500"></i>
                                    Log Out Time
                                </label>
                                <input type="time" name="schedule[${dayName}][time_out]" required
                                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <label class="block text-xs font-medium text-gray-600 mb-1">
                                    <i data-feather="log-in" class="inline w-3 h-3 mr-1 text-green-500"></i>
                                    Log In Time
                                </label>
                                <input type="time" name="schedule[${dayName}][time_in]" required
                                       class="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                        </div>
                    `;
                    scheduleCardsContainer.appendChild(card);
                });

                // Re-initialize feather icons for the new elements
                if (typeof feather !== 'undefined') {
                    feather.replace();
                }
            }

            // Select all functionality (only affects visible students)
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;

                // Only affect visible students
                studentRows.forEach(row => {
                    if (row.style.display !== 'none') {
                        const checkbox = row.querySelector('.student-checkbox');
                        if (checkbox) {
                            checkbox.checked = isChecked;
                        }
                    }
                });

                updateSelectionState();
            });

            // Individual student checkbox change
            studentCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateSelectionState);
            });

            // Day checkbox change
            dayCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateDaySelectionState);
            });

            // Live search and filter event listeners
            searchInput.addEventListener('input', filterStudents);
            batchFilter.addEventListener('change', function() {
                console.log('Batch filter changed to:', batchFilter.value);
                filterStudents();
            });
            groupFilter.addEventListener('change', function() {
                console.log('Group filter changed to:', groupFilter.value);
                filterStudents();
            });
            scheduleFilter.addEventListener('change', function() {
                console.log('Schedule filter changed to:', scheduleFilter.value);
                filterStudents();
            });

            // Schedule day button click handlers
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('schedule-day-btn')) {
                    const studentId = e.target.getAttribute('data-student-id');
                    const day = e.target.getAttribute('data-day');
                    const timeOut = e.target.getAttribute('data-time-out');
                    const timeIn = e.target.getAttribute('data-time-in');
                    const validUntil = e.target.getAttribute('data-valid-until');

                    showScheduleModal(studentId, day, timeOut, timeIn, validUntil);
                }
            });

            // Modal close handlers
            closeModalBtn.addEventListener('click', function() {
                scheduleModal.classList.add('hidden');
            });

            scheduleModal.addEventListener('click', function(e) {
                if (e.target === scheduleModal) {
                    scheduleModal.classList.add('hidden');
                }
            });

            // Function to show schedule modal
            function showScheduleModal(studentId, day, timeOut, timeIn, validUntil) {
                const studentRow = document.querySelector(`tr input[value="${studentId}"]`).closest('tr');
                const studentName = studentRow.querySelector('td:nth-child(2)').textContent.trim();

                modalContent.innerHTML = `
                    <div class="space-y-3">
                        <div class="p-3 bg-gray-50 rounded-lg">
                            <h4 class="font-medium text-gray-800 mb-1">Student Information</h4>
                            <p class="text-sm text-gray-600">ID: <span class="font-medium">${studentId}</span></p>
                            <p class="text-sm text-gray-600">Name: <span class="font-medium">${studentName}</span></p>
                        </div>

                        <div class="p-3 bg-orange-50 rounded-lg">
                            <h4 class="font-medium text-gray-800 mb-2">
                                <i data-feather="calendar" class="inline w-4 h-4 mr-1 text-orange-500"></i>
                                ${day} Schedule
                            </h4>
                            <div class="grid grid-cols-2 gap-3">
                                <div>
                                    <p class="text-xs text-gray-600 mb-1">
                                        <i data-feather="log-out" class="inline w-3 h-3 mr-1 text-red-500"></i>
                                        Log Out Time
                                    </p>
                                    <p class="text-sm font-medium text-gray-800">${timeOut}</p>
                                </div>
                                <div>
                                    <p class="text-xs text-gray-600 mb-1">
                                        <i data-feather="log-in" class="inline w-3 h-3 mr-1 text-green-500"></i>
                                        Log In Time
                                    </p>
                                    <p class="text-sm font-medium text-gray-800">${timeIn}</p>
                                </div>
                            </div>
                            ${validUntil ? `
                                <div class="mt-2 pt-2 border-t border-orange-200">
                                    <p class="text-xs text-gray-600">
                                        <i data-feather="clock" class="inline w-3 h-3 mr-1"></i>
                                        Valid until: <span class="font-medium">${validUntil}</span>
                                    </p>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;

                scheduleModal.classList.remove('hidden');

                // Re-initialize feather icons
                if (typeof feather !== 'undefined') {
                    feather.replace();
                }
            }

            // Proceed to day selection
            proceedBtn.addEventListener('click', function() {
                daySelectionSection.classList.remove('hidden');
                daySelectionSection.scrollIntoView({ behavior: 'smooth' });
            });

            // Proceed to schedule setting
            proceedToScheduleBtn.addEventListener('click', function() {
                generateScheduleCards();
                scheduleSection.classList.remove('hidden');
                scheduleSection.scrollIntoView({ behavior: 'smooth' });
            });

            // Initialize
            updateSelectionState();
            updateDaySelectionState();
        });
    </script>
</x-monitorLayout>
