<?php

namespace App\Console\Commands;

use App\Models\StudentDetail;
use App\Models\Going_out;
use Illuminate\Console\Command;

class PopulateGoingOutData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:populate-going-out-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Fetch all students
        $students = StudentDetail::all();

        // Get today's date
        $date = now()->format('Y-m-d');
        $isSunday = now()->isSunday();

         $data = [];
        foreach ($students as $student) {
             // For Sunday: Create only one session (session 1)
            // For Monday-Saturday: Don't create any records (students will create sessions as needed)
            if ($isSunday) {
                $data[] = [
                    'student_id' => $student->student_id,
                    'going_out_date' => $date,
                    'session_number' => 1,
                    'session_status' => 'active',
                    'destination' => null,
                    'purpose' => null,
                    'time_out' => null,
                    'time_in' => null,
                    'time_out_remark' => null,
                    'time_in_remark' => null,
                ];
            }
        }

        if (!empty($data)) {
            Going_out::insert($data);
            $this->info('Going out log data populated successfully for ' . $date . ' (Sunday - single session)');
        } else {
            $this->info('No going out records created for ' . $date . ' (Weekday - sessions created on demand)');
        }
    }
}
