<?php

namespace App\Http\Controllers;

use App\Models\StudentGen;
use App\Models\Category;
use App\Models\Assignment;
use App\Models\AssignmentMember;
use App\Models\TaskChecklist;
use App\Models\TaskChecklistStatus;
use App\Models\DynamicTask;
use App\Models\StudentAllocation;
use App\Models\GeneralTaskAssignment;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Carbon\Carbon;

class GeneralTaskController extends Controller
{
    public function index() {
        // Clean expired comments first
        AssignmentMember::cleanExpiredComments();

        // DYNAMIC SYSTEM: Fetch categories with dynamic tasks
        $categories = Category::with(['assignments' => function($query) {
            $query->where('status', 'current');
        }, 'assignments.assignmentMembers.student'])->get();

        // DYNAMIC SYSTEM: Fetch dynamic tasks with categories
        $dynamicTasks = DynamicTask::with('category')
            ->active()
            ->ordered()
            ->get()
            ->groupBy('category.name');

        // DYNAMIC SYSTEM: Get today's student allocations
        $today = now()->format('Y-m-d');
        $studentAllocations = StudentAllocation::forDate($today)->get();

        // DYNAMIC SYSTEM: Get today's task assignments
        $taskAssignments = GeneralTaskAssignment::with('task.category')
            ->forDate($today)
            ->active()
            ->get()
            ->groupBy('task.category.name');

        // Fetch all students
        $students = StudentGen::all();

        // Fetch active batches that have students
        $activeBatches = \App\Models\Batch::active()
            ->whereHas('students')
            ->get();

        // Fetch only current assignments for the main table (to avoid N/A duplicates)
        $assignments = Assignment::with(['category', 'assignmentMembers.student'])
            ->where('status', 'current')
            ->orderBy('start_date', 'desc')
            ->get();

        // Assignment history (for View History modal - includes both current and previous)
        $assignmentHistory = Assignment::with(['category', 'assignmentMembers.student'])
            ->orderBy('status', 'asc') // current first, then previous
            ->orderBy('id', 'desc') // newest first within same status
            ->get();

        // Pass all data to the generalTask view
        return view('generalTask', compact(
            'categories',
            'students',
            'assignments',
            'assignmentHistory',
            'activeBatches',
            'dynamicTasks',
            'studentAllocations',
            'taskAssignments'
        ));
    }

    // DEBUG: Check saved statuses in database
    public function debugStatuses(Request $request)
    {
        $date = $request->input('date', now()->format('Y-m-d'));
        $pageNumber = $request->input('page_number');

        $query = TaskChecklistStatus::where('task_date', $date);

        if ($pageNumber) {
            $query->where('page_number', $pageNumber);
        }

        $statuses = $query->get();

        return response()->json([
            'success' => true,
            'date' => $date,
            'page_number' => $pageNumber,
            'total_statuses' => $statuses->count(),
            'statuses' => $statuses->map(function($status) {
                return [
                    'id' => $status->id,
                    'task_id' => $status->task_id,
                    'task_date' => $status->task_date,
                    'status' => $status->status,
                    'page_number' => $status->page_number,
                    'created_at' => $status->created_at
                ];
            })
        ]);
    }

    public function taskChecklist(Request $request)
    {
        // Use selected date if provided, else today
        $date = $request->query('date');
        $currentDate = $date ? \Carbon\Carbon::parse($date) : \Carbon\Carbon::now();

        // Always start weekDays from the selected date (not startOfWeek)
        $weekDays = [];
        $tasksByDay = [];
        
        // Initialize tasks for the week starting from the selected date
        $this->initializeDefaultTasksForWeek($currentDate);
        
        for ($i = 0; $i < 7; $i++) {
            $day = $currentDate->copy()->addDays($i)->format('Y-m-d');
            $weekDays[] = $day;
            // Get tasks for this specific day (they should all reference the same week's tasks)
            $tasksByDay[$day] = TaskChecklist::whereDate('week_start_date', $currentDate->format('Y-m-d'))->get();
        }

        // Debug: log the weekDays array
        \Log::info('Checklist weekDays', $weekDays);

        // For rendering, get the first day's tasks as the base (assume all days have same task structure)
        $baseTasks = $tasksByDay[$weekDays[0]];

        return view('task-checklist', [
            'tasksByDay' => $tasksByDay,
            'baseTasks' => $baseTasks,
            'currentDate' => $currentDate,
            'weekDays' => $weekDays
        ]);
    }

    public function updateTaskStatus(Request $request)
    {
        $validated = $request->validate([
            'task_id' => 'required|exists:task_checklists,id',
            'week' => 'required|integer|in:1,2',
            'day' => 'required|integer|min:0|max:6',
            'status' => 'required|in:0,1', // assuming 0 = not done, 1 = done
        ]);

        $task = TaskChecklist::findOrFail($validated['task_id']);

        if ($validated['week'] == 1) {
            $status = $task->week1_status ?? array_fill(0, 7, null);
            $status[$validated['day']] = $validated['status'];
            $task->week1_status = $status;
        } else {
            $status = $task->week2_status ?? array_fill(0, 7, null);
            $status[$validated['day']] = $validated['status'];
            $task->week2_status = $status;
        }

        $task->save();

        return response()->json(['success' => true, 'message' => 'Saved successfully.']);
    }

    // Enhanced method for dashboard task checklist modal - PERMANENT Per-Day Persistence System
    public function updateTaskStatusExact(Request $request)
    {
        Log::info('🎯 [PERMANENT-SAVE] updateTaskStatusExact called with data: ' . json_encode($request->all()));

        $request->validate([
            'task_id' => 'required',
            'date' => 'required|date',
            'status' => 'nullable|in:✓,✗,check,wrong,',
        ]);

        $taskId = $request->input('task_id');
        $date = $request->input('date');
        $status = $request->input('status');

        Log::info("💾 [PERMANENT-SAVE] Saving PERMANENT status for Task: {$taskId}, Date: {$date}, Status: {$status}");

        // Use enhanced permanent save method
        $result = $this->savePermanentTaskStatus($taskId, $date, $status);
        Log::info('✅ [PERMANENT-SAVE] updateTaskStatusExact result: ' . json_encode($result));

        return response()->json([
            'success' => $result['success'],
            'message' => $result['message'],
            'task_id' => $taskId,
            'date' => $date,
            'status' => $status,
            'saved_timestamp' => now()->format('Y-m-d H:i:s'),
            'permanent_system' => true,
            'never_disappears' => true
        ]);
    }



    // PERMANENT: Save individual task status with NEVER DISAPPEAR persistence
    private function savePermanentTaskStatus($taskId, $date, $status)
    {
        try {
            Log::info("🔒 [PERMANENT-SAVE-METHOD] Saving permanent status: TaskID={$taskId}, Date={$date}, Status={$status}");

            // Convert status to database format
            $dbStatus = null;
            if ($status === 'check' || $status === '✓') {
                $dbStatus = 'check';
            } elseif ($status === 'wrong' || $status === '✗') {
                $dbStatus = 'wrong';
            } elseif (empty($status)) {
                // Clear status
                TaskChecklistStatus::where('task_id', $taskId)
                    ->where('task_date', $date)
                    ->delete();

                Log::info("🔒 [PERMANENT-SAVE-METHOD] Status cleared for TaskID={$taskId}, Date={$date}");
                return [
                    'success' => true,
                    'message' => 'Status cleared successfully',
                    'action' => 'cleared'
                ];
            }

            if ($dbStatus) {
                // Save or update status in TaskChecklistStatus table
                $checklistStatus = TaskChecklistStatus::updateOrCreate(
                    [
                        'task_id' => $taskId,
                        'task_date' => $date
                    ],
                    [
                        'status' => $dbStatus,
                        'exact_timestamp' => now(),
                        'remarks' => null // Can be updated separately
                    ]
                );

                Log::info("🔒 [PERMANENT-SAVE-METHOD] Successfully saved: TaskID={$taskId}, Date={$date}, Status={$dbStatus}");

                return [
                    'success' => true,
                    'message' => 'Status saved permanently',
                    'task_id' => $taskId,
                    'date' => $date,
                    'status' => $dbStatus,
                    'display_status' => $status === 'check' ? '✓' : '✗',
                    'timestamp' => $checklistStatus->exact_timestamp,
                    'permanent' => true,
                    'never_disappears' => true
                ];
            } else {
                Log::warning("🔒 [PERMANENT-SAVE-METHOD] Invalid status provided: {$status}");
                return [
                    'success' => false,
                    'message' => 'Invalid status provided'
                ];
            }

        } catch (\Exception $e) {
            Log::error("🔒 [PERMANENT-SAVE-METHOD] Error saving status: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error saving status: ' . $e->getMessage()
            ];
        }
    }

    // DASHBOARD PERSISTENT STATUS: Save all task statuses with persistent UI retention
    public function saveAllTaskStatuses(Request $request)
    {
        Log::info('🔒 [DASHBOARD-PERMANENT-SAVE] === SAVE ALL STATUSES CALLED ===');
        Log::info('🔒 [DASHBOARD-PERMANENT-SAVE] Request method: ' . $request->method());
        Log::info('🔒 [DASHBOARD-PERMANENT-SAVE] Request URL: ' . $request->url());
        Log::info('🔒 [DASHBOARD-PERMANENT-SAVE] All request data: ' . json_encode($request->all()));
        Log::info('🔒 [DASHBOARD-PERMANENT-SAVE] Headers: ' . json_encode($request->headers->all()));
        Log::info('� [DASHBOARD-PERSIST] saveAllTaskStatuses called with data: ' . json_encode($request->all()));

        // More flexible validation to handle the error
        try {
            $statuses = $request->input('statuses', []);
            $date = $request->input('date');

            Log::info('🔒 [DASHBOARD-PERMANENT-SAVE] Received statuses count: ' . count($statuses));
            Log::info('🔒 [DASHBOARD-PERMANENT-SAVE] Received date: ' . $date);

            if (empty($statuses)) {
                Log::warning('🔒 [DASHBOARD-PERMANENT-SAVE] No statuses provided');
                return response()->json([
                    'success' => false,
                    'message' => 'No statuses provided'
                ]);
            }

            if (!$date) {
                Log::warning('🔒 [DASHBOARD-PERMANENT-SAVE] No date provided');
                return response()->json([
                    'success' => false,
                    'message' => 'No date provided'
                ]);
            }

        } catch (\Exception $e) {
            Log::error('🔒 [DASHBOARD-PERMANENT-SAVE] Input processing failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Input processing failed: ' . $e->getMessage()
            ]);
        }

        // VALIDATION: Ensure statuses persist across navigation and reloads
        Log::info('🔒 [DASHBOARD-VALIDATION] Starting permanent status validation for ' . count($statuses) . ' statuses');
        $savedCount = 0;
        $failedCount = 0;
        $results = [];

        Log::info("� [DASHBOARD-PERSIST] Processing " . count($statuses) . " statuses for date: {$date}");

        foreach ($statuses as $statusData) {
            $taskId = $statusData['task_id'] ?? null;
            $status = $statusData['status'] ?? null;
            $week = $statusData['week'] ?? null;
            $day = $statusData['day'] ?? null;

            if (!$taskId) {
                Log::warning("⚠️ [DASHBOARD-PERMANENT] Skipping invalid status data (no task_id): " . json_encode($statusData));
                $failedCount++;
                continue;
            }

            // Allow null status (for clearing/unchecking)
            if ($status === null) {
                Log::info("🔄 [DASHBOARD-PERMANENT] Clearing status for task: {$taskId}, day: {$day}");
            }

            // PERMANENT: Save with enhanced persistence using TaskStatus model
            try {
                Log::info("🔒 [DASHBOARD-PERMANENT-SAVE] Processing task: {$taskId}, status: {$status}, week: {$week}, day: {$day}");

                // Convert status to database format
                $dbStatus = null;
                if ($status === 'correct' || $status === '✓' || $status === 'check') {
                    $dbStatus = 'correct';
                } elseif ($status === 'wrong' || $status === '✗' || $status === 'x') {
                    $dbStatus = 'wrong';
                } elseif ($status === null) {
                    $dbStatus = null; // Clear the status
                }

                // Calculate week start date
                $selectedDate = Carbon::parse($date);
                $weekStart = $selectedDate->copy()->startOfWeek()->format('Y-m-d');

                // Map day abbreviations to day numbers
                $dayMap = [
                    'mon' => 0, 'tue' => 1, 'wed' => 2, 'thu' => 3,
                    'fri' => 4, 'sat' => 5, 'sun' => 6
                ];
                $dayNumber = $dayMap[$day] ?? 0;

                // Find or create the task checklist record using task_description as identifier
                Log::info("🔍 [DEBUG] Looking for task checklist with task_description: {$taskId}, week_start_date: {$weekStart}");

                $taskChecklist = TaskChecklist::firstOrCreate([
                    'task_description' => $taskId,
                    'week_start_date' => $weekStart
                ], [
                    'task_category' => 'General Cleaning',
                    'week1_status' => array_fill(0, 7, null),
                    'week2_status' => array_fill(0, 7, null)
                ]);

                Log::info("🔍 [DEBUG] Task checklist found/created: ID {$taskChecklist->id}");

                // Update the appropriate week and day
                $weekField = $week == 1 ? 'week1_status' : 'week2_status';
                $currentStatuses = $taskChecklist->$weekField ?? array_fill(0, 7, null);

                // Ensure array has 7 elements
                if (count($currentStatuses) < 7) {
                    $currentStatuses = array_pad($currentStatuses, 7, null);
                }

                // Update the specific day
                Log::info("🔍 [DEBUG] Before update - Day number: {$dayNumber}, Status: {$dbStatus}");
                Log::info("🔍 [DEBUG] Current statuses before: " . json_encode($currentStatuses));

                $currentStatuses[$dayNumber] = $dbStatus;
                $taskChecklist->$weekField = $currentStatuses;

                Log::info("🔍 [DEBUG] Current statuses after: " . json_encode($currentStatuses));
                Log::info("🔍 [DEBUG] About to save task checklist...");

                $saved = $taskChecklist->save();

                Log::info("🔍 [DEBUG] Save result: " . ($saved ? 'SUCCESS' : 'FAILED'));

                Log::info("🔒 [DASHBOARD-PERMANENT-SAVE] Successfully saved task checklist: {$taskChecklist->id}");

                $savedCount++;
                Log::info("🔒 [DASHBOARD-PERMANENT] PERMANENTLY saved Task: {$taskId}, Status: {$status}, Week: {$week}, Day: {$day}");

                $results[] = [
                    'success' => true,
                    'task_id' => $taskId,
                    'status' => $status,
                    'week' => $week,
                    'day' => $day,
                    'permanent' => true
                ];

            } catch (\Exception $e) {
                $failedCount++;
                Log::error("❌ [DASHBOARD-PERMANENT] Failed to save Task: {$taskId}, Error: " . $e->getMessage());

                $results[] = [
                    'success' => false,
                    'task_id' => $taskId,
                    'error' => $e->getMessage()
                ];
            }
        }

        $totalCount = count($statuses);
        Log::info("� [DASHBOARD-PERSIST] Batch save completed - Saved: {$savedCount}, Failed: {$failedCount}, Total: {$totalCount}");

        Log::info("🔒 [DASHBOARD-FINAL] Final result - Saved: {$savedCount}, Failed: {$failedCount}, Total: {$totalCount}");

        return response()->json([
            'success' => $savedCount > 0,
            'message' => $savedCount > 0 ?
                "✅ Successfully saved {$savedCount} task status(es)!" :
                "❌ No statuses were saved. Check logs for details.",
            'saved_count' => $savedCount,
            'failed_count' => $failedCount,
            'total_count' => $totalCount,
            'date' => $date,
            'results' => $results,
            'dashboard_persistent' => true,
            'permanent_save' => true,
            'never_disappear' => true,
            'ui_retention' => true,
            'persist_across_navigation' => true,
            'saved_timestamp' => now()->format('Y-m-d H:i:s'),
            'success_message' => "✅ Saved successfully! {$savedCount} task status(es) have been saved.",
            'debug_info' => [
                'statuses_received' => count($statuses),
                'date_received' => $date
            ]
        ]);
    }

    // DASHBOARD PERMANENT STATUS: Load all permanently saved statuses for a specific date
    public function loadDashboardStatuses(Request $request)
    {
        $request->validate([
            'date' => 'required|date',
            'page_number' => 'nullable|integer|min:1|max:10'
        ]);

        $date = $request->input('date');
        $pageNumber = $request->input('page_number', 1); // Default to page 1
        Log::info("🔒 [DASHBOARD-PERMANENT-LOAD] Loading PERMANENT statuses for date: {$date}, page: {$pageNumber}");

        try {
            // Load from TaskChecklistStatus table for PERMANENT persistence - NEVER DISAPPEAR
            $checklistStatuses = TaskChecklistStatus::where('task_date', $date)
                ->where('page_number', $pageNumber)
                ->get();

            $allStatuses = [];
            $loadedCount = 0;

            foreach ($checklistStatuses as $checklistStatus) {
                $taskId = $checklistStatus->task_id;
                $dbStatus = $checklistStatus->status; // 'check' or 'wrong'
                $displayStatus = $dbStatus === 'check' ? '✓' : '✗'; // Convert to display format

                $allStatuses[$taskId] = [
                    'status' => $displayStatus, // ✓ or ✗ for display
                    'task_id' => $taskId,
                    'date' => $date,
                    'db_status' => $dbStatus,
                    'permanent_save' => true,
                    'never_disappear' => true,
                    'persist_across_navigation' => true,
                    'persist_across_reload' => true
                ];

                $loadedCount++;
                Log::info("🔒 [DASHBOARD-PERMANENT-LOAD] Loaded PERMANENT status #{$loadedCount}: {$taskId} = {$displayStatus}");
            }

            Log::info("🔒 [DASHBOARD-PERMANENT-LOAD] Total PERMANENT statuses loaded: {$loadedCount}");

            // FALLBACK: Also check TaskChecklist table for compatibility
            $targetDate = Carbon::parse($date);
            $weekStart = $targetDate->copy()->startOfWeek(Carbon::MONDAY);
            $taskChecklists = TaskChecklist::where('week_start_date', $weekStart->format('Y-m-d'))->get();

            foreach ($taskChecklists as $checklist) {
                // Skip if already loaded from ChecklistStatus
                if (isset($allStatuses[$checklist->task_id])) {
                    continue;
                }

                // Calculate which week and day this date falls into
                $dayDiff = $weekStart->diffInDays($targetDate);
                $weekNumber = $dayDiff < 7 ? 1 : 2;
                $dayIndex = $dayDiff % 7;

                // Get the status for this specific day
                $statusArray = $weekNumber === 1 ? $checklist->week1_status : $checklist->week2_status;
                $status = $statusArray[$dayIndex] ?? null;

                if ($status) {
                    // Convert database status to display format
                    $displayStatus = $status === 'check' ? '✓' : ($status === 'wrong' ? '✗' : null);

                    if ($displayStatus) {
                        $allStatuses[$checklist->task_id] = [
                            'status' => $displayStatus,
                            'task_id' => $checklist->task_id,
                            'date' => $date,
                            'week_number' => $weekNumber,
                            'day_index' => $dayIndex,
                            'fallback_load' => true
                        ];
                    }
                }
            }

            Log::info("� [DASHBOARD-PERMANENT-LOAD] Loaded " . count($allStatuses) . " PERMANENT statuses for date: {$date}");

            return response()->json([
                'success' => true,
                'statuses' => $allStatuses,
                'date' => $date,
                'count' => count($allStatuses),
                'dashboard_load' => true,
                'permanent_load' => true,
                'never_disappear' => true
            ]);

        } catch (\Exception $e) {
            Log::error("❌ [DASHBOARD-PERMANENT-LOAD] Error loading statuses: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error loading statuses: ' . $e->getMessage(),
                'statuses' => []
            ]);
        }
    }

    // DASHBOARD PERSISTENT STATUS: Save individual task status with UI retention
    private function savePersistentTaskStatus($taskId, $date, $status)
    {
        try {
            Log::info("🔒 [PERMANENT-SAVE] Starting permanent save for Task: {$taskId}, Date: {$date}, Status: {$status}");

            // Convert display symbols to database values
            $dbStatus = null;
            if ($status === '✓' || $status === 'check') {
                $dbStatus = 'check';
            } elseif ($status === '✗' || $status === 'wrong') {
                $dbStatus = 'wrong';
            }

            if (!$dbStatus) {
                Log::warning("⚠️ [PERMANENT-SAVE] Empty status provided, clearing status for Task: {$taskId}, Date: {$date}");
            }

            // Find or create task record for this frontend task ID
            $taskChecklist = $this->findOrCreateTaskFromFrontendId($taskId, $date);

            if (!$taskChecklist) {
                Log::error("❌ [PERMANENT-SAVE] Could not find/create task checklist for Task: {$taskId}");
                return ['success' => false, 'message' => 'Could not find or create task record'];
            }

            Log::info("✅ [PERMANENT-SAVE] Found/created task checklist ID: {$taskChecklist->id}, Week: {$taskChecklist->week_start_date}");

            // Calculate which week and day index this date falls into
            $taskDate = Carbon::parse($taskChecklist->week_start_date);
            $statusDate = Carbon::parse($date);
            $dayDiff = $taskDate->diffInDays($statusDate);
            $weekNumber = $dayDiff < 7 ? 1 : 2;
            $dayIndex = $dayDiff % 7;

            Log::info("📊 [PERMANENT-SAVE] Date calculations - Task Week Start: {$taskDate->format('Y-m-d')}, Status Date: {$statusDate->format('Y-m-d')}, Day Diff: {$dayDiff}, Week: {$weekNumber}, Day Index: {$dayIndex}");

            // Get current status array and update it
            $statusArray = $weekNumber === 1 ?
                ($taskChecklist->week1_status ?? array_fill(0, 7, null)) :
                ($taskChecklist->week2_status ?? array_fill(0, 7, null));

            // Update the specific day
            $statusArray[$dayIndex] = $dbStatus;

            // Save to database with PERMANENT persistence
            if ($weekNumber === 1) {
                $taskChecklist->week1_status = $statusArray;
                Log::info("📝 [PERMANENT-SAVE] Updated week1_status[{$dayIndex}] = '{$dbStatus}' for date {$statusDate->format('Y-m-d')}");
            } else {
                $taskChecklist->week2_status = $statusArray;
                Log::info("📝 [PERMANENT-SAVE] Updated week2_status[{$dayIndex}] = '{$dbStatus}' for date {$statusDate->format('Y-m-d')}");
            }

            // Force save with timestamp
            $taskChecklist->updated_at = now();
            $saved = $taskChecklist->save();

            if ($saved) {
                Log::info("🔒 [PERMANENT-SAVE] Status PERMANENTLY saved to database for Task: {$taskId}, Date: {$statusDate->format('Y-m-d')}");

                // Verify the save by re-reading from database
                $verification = TaskChecklist::find($taskChecklist->id);
                $verifyArray = $weekNumber === 1 ? $verification->week1_status : $verification->week2_status;
                $verifyStatus = $verifyArray[$dayIndex] ?? null;

                Log::info("🔍 [PERMANENT-SAVE] Verification check - Saved status: {$verifyStatus}, Expected: {$dbStatus}");

                return [
                    'success' => true,
                    'message' => "🔒 Status PERMANENTLY saved for {$statusDate->format('Y-m-d')}",
                    'date' => $statusDate->format('Y-m-d'),
                    'status' => $dbStatus,
                    'task_id' => $taskId,
                    'week_number' => $weekNumber,
                    'day_index' => $dayIndex,
                    'saved_timestamp' => now()->format('Y-m-d H:i:s'),
                    'verification_status' => $verifyStatus,
                    'permanent_save' => true
                ];
            } else {
                Log::error("❌ [PERMANENT-SAVE] Failed to save status to database for Task: {$taskId}, Date: {$statusDate->format('Y-m-d')}");
                return ['success' => false, 'message' => 'Failed to save status to database'];
            }

        } catch (\Exception $e) {
            Log::error("❌ [PERMANENT-SAVE] Exception during save: " . $e->getMessage());
            return ['success' => false, 'message' => 'Error saving status: ' . $e->getMessage()];
        }
    }

    // Save single task status for auto-save functionality
    public function saveSingleTaskStatus(Request $request)
    {
        // Ensure we always return JSON
        try {
            Log::info('💾 [AUTO-SAVE] saveSingleTaskStatus called with data: ' . json_encode($request->all()));

            // Validate input
            $validated = $request->validate([
                'task_id' => 'required',
                'week' => 'required',
                'day' => 'required',
                'status' => 'nullable|in:correct,wrong',
                'date' => 'required|date'
            ]);

            $taskId = $validated['task_id'];
            $week = $validated['week'];
            $day = $validated['day'];
            $status = $validated['status'];
            $date = $validated['date'];

            Log::info("💾 [AUTO-SAVE] Processing: Task={$taskId}, Week={$week}, Day={$day}, Status={$status}, Date={$date}");

            // Parse the date to get the week start
            $selectedDate = Carbon::parse($date);
            $weekStart = $selectedDate->copy()->startOfWeek(); // Monday of the selected week

            Log::info("📅 [AUTO-SAVE] Selected date: {$selectedDate->format('Y-m-d')}, Week start: {$weekStart->format('Y-m-d')}");

            // Ensure task checklists exist for this week
            $this->ensureTaskChecklistsExist($weekStart->format('Y-m-d'));

            // Find the task checklist for this week
            $taskChecklist = TaskChecklist::where('task_id', $taskId)
                ->where('week_start_date', $weekStart->format('Y-m-d'))
                ->first();

            if (!$taskChecklist) {
                Log::error("❌ [AUTO-SAVE] Task checklist not found for task_id={$taskId}, week_start={$weekStart->format('Y-m-d')}");
                return response()->json([
                    'success' => false,
                    'message' => 'Task checklist not found for this week'
                ], 200);
            }

            // Convert day to index if it's not already a number
            $dayIndex = is_numeric($day) ? intval($day) : null;
            if ($dayIndex === null) {
                $dayMapping = [
                    'monday' => 0, 'tuesday' => 1, 'wednesday' => 2, 'thursday' => 3,
                    'friday' => 4, 'saturday' => 5, 'sunday' => 6
                ];
                $dayIndex = $dayMapping[strtolower($day)] ?? 0;
            }

            Log::info("📊 [AUTO-SAVE] Using day index: {$dayIndex}");

            // Get the appropriate week field (always use week1 for now)
            $weekField = 'week1_status';
            $currentStatuses = $taskChecklist->$weekField ?? array_fill(0, 7, null);

            Log::info("📊 [AUTO-SAVE] Current statuses before update: " . json_encode($currentStatuses));

            // Update the status
            $currentStatuses[$dayIndex] = $status;
            $taskChecklist->$weekField = $currentStatuses;
            $taskChecklist->updated_at = now();

            Log::info("📊 [AUTO-SAVE] Current statuses after update: " . json_encode($currentStatuses));

            $saved = $taskChecklist->save();

            if ($saved) {
                Log::info("✅ [AUTO-SAVE] Successfully saved: Task={$taskId}, Week={$week}, Day={$dayIndex}, Status={$status}");

                return response()->json([
                    'success' => true,
                    'message' => 'Status saved successfully',
                    'task_id' => $taskId,
                    'week' => $week,
                    'day' => $day,
                    'day_index' => $dayIndex,
                    'status' => $status,
                    'week_start' => $weekStart->format('Y-m-d'),
                    'saved_timestamp' => now()->format('Y-m-d H:i:s')
                ], 200);
            } else {
                Log::error("❌ [AUTO-SAVE] Failed to save: Task={$taskId}, Week={$week}, Day={$day}, Status={$status}");
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to save status'
                ], 200);
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::error("❌ [AUTO-SAVE] Validation error: " . json_encode($e->errors()));
            return response()->json([
                'success' => false,
                'message' => 'Validation error: ' . implode(', ', array_flatten($e->errors()))
            ], 200);
        } catch (\Exception $e) {
            Log::error("❌ [AUTO-SAVE] Exception: " . $e->getMessage());
            Log::error("❌ [AUTO-SAVE] Stack trace: " . $e->getTraceAsString());
            return response()->json([
                'success' => false,
                'message' => 'Error saving status: ' . $e->getMessage()
            ], 200);
        }
    }

    // Enhanced helper to save a single task status with per-day persistence
    private function saveSingleTaskStatusPerDay($data)
    {
        $taskId = $data['task_id'] ?? null;
        $date = $data['date'] ?? null;
        $status = $data['status'] ?? null;

        if (!$taskId || !$date) {
            Log::error("❌ [PER-DAY] Missing required data - Task ID: {$taskId}, Date: {$date}");
            return ['success' => false, 'message' => 'Missing task_id or date'];
        }

        Log::info("🔍 [PER-DAY] Processing save for Task: {$taskId}, Date: {$date}, Status: {$status}");

        // Find or create task record for this frontend task ID
        $taskChecklist = $this->findOrCreateTaskFromFrontendId($taskId, $date);

        if (!$taskChecklist) {
            Log::error("❌ [PER-DAY] Task not found and could not be created for Task: {$taskId}");
            return ['success' => false, 'message' => 'Task not found and could not be created'];
        }

        Log::info("✅ [PER-DAY] Found/created task checklist for Task: {$taskId}, Week: {$taskChecklist->week_start_date}");

        // Convert display symbols to database values with per-day tracking
        $dbStatus = null;
        if ($status === '✓' || $status === 'check') {
            $dbStatus = 'check';
        } elseif ($status === '✗' || $status === 'wrong') {
            $dbStatus = 'wrong';
        }

        Log::info("🔄 [PER-DAY] Status conversion - Input: '{$status}' -> Database: '{$dbStatus}'");

        // Calculate which week and day index this date falls into
        $taskDate = Carbon::parse($taskChecklist->week_start_date);
        $statusDate = Carbon::parse($date);
        $dayDiff = $taskDate->diffInDays($statusDate);
        $weekNumber = $dayDiff < 7 ? 1 : 2;
        $dayIndex = $dayDiff % 7;

        Log::info("📊 [PER-DAY] Date calculations - Task Week Start: {$taskDate->format('Y-m-d')}, Status Date: {$statusDate->format('Y-m-d')}, Day Diff: {$dayDiff}, Week: {$weekNumber}, Day Index: {$dayIndex}");

        $statusArray = $weekNumber === 1 ?
            ($taskChecklist->week1_status ?? array_fill(0, 7, null)) :
            ($taskChecklist->week2_status ?? array_fill(0, 7, null));

        $statusArray[$dayIndex] = $dbStatus;

        if ($weekNumber === 1) {
            $taskChecklist->week1_status = $statusArray;
            Log::info("📝 [PER-DAY] Updated week1_status[{$dayIndex}] = '{$dbStatus}' for date {$statusDate->format('Y-m-d')}");
        } else {
            $taskChecklist->week2_status = $statusArray;
            Log::info("📝 [PER-DAY] Updated week2_status[{$dayIndex}] = '{$dbStatus}' for date {$statusDate->format('Y-m-d')}");
        }

        // Save with timestamp for per-day tracking
        $taskChecklist->updated_at = now();
        $saved = $taskChecklist->save();

        if ($saved) {
            Log::info("✅ [PER-DAY] Status successfully saved to database for Task: {$taskId}, Date: {$statusDate->format('Y-m-d')}");
        } else {
            Log::error("❌ [PER-DAY] Failed to save status to database for Task: {$taskId}, Date: {$statusDate->format('Y-m-d')}");
        }

        return [
            'success' => true,
            'status' => $dbStatus,
            'display_status' => $dbStatus === 'check' ? '✓' : ($dbStatus === 'wrong' ? '✗' : ''),
            'message' => "✅ Status saved for {$statusDate->format('Y-m-d')}",
            'date' => $statusDate->format('Y-m-d'),
            'task_id' => $taskId,
            'week_number' => $weekNumber,
            'day_index' => $dayIndex,
            'saved_timestamp' => now()->format('Y-m-d H:i:s'),
            'per_day_system' => true
        ];
    }

    public function updateTaskRemarks(Request $request)
    {
        $validated = $request->validate([
            'task_id' => 'required|exists:task_checklists,id',
            'week' => 'required|integer|in:1,2',
            'remarks' => 'nullable|string|max:1000',
        ]);

        $task = TaskChecklist::findOrFail($validated['task_id']);

        if ($validated['week'] == 1) {
            $task->week1_remarks = $validated['remarks'];
        } else {
            $task->week2_remarks = $validated['remarks'];
        }

        $task->save();

        return response()->json(['success' => true, 'message' => 'Saved successfully.']);
    }

    public function updateWeekDates(Request $request)
    {
        $weekStart = Carbon::parse($request->week_start_date)->startOfWeek();

        // Update all tasks for this week
        TaskChecklist::where('week_start_date', $weekStart)
            ->update(['week_start_date' => $weekStart]);

        return response()->json(['success' => true]);
    }

    public function saveTaskChecklist(Request $request)
    {
        $validated = $request->validate([
            'tasks' => 'required|array',
            'tasks.*.task_id' => 'required|exists:task_checklists,id',
            'tasks.*.week1_status' => 'nullable|array',
            'tasks.*.week2_status' => 'nullable|array',
            'tasks.*.week1_remarks' => 'nullable|string|max:1000',
            'tasks.*.week2_remarks' => 'nullable|string|max:1000',
        ]);

        foreach ($validated['tasks'] as $taskData) {
            $task = TaskChecklist::find($taskData['task_id']);
            if (!$task) continue;
            if (isset($taskData['week1_status'])) {
                $task->week1_status = $taskData['week1_status'];
            }
            if (isset($taskData['week2_status'])) {
                $task->week2_status = $taskData['week2_status'];
            }
            if (array_key_exists('week1_remarks', $taskData)) {
                $task->week1_remarks = $taskData['week1_remarks'];
            }
            if (array_key_exists('week2_remarks', $taskData)) {
                $task->week2_remarks = $taskData['week2_remarks'];
            }
            $task->save();
        }

        return response()->json(['success' => true, 'message' => 'Saved successfully.']);
    }

    // Get task statuses for dashboard modal
    public function getTaskStatuses(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date'
        ]);

        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        // Get all task checklists that fall within the date range
        $taskChecklists = TaskChecklist::whereBetween('week_start_date', [$startDate, $endDate])
            ->orWhere(function($query) use ($startDate, $endDate) {
                $query->where('week_start_date', '<=', $startDate)
                      ->whereRaw('DATE_ADD(week_start_date, INTERVAL 13 DAY) >= ?', [$endDate]);
            })
            ->get();

        $statuses = [];

        foreach ($taskChecklists as $task) {
            $taskDate = Carbon::parse($task->week_start_date);

            // Use task_id field if available, otherwise create frontend ID from database id
            $taskIdentifier = $task->task_id ?? $this->generateFrontendTaskId($task);

            // Process week1 (days 0-6)
            if ($task->week1_status) {
                for ($i = 0; $i < 7; $i++) {
                    $currentDate = $taskDate->copy()->addDays($i);
                    if ($currentDate->between($startDate, $endDate)) {
                        $statuses[] = [
                            'task_id' => $taskIdentifier,
                            'task_date' => $currentDate->format('Y-m-d'),
                            'status' => $task->week1_status[$i] ?? null,
                            'status_display' => $this->getStatusDisplay($task->week1_status[$i] ?? null),
                            'remarks' => $task->week1_remarks
                        ];
                    }
                }
            }

            // Process week2 (days 7-13)
            if ($task->week2_status) {
                for ($i = 0; $i < 7; $i++) {
                    $currentDate = $taskDate->copy()->addDays($i + 7);
                    if ($currentDate->between($startDate, $endDate)) {
                        $statuses[] = [
                            'task_id' => $taskIdentifier,
                            'task_date' => $currentDate->format('Y-m-d'),
                            'status' => $task->week2_status[$i] ?? null,
                            'status_display' => $this->getStatusDisplay($task->week2_status[$i] ?? null),
                            'remarks' => $task->week2_remarks
                        ];
                    }
                }
            }
        }

        return response()->json($statuses);
    }

    // Helper method to get status display value
    private function getStatusDisplay($status)
    {
        switch ($status) {
            case 'check':
                return '✓';
            case 'wrong':
                return '✗';
            default:
                return '';
        }
    }

    // Clear all Friday statuses
    public function clearFridayStatuses(Request $request)
    {
        try {
            $date = $request->input('date', now()->format('Y-m-d'));

            // Clear from task_checklist_statuses table
            $fridayDate = Carbon::parse($date)->startOfWeek()->addDays(4); // Friday is 4 days from Monday
            TaskChecklistStatus::where('task_date', $fridayDate->format('Y-m-d'))->delete();

            // Clear from task_checklists table (JSON arrays)
            $weekStart = Carbon::parse($date)->startOfWeek();
            $taskChecklists = TaskChecklist::where('week_start_date', $weekStart->format('Y-m-d'))->get();

            foreach ($taskChecklists as $taskChecklist) {
                // Clear Friday (index 4) from week1_status
                if ($taskChecklist->week1_status) {
                    $week1Status = $taskChecklist->week1_status;
                    $week1Status[4] = null; // Friday is index 4 (0=Mon, 1=Tue, 2=Wed, 3=Thu, 4=Fri)
                    $taskChecklist->week1_status = $week1Status;
                }

                // Clear Friday (index 4) from week2_status if needed
                if ($taskChecklist->week2_status) {
                    $week2Status = $taskChecklist->week2_status;
                    $week2Status[4] = null; // Friday is index 4
                    $taskChecklist->week2_status = $week2Status;
                }

                $taskChecklist->save();
            }

            return response()->json([
                'success' => true,
                'message' => 'All Friday statuses cleared successfully',
                'cleared_date' => $fridayDate->format('Y-m-d')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error clearing Friday statuses: ' . $e->getMessage()
            ], 500);
        }
    }

    // Get task statuses for a specific date (enhanced for date-specific persistence)
    public function getTaskStatusesForDate(Request $request)
    {
        $request->validate([
            'date' => 'required|date'
        ]);

        $selectedDate = Carbon::parse($request->input('date'));
        $weekStart = $selectedDate->copy()->startOfWeek(); // Monday of the selected week

        Log::info("� [PERMANENT-LOAD] Getting PERMANENT task statuses for date: {$selectedDate->format('Y-m-d')}, week start: {$weekStart->format('Y-m-d')}");

        // Get or create task checklists for this week
        $this->ensureTaskChecklistsExist($weekStart->format('Y-m-d'));

        // Get ALL task checklists for this week with FORCE reload
        $taskChecklists = TaskChecklist::where('week_start_date', $weekStart->format('Y-m-d'))
            ->orderBy('id')
            ->get();

        Log::info("📊 [PERMANENT-LOAD] Found {$taskChecklists->count()} task checklists for week {$weekStart->format('Y-m-d')}");

        $statuses = [];
        $statusCount = 0;

        foreach ($taskChecklists as $task) {
            $taskDate = Carbon::parse($task->week_start_date);

            // Use task_id field if available, otherwise create frontend ID from database id
            $taskIdentifier = $task->task_id ?? $this->generateFrontendTaskId($task);

            // Return statuses for the entire week that contains the selected date
            // Process week1 (days 0-6)
            if ($task->week1_status) {
                for ($i = 0; $i < 7; $i++) {
                    $currentDate = $taskDate->copy()->addDays($i);
                    if ($task->week1_status[$i] !== null) {
                        $statuses[] = [
                            'task_id' => $taskIdentifier,
                            'task_date' => $currentDate->format('Y-m-d'),
                            'status' => $task->week1_status[$i],
                            'status_display' => $this->getStatusDisplay($task->week1_status[$i]),
                            'remarks' => $task->week1_remarks,
                            'day_index' => $i,
                            'day_name' => $currentDate->format('l'),
                            'week_start' => $weekStart->format('Y-m-d'),
                            'is_selected_date' => $currentDate->format('Y-m-d') === $selectedDate->format('Y-m-d'),
                            'saved_timestamp' => $task->updated_at ? $task->updated_at->format('Y-m-d H:i:s') : null,
                            'permanent_status' => true,
                            'never_disappears' => true
                        ];
                        $statusCount++;

                        Log::info("� [PERMANENT-LOAD] Found PERMANENT status for task {$taskIdentifier} on {$currentDate->format('Y-m-d')}: {$task->week1_status[$i]}");
                    }
                }
            }

            // Process week2 (days 7-13) if needed
            if ($task->week2_status) {
                for ($i = 0; $i < 7; $i++) {
                    $currentDate = $taskDate->copy()->addDays($i + 7);
                    if ($task->week2_status[$i] !== null) {
                        $statuses[] = [
                            'task_id' => $taskIdentifier,
                            'task_date' => $currentDate->format('Y-m-d'),
                            'status' => $task->week2_status[$i],
                            'status_display' => $this->getStatusDisplay($task->week2_status[$i]),
                            'remarks' => $task->week2_remarks,
                            'day_index' => $i + 7,
                            'day_name' => $currentDate->format('l'),
                            'week_start' => $weekStart->format('Y-m-d'),
                            'is_selected_date' => $currentDate->format('Y-m-d') === $selectedDate->format('Y-m-d'),
                            'saved_timestamp' => $task->updated_at ? $task->updated_at->format('Y-m-d H:i:s') : null,
                            'permanent_status' => true,
                            'never_disappears' => true
                        ];
                        $statusCount++;

                        Log::info("� [PERMANENT-LOAD] Found PERMANENT status for task {$taskIdentifier} on {$currentDate->format('Y-m-d')}: {$task->week2_status[$i]}");
                    }
                }
            }
        }

        Log::info("🔒 [PERMANENT-LOAD] Returning {$statusCount} PERMANENT task statuses for date: {$selectedDate->format('Y-m-d')}");

        return response()->json([
            'success' => true,
            'selected_date' => $selectedDate->format('Y-m-d'),
            'week_start' => $weekStart->format('Y-m-d'),
            'week_end' => $weekStart->copy()->addDays(6)->format('Y-m-d'),
            'statuses' => $statuses,
            'total_statuses' => $statusCount,
            'message' => "🔒 Loaded {$statusCount} PERMANENT statuses for {$selectedDate->format('Y-m-d')}",
            'permanent_persistence' => true,
            'never_disappears' => true,
            'always_restored' => true,
            'load_timestamp' => now()->format('Y-m-d H:i:s')
        ]);
    }

    // Get all task statuses for debugging and verification
    public function getAllTaskStatuses(Request $request)
    {
        try {
            Log::info("🔍 Getting ALL task statuses for debugging");

            // Get all task checklists with any status data
            $taskChecklists = TaskChecklist::whereNotNull('week_start_date')
                ->where(function($query) {
                    $query->whereNotNull('week1_status')
                          ->orWhereNotNull('week2_status');
                })
                ->orderBy('week_start_date', 'desc')
                ->get();

            $allStatuses = [];
            $totalCount = 0;

            foreach ($taskChecklists as $task) {
                $taskDate = Carbon::parse($task->week_start_date);
                $taskIdentifier = $task->task_id ?? $this->generateFrontendTaskId($task);

                // Process week1 statuses
                if ($task->week1_status && is_array($task->week1_status)) {
                    for ($i = 0; $i < 7; $i++) {
                        $currentDate = $taskDate->copy()->addDays($i);
                        $status = $task->week1_status[$i] ?? null;

                        if ($status !== null) {
                            $allStatuses[] = [
                                'task_id' => $taskIdentifier,
                                'task_date' => $currentDate->format('Y-m-d'),
                                'status' => $status,
                                'status_display' => $this->getStatusDisplay($status),
                                'day_name' => $currentDate->format('l'),
                                'week_start' => $taskDate->format('Y-m-d'),
                                'week_number' => 1,
                                'day_index' => $i,
                                'saved_timestamp' => $task->updated_at ? $task->updated_at->format('Y-m-d H:i:s') : null
                            ];
                            $totalCount++;
                        }
                    }
                }

                // Process week2 statuses
                if ($task->week2_status && is_array($task->week2_status)) {
                    for ($i = 0; $i < 7; $i++) {
                        $currentDate = $taskDate->copy()->addDays($i + 7);
                        $status = $task->week2_status[$i] ?? null;

                        if ($status !== null) {
                            $allStatuses[] = [
                                'task_id' => $taskIdentifier,
                                'task_date' => $currentDate->format('Y-m-d'),
                                'status' => $status,
                                'status_display' => $this->getStatusDisplay($status),
                                'day_name' => $currentDate->format('l'),
                                'week_start' => $taskDate->format('Y-m-d'),
                                'week_number' => 2,
                                'day_index' => $i + 7,
                                'saved_timestamp' => $task->updated_at ? $task->updated_at->format('Y-m-d H:i:s') : null
                            ];
                            $totalCount++;
                        }
                    }
                }
            }

            Log::info("✅ Found {$totalCount} total statuses across all dates");

            return response()->json([
                'success' => true,
                'total_statuses' => $totalCount,
                'statuses' => $allStatuses,
                'message' => "Found {$totalCount} total statuses",
                'debug_info' => [
                    'total_task_checklists' => $taskChecklists->count(),
                    'current_time' => now()->format('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            Log::error("❌ Error getting all task statuses: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving statuses: ' . $e->getMessage()
            ], 500);
        }
    }

    // Force refresh all task statuses for a specific date (debugging method)
    public function forceRefreshStatuses(Request $request)
    {
        $request->validate([
            'date' => 'required|date'
        ]);

        $selectedDate = Carbon::parse($request->input('date'));

        Log::info("🔄 Force refreshing statuses for date: {$selectedDate->format('Y-m-d')}");

        try {
            // Get fresh data from database
            $response = $this->getTaskStatusesForDate($request);
            $data = $response->getData(true);

            if ($data['success']) {
                Log::info("✅ Force refresh successful: {$data['total_statuses']} statuses found");

                return response()->json([
                    'success' => true,
                    'message' => "✅ Force refreshed {$data['total_statuses']} statuses for {$selectedDate->format('Y-m-d')}",
                    'statuses' => $data['statuses'],
                    'total_statuses' => $data['total_statuses'],
                    'refresh_timestamp' => now()->format('Y-m-d H:i:s')
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to refresh statuses',
                    'error' => $data['message'] ?? 'Unknown error'
                ]);
            }

        } catch (\Exception $e) {
            Log::error("❌ Error force refreshing statuses: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error refreshing statuses: ' . $e->getMessage()
            ], 500);
        }
    }

    // Get task history for View History feature
    public function getTaskHistory(Request $request)
    {
        try {
            Log::info("Getting task history");

            // Get all task checklists with statuses, ordered by most recent first
            $taskChecklists = TaskChecklist::whereNotNull('week_start_date')
                ->orderBy('week_start_date', 'desc')
                ->get();

            $history = [];

            foreach ($taskChecklists as $task) {
                $taskDate = Carbon::parse($task->week_start_date);
                $taskIdentifier = $task->task_id ?? $this->generateFrontendTaskId($task);

                // Get meaningful task description and category
                $taskInfo = $this->getTaskDescriptionAndCategory($taskIdentifier);

                // Process week1 statuses (days 0-6)
                if ($task->week1_status && is_array($task->week1_status)) {
                    for ($i = 0; $i < 7; $i++) {
                        if ($task->week1_status[$i] !== null) {
                            $currentDate = $taskDate->copy()->addDays($i);
                            $dayName = $currentDate->format('l'); // Full day name (Monday, Tuesday, etc.)

                            $history[] = [
                                'task_id' => $taskIdentifier,
                                'task_description' => $taskInfo['description'],
                                'task_category' => $taskInfo['category'],
                                'status' => $task->week1_status[$i],
                                'status_display' => $this->getStatusDisplay($task->week1_status[$i]),
                                'date' => $currentDate->format('Y-m-d'),
                                'formatted_date' => $currentDate->format('F j, Y'),
                                'day_name' => $dayName,
                                'full_date_display' => $currentDate->format('F j, Y') . ' - ' . $dayName,
                                'remarks' => $task->week1_remarks,
                                'week_start' => $task->week_start_date,
                                'created_at' => $task->created_at,
                                'updated_at' => $task->updated_at
                            ];
                        }
                    }
                }

                // Process week2 statuses (days 7-13)
                if ($task->week2_status && is_array($task->week2_status)) {
                    for ($i = 0; $i < 7; $i++) {
                        if ($task->week2_status[$i] !== null) {
                            $currentDate = $taskDate->copy()->addDays($i + 7);
                            $dayName = $currentDate->format('l'); // Full day name

                            $history[] = [
                                 'task_id' => $taskIdentifier,
                                'task_description' => $taskInfo['description'],
                                'task_category' => $taskInfo['category'],
                                'status' => $task->week2_status[$i],
                                'status_display' => $this->getStatusDisplay($task->week2_status[$i]),
                                'date' => $currentDate->format('Y-m-d'),
                                'formatted_date' => $currentDate->format('F j, Y'),
                                'day_name' => $dayName,
                                'full_date_display' => $currentDate->format('F j, Y') . ' - ' . $dayName,
                                'remarks' => $task->week2_remarks,
                                'week_start' => $task->week_start_date,
                                'created_at' => $task->created_at,
                                'updated_at' => $task->updated_at
                            ];
                        }
                    }
                }
            }

            // Sort by date (most recent first)
            usort($history, function($a, $b) {
                return strtotime($b['date']) - strtotime($a['date']);
            });

            Log::info("Found " . count($history) . " history entries");

            return response()->json([
                'success' => true,
                'history' => $history,
                'total_entries' => count($history)
            ]);

        } catch (\Exception $e) {
            Log::error("Error getting task history: " . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving task history: ' . $e->getMessage()
            ], 500);
        }
    }

    // Helper method to get meaningful task descriptions and categories
    private function getTaskDescriptionAndCategory($taskId)
    {
        // Map task IDs to meaningful descriptions and categories
        $taskMappings = [
            // Kitchen Tasks (Page 1)
            'kitchen1' => ['description' => 'Assigned members wake up on time and completed their tasks as scheduled', 'category' => 'Kitchen'],
            'kitchen2' => ['description' => 'The students assigned to cook the rice completed the task properly', 'category' => 'Kitchen'],
            'kitchen3' => ['description' => 'The students assigned to cook the viand completed the task properly', 'category' => 'Kitchen'],
            'kitchen4' => ['description' => 'The students assigned to assist the cook carried out their duties diligently', 'category' => 'Kitchen'],
            'kitchen5' => ['description' => 'Ingredients were prepared ahead of time', 'category' => 'Kitchen'],
            'kitchen6' => ['description' => 'Properly washed the burner', 'category' => 'Kitchen'],
            'kitchen7' => ['description' => 'Wiped and arranged the dishes', 'category' => 'Kitchen'],
            'kitchen8' => ['description' => 'Cleaned the canal after cooking', 'category' => 'Kitchen'],
            'kitchen9' => ['description' => 'Arranged the freezer', 'category' => 'Kitchen'],

            // Kitchen General Cleaning
            'cleaning1' => ['description' => 'Cleaned the drainage canals', 'category' => 'Kitchen Cleaning'],
            'cleaning2' => ['description' => 'Brushed and rinsed the floor of the dishwashing area', 'category' => 'Kitchen Cleaning'],
            'cleaning3' => ['description' => 'Brushed the sink', 'category' => 'Kitchen Cleaning'],
            'cleaning4' => ['description' => 'Washed the barrel container', 'category' => 'Kitchen Cleaning'],
            'cleaning5' => ['description' => 'Cleaned and arranged the storage cabinet', 'category' => 'Kitchen Cleaning'],
            'cleaning6' => ['description' => 'Wiped the cabinets (No dust/stains inside and outside the cabinet)', 'category' => 'Kitchen Cleaning'],

            // Dishwashing Tasks (Page 2)
            'dishwashing1' => ['description' => 'Cleaned the drainage canals', 'category' => 'Dishwashing'],
            'dishwashing2' => ['description' => 'Brushed and rinsed the floor of the dishwashing area', 'category' => 'Dishwashing'],
            'dishwashing3' => ['description' => 'Brushed the sink', 'category' => 'Dishwashing'],
            'dishwashing4' => ['description' => 'Washed the barrel container', 'category' => 'Dishwashing'],
            'dishwashing5' => ['description' => 'Cleaned and arranged the storage cabinet', 'category' => 'Dishwashing'],
            'dishwashing6' => ['description' => 'Wiped the cabinets (No dust/stains inside and outside the cabinet)', 'category' => 'Dishwashing'],

            // Dishwashing General Cleaning
            'dishwashing_cleaning1' => ['description' => 'Cleaned the drainage canals', 'category' => 'Dishwashing Cleaning'],
            'dishwashing_cleaning2' => ['description' => 'Brushed and rinsed the floor of the dishwashing area', 'category' => 'Dishwashing Cleaning'],
            'dishwashing_cleaning3' => ['description' => 'Brushed the sink', 'category' => 'Dishwashing Cleaning'],
            'dishwashing_cleaning4' => ['description' => 'Washed the barrel container', 'category' => 'Dishwashing Cleaning'],
            'dishwashing_cleaning5' => ['description' => 'Cleaned and arranged the storage cabinet', 'category' => 'Dishwashing Cleaning'],
            'dishwashing_cleaning6' => ['description' => 'Wiped the cabinets (No dust/stains inside and outside the cabinet)', 'category' => 'Dishwashing Cleaning'],

            // Dining Tasks (Page 3)
            'dining1' => ['description' => 'Tables and chairs are properly arranged', 'category' => 'Dining'],
            'dining2' => ['description' => 'Floor is clean and mopped', 'category' => 'Dining'],
            'dining3' => ['description' => 'Windows and doors are clean', 'category' => 'Dining'],
            'dining4' => ['description' => 'Trash bins are emptied and clean', 'category' => 'Dining'],
            'dining5' => ['description' => 'Dining area is well-ventilated', 'category' => 'Dining'],
            'dining6' => ['description' => 'Food serving area is sanitized', 'category' => 'Dining'],

            // Laundry Tasks (Page 4)
            'laundry1' => ['description' => 'Washing machines are clean and functional', 'category' => 'Laundry'],
            'laundry2' => ['description' => 'Clothes are properly sorted', 'category' => 'Laundry'],
            'laundry3' => ['description' => 'Drying area is organized', 'category' => 'Laundry'],
            'laundry4' => ['description' => 'Laundry supplies are stocked', 'category' => 'Laundry'],
            'laundry5' => ['description' => 'Floor and drainage are clean', 'category' => 'Laundry'],
            'laundry6' => ['description' => 'Equipment maintenance completed', 'category' => 'Laundry'],

            // Security Tasks (Page 5)
            'security1' => ['description' => 'All entry points are secured', 'category' => 'Security'],
            'security2' => ['description' => 'Security rounds completed', 'category' => 'Security'],
            'security3' => ['description' => 'Emergency equipment checked', 'category' => 'Security'],
            'security4' => ['description' => 'Visitor log maintained', 'category' => 'Security'],
            'security5' => ['description' => 'CCTV systems operational', 'category' => 'Security'],
            'security6' => ['description' => 'Safety protocols followed', 'category' => 'Security'],
        ];

        // Return the mapping if found, otherwise return a default
        if (isset($taskMappings[$taskId])) {
            return $taskMappings[$taskId];
        }

        // Default fallback
        return [
            'description' => "Task: {$taskId}",
            'category' => 'General'
        ];
    }

    private function initializeDefaultTasks($weekStart)
    {
        // Check if tasks already exist for this week
        $existingTasks = TaskChecklist::where('week_start_date', $weekStart)->count();

        if ($existingTasks > 0) {
            return; // Tasks already exist
        }

        $defaultTasks = [
            // Kitchen Tasks
            ['category' => 'KITCHEN', 'description' => 'Assigned members wake up on time and completed their tasks as scheduled.'],
            ['category' => 'KITCHEN', 'description' => 'The students assigned to cook the rice completed the task properly.'],
            ['category' => 'KITCHEN', 'description' => 'The students assigned to cook the viand completed the task properly.'],
            ['category' => 'KITCHEN', 'description' => 'The students assigned to assist the cook carried out their duties diligently.'],
            ['category' => 'KITCHEN', 'description' => 'Ingredients were prepared ahead of time.'],
            ['category' => 'KITCHEN', 'description' => 'The kitchen was properly cleaned after cooking.'],
            ['category' => 'KITCHEN', 'description' => 'The food was transferred from the kitchen to the center.'],
            ['category' => 'KITCHEN', 'description' => 'Proper inventory of stocks was maintained and deliveries were handled appropriately.'],
            ['category' => 'KITCHEN', 'description' => 'Water and food supplies were regularly monitored and stored in the proper place.'],
            ['category' => 'KITCHEN', 'description' => 'Receipts, kitchen phones, and keys were safely stored.'],
            ['category' => 'KITCHEN', 'description' => 'Kitchen utensils were properly stored.'],
            ['category' => 'KITCHEN', 'description' => 'The stove was turned off after cooking.'],
            ['category' => 'KITCHEN', 'description' => 'Properly disposed of the garbage.'],

            // General Cleaning Tasks
            ['category' => 'GENERAL CLEANING', 'description' => 'Properly washed the burner.'],
            ['category' => 'GENERAL CLEANING', 'description' => 'Wiped and arranged the chiller.'],
            ['category' => 'GENERAL CLEANING', 'description' => 'Cleaned the canal after cooking.'],
            ['category' => 'GENERAL CLEANING', 'description' => 'Arranged the freezer.'],
        ];

        foreach ($defaultTasks as $task) {
            TaskChecklist::create([
                'task_category' => $task['category'],
                'task_description' => $task['description'],
                'week_start_date' => $weekStart,
                'week1_status' => array_fill(0, 7, null),
                'week2_status' => array_fill(0, 7, null),
            ]);
        }
    }

    private function initializeDefaultTasksForWeek($weekStart)
    {
        // Check if tasks already exist for this week
        $existingTasks = TaskChecklist::where('week_start_date', $weekStart->format('Y-m-d'))->count();

        if ($existingTasks > 0) {
            return; // Tasks already exist
        }

        $defaultTasks = [
            // Kitchen Tasks
            ['category' => 'KITCHEN', 'description' => 'Assigned members wake up on time and completed their tasks as scheduled.'],
            ['category' => 'KITCHEN', 'description' => 'The students assigned to cook the rice completed the task properly.'],
            ['category' => 'KITCHEN', 'description' => 'The students assigned to cook the viand completed the task properly.'],
            ['category' => 'KITCHEN', 'description' => 'The students assigned to assist the cook carried out their duties diligently.'],
            ['category' => 'KITCHEN', 'description' => 'Ingredients were prepared ahead of time.'],
            ['category' => 'KITCHEN', 'description' => 'The kitchen was properly cleaned after cooking.'],
            ['category' => 'KITCHEN', 'description' => 'The food was transferred from the kitchen to the center.'],
            ['category' => 'KITCHEN', 'description' => 'Proper inventory of stocks was maintained and deliveries were handled appropriately.'],
            ['category' => 'KITCHEN', 'description' => 'Water and food supplies were regularly monitored and stored in the proper place.'],
            ['category' => 'KITCHEN', 'description' => 'Receipts, kitchen phones, and keys were safely stored.'],
            ['category' => 'KITCHEN', 'description' => 'Kitchen utensils were properly stored.'],
            ['category' => 'KITCHEN', 'description' => 'The stove was turned off after cooking.'],
            ['category' => 'KITCHEN', 'description' => 'Properly disposed of the garbage.'],

            // General Cleaning Tasks
            ['category' => 'GENERAL CLEANING', 'description' => 'Properly washed the burner.'],
            ['category' => 'GENERAL CLEANING', 'description' => 'Wiped and arranged the chiller.'],
            ['category' => 'GENERAL CLEANING', 'description' => 'Cleaned the canal after cooking.'],
            ['category' => 'GENERAL CLEANING', 'description' => 'Arranged the freezer.'],
        ];

        foreach ($defaultTasks as $task) {
            TaskChecklist::create([
                'task_category' => $task['category'],
                'task_description' => $task['description'],
                'week_start_date' => $weekStart->format('Y-m-d'),
                'week1_status' => array_fill(0, 7, null),
                'week2_status' => array_fill(0, 7, null),
            ]);
        }
    }

    // Find or create task record from frontend task ID
    private function findOrCreateTaskFromFrontendId($taskId, $date)
    {
        // First try to find existing task by task_id field
        $task = TaskChecklist::where('task_id', $taskId)->first();

        if ($task) {
            return $task;
        }

        // If not found, create new task record
        $taskInfo = $this->getTaskInfoFromFrontendId($taskId);
        if (!$taskInfo || !isset($taskInfo['category']) || !isset($taskInfo['description'])) {
            Log::error("Invalid task info returned for task ID: {$taskId}");
            return null;
        }

        // Calculate week start date from the given date
        $taskDate = Carbon::parse($date);
        $weekStart = $taskDate->startOfWeek();

        try {
            $task = TaskChecklist::create([
                'task_id' => $taskId,
                'task_category' => $taskInfo['category'],
                'task_description' => $taskInfo['description'],
                'week_start_date' => $weekStart->format('Y-m-d'),
                'week1_status' => array_fill(0, 7, null),
                'week2_status' => array_fill(0, 7, null),
            ]);

            return $task;
        } catch (\Exception $e) {
            \Log::error('Failed to create task checklist: ' . $e->getMessage());
            return null;
        }
    }

    // Get task info from frontend task ID
    private function getTaskInfoFromFrontendId($taskId)
    {
        // SIMPLE DYNAMIC GENERATION - NO PREDEFINED MAPPINGS
        Log::info("Getting task info for: {$taskId}");

        // Ensure we always return a valid result
        if (empty($taskId)) {
            Log::error("Empty task ID provided");
            return [
                'category' => 'GENERAL',
                'description' => 'Unknown task'
            ];
        }

        // Determine category based on task ID prefix
        $category = 'GENERAL CLEANING'; // Default category

        if (strpos($taskId, 'kitchen') === 0) {
            $category = 'KITCHEN';
        } elseif (strpos($taskId, 'dishwashing') === 0) {
            $category = 'DISHWASHING';
        } elseif (strpos($taskId, 'cleaning') === 0) {
            $category = 'GENERAL CLEANING';
        } elseif (strpos($taskId, 'generalcleaning') === 0) {
            $category = 'GENERAL CLEANING';
        } elseif (strpos($taskId, 'dining') === 0) {
            $category = 'DINING';
        } elseif (strpos($taskId, 'groundfloor') === 0) {
            $category = 'GROUND FLOOR';
        }

        Log::info("Task {$taskId} mapped to category: {$category}");

        // Always return a valid mapping
        return [
            'category' => $category,
            'description' => "Task {$taskId}"
        ];
    }

    // Generate frontend task ID from database record
    private function generateFrontendTaskId($task)
    {
        // Create a frontend ID based on category and database ID
        $category = strtolower(str_replace(' ', '', $task->task_category));
        return $category . $task->id;
    }

    // Ensure task checklists exist for a given week
    private function ensureTaskChecklistsExist($weekStart)
    {
        // Check if tasks already exist for this week
        $existingTasks = TaskChecklist::where('week_start_date', $weekStart)->count();

        if ($existingTasks > 0) {
            Log::info("Tasks already exist for week: {$weekStart}");
            return; // Tasks already exist
        }

        Log::info("Creating default tasks for week: {$weekStart}");

        $defaultTasks = [
            // Kitchen Tasks
            ['category' => 'KITCHEN', 'description' => 'Assigned members wake up on time and completed their tasks as scheduled.', 'task_id' => 'kitchen1'],
            ['category' => 'KITCHEN', 'description' => 'The students assigned to cook the rice completed the task properly.', 'task_id' => 'kitchen2'],
            ['category' => 'KITCHEN', 'description' => 'The students assigned to cook the viand completed the task properly.', 'task_id' => 'kitchen3'],
            ['category' => 'KITCHEN', 'description' => 'The students assigned to assist the cook carried out their duties diligently.', 'task_id' => 'kitchen4'],
            ['category' => 'KITCHEN', 'description' => 'Ingredients were prepared ahead of time.', 'task_id' => 'kitchen5'],
            ['category' => 'KITCHEN', 'description' => 'The kitchen was properly cleaned after cooking.', 'task_id' => 'kitchen6'],

            // Dishwashing Tasks
            ['category' => 'DISHWASHING', 'description' => 'Assigned members wake up on time and completed their tasks as scheduled.', 'task_id' => 'dishwashing1'],
            ['category' => 'DISHWASHING', 'description' => 'Dishes were properly washed and cleaned.', 'task_id' => 'dishwashing2'],

            // General Cleaning Tasks
            ['category' => 'GENERAL CLEANING', 'description' => 'Properly washed the burner.', 'task_id' => 'generalcleaning1'],
            ['category' => 'GENERAL CLEANING', 'description' => 'Wiped and arranged the chiller.', 'task_id' => 'generalcleaning2'],
            ['category' => 'GENERAL CLEANING', 'description' => 'Cleaned the canal after cooking.', 'task_id' => 'generalcleaning3'],
            ['category' => 'GENERAL CLEANING', 'description' => 'Arranged the freezer.', 'task_id' => 'generalcleaning4'],
        ];

        foreach ($defaultTasks as $task) {
            TaskChecklist::create([
                'task_id' => $task['task_id'],
                'task_category' => $task['category'],
                'task_description' => $task['description'],
                'week_start_date' => $weekStart,
                'week1_status' => array_fill(0, 7, null),
                'week2_status' => array_fill(0, 7, null),
            ]);
        }

        Log::info("Created " . count($defaultTasks) . " default tasks for week: {$weekStart}");
    }

    // ========== DYNAMIC CAPSTONE TASKING SYSTEM ==========

    /**
     * Set student counts for allocation (Input system for coordinators/teachers)
     */
    public function setStudentCounts(Request $request)
    {
        $request->validate([
            'date' => 'required|date',
            'batch_2025_male' => 'required|integer|min:0',
            'batch_2025_female' => 'required|integer|min:0',
            'batch_2026_male' => 'required|integer|min:0',
            'batch_2026_female' => 'required|integer|min:0',
        ]);

        $date = $request->input('date');

        // Update or create student allocations
        $allocations = [
            ['batch_year' => '2025', 'gender' => 'male', 'total_count' => $request->input('batch_2025_male')],
            ['batch_year' => '2025', 'gender' => 'female', 'total_count' => $request->input('batch_2025_female')],
            ['batch_year' => '2026', 'gender' => 'male', 'total_count' => $request->input('batch_2026_male')],
            ['batch_year' => '2026', 'gender' => 'female', 'total_count' => $request->input('batch_2026_female')],
        ];

        foreach ($allocations as $allocation) {
            StudentAllocation::updateOrCreate(
                [
                    'batch_year' => $allocation['batch_year'],
                    'gender' => $allocation['gender'],
                    'allocation_date' => $date
                ],
                [
                    'total_count' => $allocation['total_count'],
                    'allocated_count' => 0 // Reset allocations when counts change
                ]
            );
        }

        return response()->json([
            'success' => true,
            'message' => 'Student counts updated successfully',
            'date' => $date
        ]);
    }

    /**
     * Get dynamic tasks with matrix layout
     */
    public function getDynamicTasksMatrix(Request $request)
    {
        $date = $request->input('date', now()->format('Y-m-d'));

        // Get all dynamic tasks grouped by category
        $tasksByCategory = DynamicTask::with('category')
            ->active()
            ->ordered()
            ->get()
            ->groupBy('category.name');

        // Get student allocations for the date
        $allocations = StudentAllocation::forDate($date)->get()->keyBy(function($item) {
            return $item->batch_year . '_' . $item->gender;
        });

        // Get current assignments for the date
        $assignments = GeneralTaskAssignment::with('task')
            ->forDate($date)
            ->active()
            ->get()
            ->groupBy('task_id');

        return response()->json([
            'success' => true,
            'date' => $date,
            'tasks_by_category' => $tasksByCategory,
            'student_allocations' => $allocations,
            'current_assignments' => $assignments
        ]);
    }

    /**
     * Assign students to dynamic task
     */
    public function assignStudentsToTask(Request $request)
    {
        $request->validate([
            'task_id' => 'required|exists:dynamic_tasks,id',
            'batch_year' => 'required|in:2025,2026',
            'gender' => 'required|in:male,female',
            'student_count' => 'required|integer|min:1',
            'assignment_date' => 'required|date',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i',
            'notes' => 'nullable|string|max:1000'
        ]);

        $task = DynamicTask::findOrFail($request->input('task_id'));
        $batchYear = $request->input('batch_year');
        $gender = $request->input('gender');
        $studentCount = $request->input('student_count');
        $assignmentDate = $request->input('assignment_date');

        // Check if enough students are available
        $allocation = StudentAllocation::forBatchGender($batchYear, $gender)
            ->forDate($assignmentDate)
            ->first();

        if (!$allocation || $allocation->available_count < $studentCount) {
            return response()->json([
                'success' => false,
                'message' => "Not enough {$gender} students available in Batch {$batchYear}. Available: " . ($allocation ? $allocation->available_count : 0)
            ]);
        }

        // Create assignment
        $assignment = GeneralTaskAssignment::create([
            'task_id' => $task->id,
            'batch_year' => $batchYear,
            'gender' => $gender,
            'assigned_count' => $studentCount,
            'assignment_date' => $assignmentDate,
            'start_time' => $request->input('start_time'),
            'end_time' => $request->input('end_time'),
            'status' => 'pending',
            'notes' => $request->input('notes')
        ]);

        // Update allocation
        $allocation->allocateStudents($studentCount);

        return response()->json([
            'success' => true,
            'message' => "Successfully assigned {$studentCount} {$gender} students from Batch {$batchYear} to task: {$task->name}",
            'assignment' => $assignment->load('task'),
            'remaining_students' => $allocation->fresh()->available_count
        ]);
    }
}