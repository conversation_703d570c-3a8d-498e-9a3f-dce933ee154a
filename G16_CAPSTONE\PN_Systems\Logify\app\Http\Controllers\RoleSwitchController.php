<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class RoleSwitchController extends Controller
{

    public function switchMode(Request $request)
    {
        $mode = $request->input('switch_mode');

        if ($mode === 'monitor') {
            session(['user_mode' => 'monitor']);
            return redirect()->route('monitor.dashboard');
        }

        session(['user_mode' => 'educator']);
        return redirect()->route('educator.dashboard');
    }

    /**
     * Get current user mode
     */
    public function getCurrentMode()
    {
        $user = Auth::user();

        if ($user->user_role !== 'educator') {
            return response()->json([
                'success' => false,
                'message' => 'User does not have mode switching capability'
            ], 403);
        }

        // Default to educator mode if no mode is set in session
        $currentMode = session('user_mode', 'educator');

        return response()->json([
            'success' => true,
            'mode' => $currentMode,
            'user' => [
                'name' => $user->user_fname . ' ' . $user->user_lname,
                'role' => $user->user_role
            ]
        ]);
    }

    /**
     * Helper method to check if user is in monitor mode
     */
    public static function isInMonitorMode()
    {
        $user = Auth::user();

        if (!$user || $user->user_role !== 'educator') {
            return false;
        }

        return session('user_mode') === 'monitor';
    }

    /**
     * Helper method to get current mode display name
     */
    public static function getCurrentModeDisplay()
    {
        $user = Auth::user();

        if (!$user || $user->user_role !== 'educator') {
            return ucfirst($user->user_role ?? 'guest');
        }

        $mode = session('user_mode', 'educator');
        return ucfirst($mode);
    }
}
