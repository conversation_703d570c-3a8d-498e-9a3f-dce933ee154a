<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Room Task History</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            padding: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .room-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .room-card:hover {
            transform: translateY(-5px);
        }
        
        .room-title {
            color: #4a5568;
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }
        
        .occupants-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .occupant-badge {
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            margin: 3px;
            display: inline-block;
            font-size: 0.9rem;
        }
        
        .current-student {
            background: #28a745 !important;
            font-weight: bold;
        }
        
        .tasks-section {
            margin-top: 20px;
        }
        
        .day-header {
            background: #667eea;
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .task-item {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 12px 15px;
            margin-bottom: 8px;
            border-radius: 0 8px 8px 0;
        }
        
        .task-area {
            font-weight: bold;
            color: #4a5568;
        }
        
        .task-desc {
            color: #6c757d;
            margin-top: 5px;
        }
        
        .no-rooms-message {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        
        .back-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }
        
        .history-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
        }
        
        .history-item {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #2196f3;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        /* Calendar Styles */
        .calendar-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .calendar-header {
            text-align: center;
            margin-bottom: 20px;
        }

        .week-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .nav-button {
            background: #667eea;
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-button:hover {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .week-display {
            font-size: 1.2rem;
            font-weight: bold;
            color: #4a5568;
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }

        .day-card {
            background: #f8f9fa;
            border: 2px solid transparent;
            border-radius: 10px;
            padding: 15px 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .day-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .day-card.selected {
            background: #667eea;
            color: white;
            border-color: #5a67d8;
        }

        .day-card.today {
            border-color: #28a745;
            background: #d4edda;
        }

        .day-card.today.selected {
            background: #28a745;
            color: white;
        }

        .day-name {
            font-size: 0.9rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .day-number {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .selected-day-tasks {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header-card">
            <h1 class="mb-3">
                <i class="bi bi-house-door me-2"></i>
                My Room Task History
            </h1>
            <p class="text-muted mb-3">Welcome, <strong>{{ $studentName }}</strong>!</p>
            <p class="text-muted">View your room assignments, roommates, and daily tasks</p>
            
            <a href="{{ route('mainstudentdash') }}" class="btn back-button">
                <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>

        @if($assignedRooms->count() > 0)
            <!-- Weekly Calendar -->
            <div class="calendar-container">
                <div class="calendar-header">
                    <h3>
                        <i class="bi bi-calendar-week me-2"></i>
                        Select a Day to View Your Tasks
                    </h3>
                </div>

                <div class="week-navigation">
                    <button class="nav-button" onclick="navigateWeek(-1)">
                        <i class="bi bi-chevron-left"></i> Previous Week
                    </button>
                    <div class="week-display">
                        Week of {{ \Carbon\Carbon::parse($weekDays[0]['date'])->format('M j') }} - {{ \Carbon\Carbon::parse($weekDays[6]['date'])->format('M j, Y') }}
                    </div>
                    <button class="nav-button" onclick="navigateWeek(1)">
                        Next Week <i class="bi bi-chevron-right"></i>
                    </button>
                </div>

                <div class="calendar-grid">
                    @foreach($weekDays as $day)
                        <div class="day-card {{ $day['is_selected'] ? 'selected' : '' }} {{ $day['is_today'] ? 'today' : '' }}"
                             onclick="selectDay('{{ $day['date'] }}')">
                            <div class="day-name">{{ $day['day_short'] }}</div>
                            <div class="day-number">{{ $day['day_number'] }}</div>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Selected Day Tasks -->
            <div class="selected-day-tasks">
                <h3 class="mb-4">
                    <i class="bi bi-list-check me-2"></i>
                    Tasks for {{ \Carbon\Carbon::parse($selectedDate)->format('l, F j, Y') }}
                </h3>

                @if($tasksForSelectedDay->count() > 0)
                    @foreach($assignedRooms as $room)
                        @if(isset($tasksForSelectedDay[$room]) && $tasksForSelectedDay[$room]->count() > 0)
                            <div class="room-card">
                                <div class="room-title">
                                    <i class="bi bi-door-open me-2"></i>
                                    Room {{ $room }}
                                </div>

                                <!-- Room Occupants -->
                                <div class="occupants-section">
                                    <h5 class="mb-3">
                                        <i class="bi bi-people me-2"></i>
                                        Room Occupants
                                    </h5>
                                    @if(isset($roomOccupants[$room]) && $roomOccupants[$room]->count() > 0)
                                        @foreach($roomOccupants[$room] as $occupant)
                                            <span class="occupant-badge {{ $occupant === $studentName ? 'current-student' : '' }}">
                                                {{ $occupant }}
                                                @if($occupant === $studentName)
                                                    (You)
                                                @endif
                                            </span>
                                        @endforeach
                                    @else
                                        <p class="text-muted">No occupants found</p>
                                    @endif
                                </div>

                                <!-- Tasks for Selected Day -->
                                <div class="tasks-section">
                                    <h5 class="mb-3">
                                        <i class="bi bi-list-task me-2"></i>
                                        Your Tasks for {{ $selectedDayName }}
                                    </h5>

                                    @foreach($tasksForSelectedDay[$room] as $task)
                                        <div class="task-item">
                                            <div class="task-area">{{ $task->area }}</div>
                                            <div class="task-desc">{{ $task->desc }}</div>
                                            @if(isset($taskStatuses[$room][$selectedDayName]))
                                                <div class="mt-2">
                                                    <span class="status-badge {{ $taskStatuses[$room][$selectedDayName]->status === 'checked' ? 'status-completed' : 'status-pending' }}">
                                                        {{ ucfirst($taskStatuses[$room][$selectedDayName]->status) }}
                                                    </span>
                                                </div>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    @endforeach
                @else
                    <div class="text-center py-5">
                        <i class="bi bi-calendar-x" style="font-size: 3rem; color: #6c757d; margin-bottom: 15px;"></i>
                        <h4>No Tasks for This Day</h4>
                        <p class="text-muted">You don't have any room tasks assigned for {{ \Carbon\Carbon::parse($selectedDate)->format('l, F j, Y') }}.</p>
                    </div>
                @endif
            </div>
        @else
            <div class="room-card">
                <div class="no-rooms-message">
                    <i class="bi bi-house-x" style="font-size: 4rem; color: #6c757d; margin-bottom: 20px;"></i>
                    <h3>No Room Assignments Found</h3>
                    <p>You are not currently assigned to any rooms. Please contact your administrator if this seems incorrect.</p>
                </div>
            </div>
        @endif
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Function to select a specific day
        function selectDay(date) {
            const url = new URL(window.location);
            url.searchParams.set('date', date);
            window.location.href = url.toString();
        }

        // Function to navigate weeks
        function navigateWeek(direction) {
            const currentDate = new Date('{{ $selectedDate }}');
            const newDate = new Date(currentDate);
            newDate.setDate(currentDate.getDate() + (direction * 7));

            const url = new URL(window.location);
            url.searchParams.set('date', newDate.toISOString().split('T')[0]);
            window.location.href = url.toString();
        }

        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                navigateWeek(-1);
            } else if (e.key === 'ArrowRight') {
                navigateWeek(1);
            }
        });

        // Add hover effects for better UX
        document.querySelectorAll('.day-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                if (!this.classList.contains('selected')) {
                    this.style.transform = 'translateY(-3px)';
                }
            });

            card.addEventListener('mouseleave', function() {
                if (!this.classList.contains('selected')) {
                    this.style.transform = 'translateY(0)';
                }
            });
        });
    </script>
</body>
</html>
