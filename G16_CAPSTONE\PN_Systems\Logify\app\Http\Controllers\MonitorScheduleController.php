<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Schedule;
use App\Models\StudentDetail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\IrregularSchedule;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class MonitorScheduleController extends Controller
{
    /**
     * Get the effective schedule for a student, prioritizing irregular schedule if it exists
     *
     * @param string $student_id
     * @param string $batch
     * @param string $group
     * @return \Illuminate\Database\/Eloquent\Collection
     */
    private function getStudentSchedule($student_id, $batch, $group)
    {
        // First check if student has an academic irregular schedule
        $irregularSchedule = Schedule::where('student_id', $student_id)
            ->where('schedule_type', 'academic') // Only look for academic irregular schedules
            ->where(function ($query) {
                $query->whereNull('valid_until')
                    ->orWhereDate('valid_until', '>=', Carbon::today());
            })
            ->orderBy('day_of_week')
            ->get();

        // If irregular schedule exists, return it
        if ($irregularSchedule->isNotEmpty()) {
            Log::info('Fetched irregular schedule for student.', [
                'student_id' => $student_id,
            ]);
            return $irregularSchedule;
        }

        Log::info('Fetched batch schedule for student.', [
            'student_id' => $student_id,
            'batch' => $batch,
            'group' => $group,
        ]);
        // If no irregular schedule, return the academic batch schedule
        return Schedule::where('batch', $batch)
            ->where('pn_group', $group)
            ->where('schedule_type', 'academic') // Only look for academic batch schedules
            ->where(function ($query) {
                $query->whereNull('valid_until')
                    ->orWhereDate('valid_until', '>=', Carbon::today());
            })
            ->orderBy('day_of_week')
            ->get();
    }
    public function getGroups(Request $request)
    {
        try {
            $batch = $request->input('batch');

            $groups = DB::table('student_details')
                ->join('pnph_users', 'student_details.user_id', '=', 'pnph_users.user_id')
                ->where('student_details.batch', $batch)
                ->where('pnph_users.status', 'active')
                ->select('student_details.group')
                ->distinct()
                ->orderBy('student_details.group')
                ->get();

            Log::info('Fetched groups for batch.', [
                'batch' => $batch,
                'user_id' => Auth::id(),
            ]);

            return response()->json($groups);
        } catch (\Exception $e) {
            Log::error('Error fetching groups:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to fetch groups',
                'message' => $e->getMessage()
            ], 500);
        }
    }
    public function show(Request $request)
    {
        try {
            $data = $request->only([
                'type',
                'batch',
                'group',
                'student_id',
                'gender',
            ]);
            $currentSchedule = null;
            $validUntil = null;
            $expiredSchedule = null;
            $warningMessage = null;

            // Get unique active batches from student_details
            $batches = DB::table('student_details')
                ->join('pnph_users', 'student_details.user_id', '=', 'pnph_users.user_id')
                ->where('pnph_users.status', 'active')
                ->select('student_details.batch')
                ->distinct()
                ->orderBy('student_details.batch', 'desc')
                ->get();
            // dd($group);
            // dd($type);
            if ($data['type'] == 'Academic') {
                // dd($request->all());
                // Check for expired schedule first
                $expiredSchedule = Schedule::where('batch', $data['batch'])
                    ->where('pn_group', $data['group'])
                    ->where('schedule_type', 'academic') // Only look for academic batch schedules
                    ->where('valid_until', '<', Carbon::now())
                    ->orderBy('valid_until', 'desc')
                    ->first();

                // Get current active schedule for the batch and group
                $currentSchedule = Schedule::where('batch', $data['batch'])
                    ->where('pn_group', $data['group'])
                    ->where('schedule_type', 'academic') // Only look for academic batch schedules
                    ->where(function ($query) {
                        $query->whereNull('valid_until')
                            ->orWhereDate('valid_until', '>=', Carbon::today());
                    })
                    ->orderBy('day_of_week')
                    ->get();

                // Get the valid_until value if it exists
                if ($currentSchedule->isNotEmpty()) {
                    $validUntil = $currentSchedule->first()->valid_until;
                }

                // If schedule is expiring soon (within 3 days), prepare warning message
                if ($validUntil && Carbon::parse($validUntil)->diffInDays(Carbon::now()) <= 3) {
                    $warningMessage = 'Schedule will expire on ' . Carbon::parse($validUntil)->format('M d, Y') . '. Please update the schedule.';
                }
            } elseif ($data['type'] == 'Irregular') {
                // dd($data);
                // Check for expired schedule first
                $expiredSchedule = Schedule::where('student_id', $data['student_id'])
                    ->where('schedule_type', 'academic') // Only look for academic irregular schedules
                    ->where('valid_until', '<', Carbon::now())
                    ->orderBy('valid_until', 'desc')
                    ->first();

                // Get current active schedule for the student
                $currentSchedule = Schedule::where('student_id', $data['student_id'])
                    ->where('schedule_type', 'academic') // Only look for academic irregular schedules
                    ->where(function ($query) {
                        $query->whereNull('valid_until')
                            ->orWhereDate('valid_until', '>=', Carbon::today());
                    })
                    ->orderBy('day_of_week')
                    ->get();

                // Get the valid_until value if it exists
                if ($currentSchedule->isNotEmpty()) {
                    $validUntil = $currentSchedule->first()->valid_until;
                }

                // If schedule is expiring soon (within 3 days), prepare warning message
                if ($validUntil && Carbon::parse($validUntil)->diffInDays(Carbon::now()) <= 3) {
                    $warningMessage = 'Schedule will expire on ' . Carbon::parse($validUntil)->format('M d, Y') . '. Please update the schedule.';
                }
            } elseif ($data['type'] == 'GoingOut') {
                // Check for expired schedule first
                $expiredSchedule = Schedule::where('gender', $data['gender'])
                    ->where('schedule_type', 'going_out') // Only look for going-out gender schedules
                    ->where('valid_until', '<', Carbon::now())
                    ->orderBy('valid_until', 'desc')
                    ->first();

                // Get current active schedule for the student
                $currentSchedule = Schedule::where('gender', $data['gender'])
                    ->where('schedule_type', 'going_out') // Only look for going-out gender schedules
                    ->where(function ($query) {
                        $query->whereNull('valid_until')
                            ->orWhereDate('valid_until', '>=', Carbon::today());
                    })
                    ->orderBy('day_of_week')
                    ->get();

                // Get the valid_until value if it exists
                if ($currentSchedule->isNotEmpty()) {
                    $validUntil = $currentSchedule->first()->valid_until;
                }

                // If schedule is expiring soon (within 3 days), prepare warning message
                if ($validUntil && Carbon::parse($validUntil)->diffInDays(Carbon::now()) <= 3) {
                    $warningMessage = 'Schedule will expire on ' . Carbon::parse($validUntil)->format('M d, Y') . '. Please update the schedule.';
                }
            }

            Log::info('Monitor viewed schedule form.', [
                'user_id' => Auth::id(),
                'type' => $data['type'],
                'batch' => $data['batch'] ?? null,
                'group' => $data['group'] ?? null,
                'student_id' => $data['student_id'] ?? null,
                'gender' => $data['gender'] ?? null,
            ]);

            // Always use setSched view
            return view('user-monitor.setSched', compact('data', 'batches', 'currentSchedule', 'validUntil', 'expiredSchedule', 'warningMessage'));
        } catch (\Exception $e) {
            Log::error('Error in MonitorScheduleController@show:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            return back()->with('error', 'Failed to load schedule form: ' . $e->getMessage());
        }
    }

    public function store(Request $request)
    {
        try {
            // dd($request->all());
            // 1) Validate top‐level inputs
            $validator = validator($request->all(), [
                'student_id'         => 'nullable|string',
                'gender'             => 'nullable|string',
                'type'               => 'required|in:Academic,Irregular,GoingOut',
                'batch'              => 'nullable|string',
                'group'              => 'nullable|in:PN1,PN2',
                'valid_until_option' => 'required|in:until_changed,set_date',
                'valid_until'        => 'nullable|date|after_or_equal:today',
                'grace_period_logout_minutes' => 'nullable|integer|min:0|max:60',
                'grace_period_login_minutes' => 'nullable|integer|min:0|max:60',
                'schedule'           => 'required|array',
                'schedule.*.time_out' => 'required|date_format:H:i',
                'schedule.*.time_in' => [
                    'required',
                    'date_format:H:i',
                    function ($attribute, $value, $fail) use ($request) {
                        $parts = explode('.', $attribute);
                        $day = $parts[1];
                        $timeOut = $request->input("schedule.$day.time_out");

                        if ($timeOut === $value) {
                            $fail("Time in cannot be the same as time out for this schedule");
                        } elseif (strtotime($timeOut) >= strtotime($value)) {
                            $fail("Time in must be after time out for this schedule");
                        }
                    },
                ],
            ], [
                'schedule.*.time_in.after' => 'Time in must be after time out',
                'valid_until.after_or_equal' => 'Valid until date must be today or a future date',
                'group.required' => 'Please select a group (PN1 or PN2)',
                'group.in' => 'Please select either PN1 or PN2',
                'grace_period_logout_minutes.integer' => 'Log out grace period must be a valid number',
                'grace_period_logout_minutes.min' => 'Log out grace period cannot be negative',
                'grace_period_logout_minutes.max' => 'Log out grace period cannot exceed 60 minutes',
                'grace_period_login_minutes.integer' => 'Log in grace period must be a valid number',
                'grace_period_login_minutes.min' => 'Log in grace period cannot be negative',
                'grace_period_login_minutes.max' => 'Log in grace period cannot exceed 60 minutes'
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput();
            }

            $data = $validator->validated();
            // 2) Determine final valid_until
            $validUntil = $data['valid_until_option'] === 'set_date'
                ? $data['valid_until']
                : null;

            if (isset($data['student_id'])) {
                // Check for existing ACTIVE irregular schedule for this student
                $existingIregStud = Schedule::where('student_id', request('student_id'))
                    ->where('schedule_type', 'academic') // Only look for academic irregular schedules
                    ->where(function ($query) {
                        $query->whereNull('valid_until') // Permanent schedule
                            ->orWhere('valid_until', '>=', Carbon::today()); // Or valid today/future
                    })
                    ->orderBy('updated_at', 'desc') // Get most recent first
                    ->first();
                if ($existingIregStud) {
                    // Get student details to find their batch and group for grace period inheritance
                    $student = \App\Models\StudentDetail::where('student_id', $data['student_id'])->first();

                    // Get batch schedule to inherit grace periods if not explicitly set
                    $batchSchedule = null;
                    if ($student) {
                        $batchSchedule = Schedule::where('batch', $student->batch)
                            ->where('pn_group', $student->group)
                            ->where('schedule_type', 'academic')
                            ->where(function ($query) {
                                $query->whereNull('valid_until')
                                    ->orWhereDate('valid_until', '>=', Carbon::today());
                            })
                            ->first();
                    }

                    // Update existing schedule
                    foreach ($data['schedule'] as $dayKey => $times) {
                        $updateData = [
                            'time_in' => $times['time_in'],
                            'time_out' => $times['time_out'],
                            'valid_until' => $validUntil,
                            'updated_at' => now(), // Force timestamp update
                        ];

                        // Handle grace periods for Academic and Irregular schedules
                        if ($data['type'] === 'Academic' || $data['type'] === 'Irregular') {
                            // If grace periods are explicitly provided, use them
                            if (array_key_exists('grace_period_logout_minutes', $data)) {
                                $updateData['grace_period_logout_minutes'] = $data['grace_period_logout_minutes'] ?: null;
                            } else {
                                // Inherit from batch schedule if available
                                $updateData['grace_period_logout_minutes'] = $batchSchedule ? $batchSchedule->grace_period_logout_minutes : null;
                            }

                            if (array_key_exists('grace_period_login_minutes', $data)) {
                                $updateData['grace_period_login_minutes'] = $data['grace_period_login_minutes'] ?: null;
                            } else {
                                // Inherit from batch schedule if available
                                $updateData['grace_period_login_minutes'] = $batchSchedule ? $batchSchedule->grace_period_login_minutes : null;
                            }
                        }

                        $updated = Schedule::where('student_id', $data['student_id'])
                            ->where('schedule_type', 'academic') // Only update academic irregular schedules
                            ->where('day_of_week', ucfirst($dayKey)) // Target a specific day
                            ->where(function ($query) {
                                $query->whereNull('valid_until')
                                    ->orWhereDate('valid_until', '>=', Carbon::today());
                            })
                            ->update($updateData);
                    }

                    // Clear any potential caches
                    if (function_exists('opcache_reset')) {
                        opcache_reset();
                    }

                    // Clear Laravel's query cache if enabled
                    if (config('cache.default') !== 'array') {
                        Cache::flush();
                    }

                    // Verify the update was successful by re-querying
                    $verifySchedule = Schedule::where('student_id', $data['student_id'])
                        ->where('schedule_type', 'academic') // Only verify academic irregular schedules
                        ->where(function ($query) {
                            $query->whereNull('valid_until')
                                ->orWhereDate('valid_until', '>=', Carbon::today());
                        })
                        ->orderBy('updated_at', 'desc')
                        ->get();

                    return redirect()
                        ->route('monitor.schedule', [
                            'student_id' => $data['student_id'],
                            'type' => $data['type'],
                        ])
                        ->with('success', 'Schedule has been updated successfully! Changes are now active.');
                } else {
                    // Get student details to find their batch and group
                    $student = \App\Models\StudentDetail::where('student_id', $data['student_id'])->first();

                    // Get batch schedule to inherit grace periods if not explicitly set
                    $batchSchedule = null;
                    if ($student) {
                        $batchSchedule = Schedule::where('batch', $student->batch)
                            ->where('pn_group', $student->group)
                            ->where('schedule_type', 'academic')
                            ->where(function ($query) {
                                $query->whereNull('valid_until')
                                    ->orWhereDate('valid_until', '>=', Carbon::today());
                            })
                            ->first();
                    }
                    // dd(session('user.user_id'));
                    foreach ($data['schedule'] as $dayKey => $times) {
                        $createData = [
                            'student_id' => $data['student_id'],
                            'day_of_week' => ucfirst($dayKey),
                            'schedule_type' => 'academic', // Set schedule type for academic irregular schedules
                            'time_in' => $times['time_in'],
                            'time_out' => $times['time_out'],
                            'valid_until' => $validUntil,
                            'created_by' => session('user.user_id'),
                        ];

                        // Add grace periods for Academic and Irregular schedules
                        if ($data['type'] === 'Academic' || $data['type'] === 'Irregular') {
                            // If grace periods are explicitly provided, use them
                            if (array_key_exists('grace_period_logout_minutes', $data)) {
                                $createData['grace_period_logout_minutes'] = $data['grace_period_logout_minutes'] ?: null;
                            } else {
                                // Inherit from batch schedule if available
                                $createData['grace_period_logout_minutes'] = $batchSchedule ? $batchSchedule->grace_period_logout_minutes : null;
                            }

                            if (array_key_exists('grace_period_login_minutes', $data)) {
                                $createData['grace_period_login_minutes'] = $data['grace_period_login_minutes'] ?: null;
                            } else {
                                // Inherit from batch schedule if available
                                $createData['grace_period_login_minutes'] = $batchSchedule ? $batchSchedule->grace_period_login_minutes : null;
                            }
                        }

                        Schedule::create($createData);
                    }

                    Log::info('Created new irregular student schedule.', [
                        'student_id' => $data['student_id'],
                        'user_id' => Auth::id(),
                        'batch_schedule_found' => $batchSchedule ? true : false,
                        'inherited_grace_logout' => $batchSchedule ? $batchSchedule->grace_period_logout_minutes : null,
                        'inherited_grace_login' => $batchSchedule ? $batchSchedule->grace_period_login_minutes : null,
                        'explicit_grace_logout' => array_key_exists('grace_period_logout_minutes', $data) ? $data['grace_period_logout_minutes'] : 'not_provided',
                        'explicit_grace_login' => array_key_exists('grace_period_login_minutes', $data) ? $data['grace_period_login_minutes'] : 'not_provided'
                    ]);
                    return redirect()
                        ->route('monitor.schedule', [
                            'student_id' => $data['student_id'],
                            'type' => $data['type'],
                        ])
                        ->with('success', 'Schedule has been saved successfully!');
                }
            }
            // 3) Process each day's schedule
            if (isset($data['group']) && isset($data['batch'])) {
                $existingAcadBatch = Schedule::where('batch', $data['batch'])
                    ->where('pn_group', $data['group'])
                    ->where('schedule_type', 'academic') // Only look for academic batch schedules
                    ->where(function ($query) {
                        $query->whereNull('valid_until')
                            ->orWhereDate('valid_until', '>=', Carbon::today());
                    })
                    ->first();
                if ($existingAcadBatch) {
                    foreach ($data['schedule'] as $dayKey => $times) {
                        $updateData = [
                            'time_in' => $times['time_in'],
                            'time_out' => $times['time_out'],
                            'valid_until' => $validUntil,
                            'updated_at' => now(),
                        ];

                        // Preserve existing grace periods for Academic and Going Out schedules if not provided
                        if ($data['type'] === 'Academic' || $data['type'] === 'GoingOut') {
                            // Only update grace periods if they are explicitly provided in the request
                            if (array_key_exists('grace_period_logout_minutes', $data)) {
                                $updateData['grace_period_logout_minutes'] = $data['grace_period_logout_minutes'] ?: null;
                            }
                            if (array_key_exists('grace_period_login_minutes', $data)) {
                                $updateData['grace_period_login_minutes'] = $data['grace_period_login_minutes'] ?: null;
                            }
                        }

                        $updated = Schedule::where('batch', $data['batch'])
                            ->where('pn_group', $data['group'])
                            ->where('day_of_week', ucfirst($dayKey)) // Target a specific day
                            ->where(function ($query) {
                                $query->whereNull('valid_until')
                                    ->orWhereDate('valid_until', '>=', Carbon::today());
                            })
                            ->update($updateData);
                    }

                    // Verify the update was successful by re-querying
                    $verifySchedule = Schedule::where('batch', $data['batch'])
                        ->where('pn_group', $data['group'])
                        ->where(function ($query) {
                            $query->whereNull('valid_until')
                                ->orWhereDate('valid_until', '>=', Carbon::today());
                        })
                        ->orderBy('updated_at', 'desc')
                        ->get();

                    return redirect()
                        ->route('monitor.schedule', [
                            'batch' => $data['batch'],
                            'group' => $data['group'],
                            'type' => $data['type'],
                        ])
                        ->with('success', 'Schedule has been updated successfully! Changes are now active.');
                } else {
                    // dd($data);
                    foreach ($data['schedule'] as $dayKey => $times) {
                        // Create new schedule
                        $createData = [
                            'batch' => $data['batch'],
                            'pn_group' => $data['group'],
                            'day_of_week' => ucfirst($dayKey),
                            'schedule_type' => 'academic', // Set schedule type for academic batch schedules
                            'time_in' => $times['time_in'],
                            'time_out' => $times['time_out'],
                            'valid_until' => $validUntil,
                            'created_by' => session('user.user_id'),
                        ];

                        // Add grace periods only for Academic schedules
                        if ($data['type'] === 'Academic') {
                            $createData['grace_period_logout_minutes'] = $data['grace_period_logout_minutes'] ?? null;
                            $createData['grace_period_login_minutes'] = $data['grace_period_login_minutes'] ?? null;
                        }

                        Schedule::create($createData);
                    }
                    return redirect()
                        ->route('monitor.schedule', [
                            'batch' => $data['batch'],
                            'group' => $data['group'],
                            'type' => $data['type'],
                        ])
                        ->with('success', 'Schedule has been saved successfully!');
                }
            }

            if (isset($data['gender'])) {
                // dd($data);
                $existingGoingOut = Schedule::where('gender', $data['gender'])
                    ->where('schedule_type', 'going_out') // Only look for going-out gender schedules
                    ->where(function ($query) {
                        $query->whereNull('valid_until')
                            ->orWhereDate('valid_until', '>=', Carbon::today());
                    })
                    ->first();
                if ($existingGoingOut) {
                    // dd('dsjs');
                    foreach ($data['schedule'] as $dayKey => $times) {
                        $updateData = [
                            'time_in' => $times['time_in'],
                            'time_out' => $times['time_out'],
                            'valid_until' => $validUntil,
                            'updated_at' => now(), // Force timestamp update
                        ];

                        // Add grace periods only for Academic schedules (not for Going Out)
                        if ($data['type'] === 'Academic') {
                            // Only update grace periods if they are explicitly provided in the request
                            if (array_key_exists('grace_period_logout_minutes', $data)) {
                                $updateData['grace_period_logout_minutes'] = $data['grace_period_logout_minutes'] ?: null;
                            }
                            if (array_key_exists('grace_period_login_minutes', $data)) {
                                $updateData['grace_period_login_minutes'] = $data['grace_period_login_minutes'] ?: null;
                            }
                        }

                        $updated = Schedule::where('gender', $data['gender'])
                            ->where('day_of_week', ucfirst($dayKey)) // Target a specific day
                            ->where(function ($query) {
                                $query->whereNull('valid_until')
                                    ->orWhereDate('valid_until', '>=', Carbon::today());
                            })
                            ->update($updateData);
                    }
                    return redirect()
                        ->route('monitor.schedule', [
                            'gender' => $data['gender'],
                            'type' => $data['type'],
                        ])
                        ->with('success', 'Schedule has been updated successfully!');
                } else {
                    // dd('kdf');
                    foreach ($data['schedule'] as $dayKey => $times) {
                        $createData = [
                            'gender' => $data['gender'],
                            'day_of_week' => ucfirst($dayKey),
                            'schedule_type' => 'going_out', // Set schedule type for going-out gender schedules
                            'time_in' => $times['time_in'],
                            'time_out' => $times['time_out'],
                            'valid_until' => $validUntil,
                            'created_by' => session('user.user_id'),
                        ];

                        // Add grace periods only for Academic schedules (not for Going Out)
                        if ($data['type'] === 'Academic') {
                            $createData['grace_period_logout_minutes'] = $data['grace_period_logout_minutes'] ?? null;
                            $createData['grace_period_login_minutes'] = $data['grace_period_login_minutes'] ?? null;
                        }

                        // Create new schedule
                        Schedule::create($createData);
                    }
                    return redirect()
                        ->route('monitor.schedule', [
                            'gender' => $data['gender'],
                            'type' => $data['type'],
                        ])
                        ->with('success', 'Schedule has been saved successfully!');
                }
            }
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update schedule: ' . $e->getMessage());
        }
    }


    public function showIrregularSchedule($student_id)
    {
        try {
            // Get the student details with their name
            $student = DB::table('student_details')
                ->join('pnph_users', 'student_details.user_id', '=', 'pnph_users.user_id')
                ->where('student_details.student_id', $student_id)
                ->select(
                    'student_details.*',
                    'pnph_users.user_fname as first_name',
                    'pnph_users.user_lname as last_name'
                )
                ->first();

            if (!$student) {
                return redirect()
                    ->route('monitor.irregular-schedule.select')
                    ->with('error', 'Student not found');
            }

            // Get current active schedule for the student
            $currentSchedule = Schedule::where('student_id', $student_id)
                ->where('schedule_type', 'academic') // Only get academic irregular schedules
                ->where(function ($query) {
                    $query->whereNull('valid_until')
                        ->orWhereDate('valid_until', '>=', Carbon::today());
                })
                ->orderBy('day_of_week')
                ->get();

            // Get expired schedule if it exists
            $expiredSchedule = Schedule::where('student_id', $student_id)
                ->where('schedule_type', 'academic') // Only get academic irregular schedules
                ->where('valid_until', '<', Carbon::now())
                ->orderBy('valid_until', 'desc')
                ->first();

            // Get the valid_until value if it exists
            $validUntil = $currentSchedule->isNotEmpty() ? $currentSchedule->first()->valid_until : null;

            // Prepare warning message if schedule is expiring soon
            $warningMessage = null;
            if ($validUntil && Carbon::parse($validUntil)->diffInDays(Carbon::now()) <= 3) {
                $warningMessage = 'Schedule will expire on ' . Carbon::parse($validUntil)->format('M d, Y') . '. Please update the schedule.';
            }

            // Get batch schedule to show inherited grace periods for new irregular schedules
            $batchSchedule = null;
            if ($currentSchedule->isEmpty()) {
                $batchSchedule = Schedule::where('batch', $student->batch)
                    ->where('pn_group', $student->group)
                    ->where('schedule_type', 'academic')
                    ->where(function ($query) {
                        $query->whereNull('valid_until')
                            ->orWhereDate('valid_until', '>=', Carbon::today());
                    })
                    ->first();
            }

            // Prepare data array for the view
            $data = [
                'type' => 'Irregular',
                'student_id' => $student_id,
                'student_name' => $student->first_name . ' ' . $student->last_name,
                'batch' => $student->batch,
                'group' => $student->group
            ];

            Log::info('Monitor viewed irregular schedule.', [
                'student_id' => $student_id,
                'user_id' => Auth::id(),
            ]);

            return view('user-monitor.setSched', compact('data', 'currentSchedule', 'expiredSchedule', 'validUntil', 'warningMessage', 'batchSchedule'));
        } catch (\Exception $e) {
            Log::error('Error in showIrregularSchedule:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'student_id' => $student_id,
                'user_id' => Auth::id(),
            ]);
            return redirect()
                ->route('monitor.irregular-schedule.select')
                ->with('error', 'Failed to load student schedule: ' . $e->getMessage());
        }
    }

    public function selectStudent()
    {
        try {
            $students = DB::table('student_details')
                ->join('pnph_users', 'student_details.user_id', '=', 'pnph_users.user_id')
                ->select(
                    'student_details.student_id',
                    'pnph_users.user_fname as first_name',
                    'pnph_users.user_lname as last_name',
                    'student_details.batch',
                    'student_details.group as pn_group'
                )
                ->where('pnph_users.user_role', 'student')
                ->where('pnph_users.status', 'active')
                ->orderBy('student_details.batch')
                ->orderBy('student_details.group')
                ->orderBy('pnph_users.user_lname')
                ->get();

            // Get all student_ids that already have an academic irregular schedule
            $studentsWithSchedule = DB::table('schedules')
                ->whereNotNull('student_id')
                ->where('schedule_type', 'academic') // Only count academic irregular schedules
                ->select('student_id')
                ->distinct()
                ->pluck('student_id')
                ->toArray();

            Log::info('Monitor viewed select student page.', [
                'user_id' => Auth::id(),
            ]);

            return view('user-monitor.select-student', compact('students', 'studentsWithSchedule'));
        } catch (\Exception $e) {
            Log::error('Error in selectStudent:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => Auth::id(),
            ]);
            return back()->with('error', 'Failed to load students: ' . $e->getMessage());
        }
    }


    public function dashboard()
    {
        // Get unique active batches from student_details
        $batches = DB::table('student_details')
            ->join('pnph_users', 'student_details.user_id', '=', 'pnph_users.user_id')
            ->where('pnph_users.status', 'active')
            ->select('student_details.batch')
            ->distinct()
            ->orderBy('student_details.batch', 'desc')
            ->get();

        Log::info('Monitor accessed dashboard.', [
            'user_id' => Auth::id(),
        ]);

        return view('user-monitor.dashboard', compact('batches'));
    }

    public function delete(Request $request)
    {
        try {
            $type = $request->input('type');
            $query = Schedule::query();

            if ($type === 'Academic') {
                $query->where('batch', $request->input('batch'))
                    ->where('pn_group', $request->input('group'));
            } elseif ($type === 'Irregular') {
                $query->where('student_id', $request->input('student_id'))
                    ->where('schedule_type', 'academic'); // Only delete academic irregular schedules
            } elseif ($type === 'GoingOut') {
                $query->where('gender', $request->input('gender'));
            }

            $deleted = $query->delete();

            if ($deleted) {
                Log::info('Schedule deleted successfully', [
                    'type' => $type,
                    'user_id' => Auth::id(),
                    'batch' => $request->input('batch'),
                    'group' => $request->input('group'),
                    'student_id' => $request->input('student_id'),
                    'gender' => $request->input('gender')
                ]);

                return redirect()->back()->with('success', 'Schedule deleted successfully');
            }

            return redirect()->back()->with('error', 'No schedule found to delete');
        } catch (\Exception $e) {
            Log::error('Error deleting schedule:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return redirect()->back()->with('error', 'Failed to delete schedule: ' . $e->getMessage());
        }
    }

    public function updateGracePeriod(Request $request)
    {
        try {
            // Validate the request - only allow Academic schedules
            $validator = validator($request->all(), [
                'type' => 'required|in:Academic,Irregular',
                'batch' => 'nullable|string',
                'group' => 'nullable|in:PN1,PN2',
                'student_id' => 'nullable|string',
                'gender' => 'nullable|string',
                'grace_period_logout_minutes' => 'nullable|integer|min:0|max:60',
                'grace_period_login_minutes' => 'nullable|integer|min:0|max:60',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid input: ' . $validator->errors()->first()
                ], 400);
            }

            $data = $validator->validated();

            // Prevent grace period updates for GoingOut schedules
            if ($data['type'] === 'GoingOut') {
                return response()->json([
                    'success' => false,
                    'message' => 'Grace period settings are not available for Going Out schedules.'
                ], 400);
            }

            // Determine which schedules to update based on type
            $query = Schedule::query();

            if ($data['type'] === 'Academic' && isset($data['batch']) && isset($data['group'])) {
                $query->where('batch', $data['batch'])
                    ->where('pn_group', $data['group']);
            } elseif ($data['type'] === 'Irregular' && isset($data['student_id'])) {
                $query->where('student_id', $data['student_id']);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid schedule parameters'
                ], 400);
            }

            // Only update active schedules
            $query->where(function ($q) {
                $q->whereNull('valid_until')
                    ->orWhereDate('valid_until', '>=', Carbon::today());
            });

            // Prepare update data
            $updateData = [];
            $updateData['grace_period_logout_minutes'] = $data['grace_period_logout_minutes'] ?: null;
            $updateData['grace_period_login_minutes'] = $data['grace_period_login_minutes'] ?: null;
            $updateData['updated_at'] = now();

            // Update the schedules
            $updated = $query->update($updateData);

            if ($updated > 0) {
                $logoutValue = $data['grace_period_logout_minutes'];
                $loginValue = $data['grace_period_login_minutes'];

                $messages = [];
                if ($logoutValue) {
                    $messages[] = "Log out: {$logoutValue} minutes";
                } else {
                    $messages[] = "Log out: exact timing";
                }

                if ($loginValue) {
                    $messages[] = "Log in: {$loginValue} minutes";
                } else {
                    $messages[] = "Log in: exact timing";
                }

                $message = "Grace periods updated - " . implode(', ', $messages);

                Log::info('Grace periods updated successfully', [
                    'type' => $data['type'],
                    'batch' => $data['batch'] ?? null,
                    'group' => $data['group'] ?? null,
                    'student_id' => $data['student_id'] ?? null,
                    'grace_period_logout_minutes' => $logoutValue,
                    'grace_period_login_minutes' => $loginValue,
                    'schedules_updated' => $updated,
                    'user_id' => Auth::id()
                ]);

                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'schedules_updated' => $updated
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'No schedules found to update'
                ], 404);
            }
        } catch (\Exception $e) {
            Log::error('Error updating grace period:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
                'user_id' => Auth::id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update grace period: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show students for individual going-out schedule selection
     */
    public function showIndividualGoingOutStudents(Request $request)
    {
        try {
            // Get all students with their user details (no server-side filtering)
            $students = StudentDetail::with('user')->orderBy('student_id')->get();
            $batches = StudentDetail::distinct()->pluck('batch')->sort();
            $groups = StudentDetail::distinct()->pluck('group')->sort();

            // Get existing individual going-out schedules for all students
            $existingSchedules = Schedule::where('schedule_type', 'going_out')
                ->whereNotNull('student_id')
                ->where(function ($query) {
                    $query->whereNull('valid_until')
                        ->orWhere('valid_until', '>=', Carbon::today());
                })
                ->get()
                ->groupBy('student_id');

            Log::info('Individual going-out students page accessed', [
                'user_id' => Auth::id(),
                'students_count' => $students->count(),
                'existing_schedules_count' => $existingSchedules->count()
            ]);

            return view('user-monitor.individual-goingout-students', compact('students', 'batches', 'groups', 'existingSchedules'));
        } catch (\Exception $e) {
            Log::error('Error loading individual going-out students page:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => Auth::id()
            ]);

            return redirect()->route('monitor.dashboard')
                ->with('error', 'Failed to load students page: ' . $e->getMessage());
        }
    }

    /**
     * Set individual going-out schedule for selected students
     */
    public function setIndividualGoingOutSchedule(Request $request)
    {
        try {
            // Validate the request
            $validator = validator($request->all(), [
                'student_ids' => 'required|array|min:1',
                'student_ids.*' => 'required|exists:student_details,student_id',
                'selected_days' => 'required|array|min:1',
                'selected_days.*' => 'required|in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
                'schedule' => 'required|array',
                'schedule.*.time_out' => 'required|date_format:H:i',
                'schedule.*.time_in' => 'required|date_format:H:i|after:schedule.*.time_out',
            ]);

            if ($validator->fails()) {
                return redirect()->back()
                    ->withErrors($validator)
                    ->withInput()
                    ->with('error', 'Please fix the validation errors and try again.');
            }

            $data = $validator->validated();

            DB::beginTransaction();

            try {
                $createdSchedules = 0;
                $updatedSchedules = 0;

                foreach ($data['student_ids'] as $studentId) {
                    // Only process the selected days
                    foreach ($data['selected_days'] as $selectedDay) {
                        if (!isset($data['schedule'][$selectedDay])) {
                            continue; // Skip if no schedule data for this day
                        }

                        $times = $data['schedule'][$selectedDay];
                        $dayOfWeek = ucfirst($selectedDay);

                        // Calculate expiry time: end of the selected day (23:59:59)
                        $today = Carbon::today();

                        // Find the next occurrence of the selected day
                        $dayMapping = [
                            'monday' => Carbon::MONDAY,
                            'tuesday' => Carbon::TUESDAY,
                            'wednesday' => Carbon::WEDNESDAY,
                            'thursday' => Carbon::THURSDAY,
                            'friday' => Carbon::FRIDAY,
                            'saturday' => Carbon::SATURDAY,
                            'sunday' => Carbon::SUNDAY
                        ];

                        $targetDayNumber = $dayMapping[strtolower($selectedDay)];

                        // If today is the selected day, expire at end of today
                        // Otherwise, find the next occurrence of the selected day
                        if ($today->dayOfWeek === $targetDayNumber) {
                            $validUntil = $today->copy()->endOfDay(); // End of today
                        } else {
                            $validUntil = $today->copy()->next($targetDayNumber)->endOfDay(); // End of next occurrence
                        }

                        // Check if individual going-out schedule already exists for this student and day
                        $existingSchedule = Schedule::where([
                            ['student_id', $studentId],
                            ['day_of_week', $dayOfWeek],
                            ['schedule_type', 'going_out']
                        ])->where(function ($query) {
                            $query->whereNull('valid_until')
                                ->orWhere('valid_until', '>=', Carbon::now());
                        })->first();

                        $scheduleData = [
                            'student_id' => $studentId,
                            'gender' => null, // Individual schedules don't use gender
                            'batch' => null,  // Individual schedules don't use batch
                            'pn_group' => null, // Individual schedules don't use group
                            'day_of_week' => $dayOfWeek,
                            'schedule_type' => 'going_out', // Set schedule type for going-out
                            'time_in' => $times['time_in'],
                            'time_out' => $times['time_out'],
                            'valid_until' => $validUntil, // Auto-expire at end of day
                            'grace_period_logout_minutes' => null, // No grace periods for going-out
                            'grace_period_login_minutes' => null,  // No grace periods for going-out
                            'updated_at' => now(),
                        ];

                        if ($existingSchedule) {
                            // Update existing schedule
                            $existingSchedule->update($scheduleData);
                            $updatedSchedules++;
                        } else {
                            // Create new schedule
                            $scheduleData['created_by'] = session('user.user_id');
                            $scheduleData['created_at'] = now();
                            Schedule::create($scheduleData);
                            $createdSchedules++;
                        }
                    }
                }

                DB::commit();

                $studentCount = count($data['student_ids']);
                $dayCount = count($data['selected_days']);
                $dayNames = implode(', ', array_map('ucfirst', $data['selected_days']));

                $message = "Individual going-out schedule set successfully for {$studentCount} student(s) on {$dayNames}. ";
                $message .= "Created: {$createdSchedules}, Updated: {$updatedSchedules} schedule entries. ";
                $message .= "Schedules will automatically expire at midnight of the scheduled day.";

                Log::info('Individual going-out schedule set successfully', [
                    'student_ids' => $data['student_ids'],
                    'selected_days' => $data['selected_days'],
                    'student_count' => $studentCount,
                    'day_count' => $dayCount,
                    'created_schedules' => $createdSchedules,
                    'updated_schedules' => $updatedSchedules,
                    'user_id' => Auth::id()
                ]);

                return redirect()->route('monitor.individual-goingout.students')
                    ->with('success', $message);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('Error setting individual going-out schedule:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all(),
                'user_id' => Auth::id()
            ]);

            return redirect()->back()
                ->withInput()
                ->with('error', 'Failed to set schedule: ' . $e->getMessage());
        }
    }
}
