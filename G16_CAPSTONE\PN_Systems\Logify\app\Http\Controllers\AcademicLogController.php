<?php

namespace App\Http\Controllers;

use App\Models\Academic;
use App\Models\PNUser;
use App\Models\StudentDetail;
use App\Models\Schedule;
use App\Models\NotificationView;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class AcademicLogController extends Controller
{
    /**
     * Monitor academic logs for the current day.
     */
    public function monitor(Request $request)
    {
        // Mark academic notifications as viewed when accessing the monitor page
        NotificationView::markAsViewed('academic');

        $query = Academic::query();

        if ($request->has('date') && !empty($request->date)) {
            $query->whereDate('academic_date', $request->date);
        } else {
            $query->whereDate('academic_date', now()->format('Y-m-d'));
        }

        // Filter by batch if present
        if ($request->has('batch') && !empty($request->batch)) {
            $query->whereHas('studentDetail', function ($q) use ($request) {
                $q->where('batch', $request->batch);
            });
        }

        // Filter by group if present
        if ($request->has('group') && !empty($request->group)) {
            $query->whereHas('studentDetail', function ($q) use ($request) {
                $q->where('group', $request->group);
            });
        }

        $selectedDate = $request->date ?? now()->format('Y-m-d');

        // Order by latest activity (most recent time_out or time_in operations first)
        $todayLogs = $query->with(['studentDetail'])
            ->get()
            ->sortByDesc(function ($log) {
                // Get the latest timestamp between time_out and time_in
                $timeOutTimestamp = $log->time_out ? strtotime($log->academic_date . ' ' . $log->time_out) : 0;
                $timeInTimestamp = $log->time_in ? strtotime($log->academic_date . ' ' . $log->time_in) : 0;
                return max($timeOutTimestamp, $timeInTimestamp);
            })
            ->values();

        $batches = StudentDetail::distinct()->pluck('batch')->sort();
        $groups = StudentDetail::distinct()->pluck('group')->sort();

        return view('user-educator.academicmonitor', [
            'academicLogs' => $todayLogs,
            'batches' => $batches,
            'groups' => $groups,
            'selectedDate' => $selectedDate,
        ]);
    }

    /**
     * Monitor past logs with filtering capabilities.
     */
    public function pastLogs(Request $request)
    {
        Log::info('Educator accessed past academic logs.', [
            'user_id' => Auth::id(),
            'month' => $request->month ?? null,
            'date' => $request->date ?? null,
        ]);
        $query = Academic::with(['studentDetail'])
            ->orderBy('academic_date', 'desc')
            ->orderBy('time_out', 'desc');

        if ($request->has('month') && !empty($request->month)) {
            $month = date('m', strtotime($request->month));
            $year = date('Y', strtotime($request->month));
            $query->whereMonth('academic_date', $month)
                ->whereYear('academic_date', $year);
        }

        if ($request->has('date') && !empty($request->date)) {
            $query->whereDate('academic_date', $request->date);
        }

        $academicLogs = $query->get();

        if ($academicLogs->isEmpty() && $request->has('date') && !empty($request->date)) {
            Log::info('No logs found for selected date, creating empty logs.', [
                'date' => $request->date,
            ]);
            $allStudents = StudentDetail::all();

            foreach ($allStudents as $student) {
                Academic::firstOrCreate([
                    'student_id' => $student->student_id,
                    'academic_date' => $request->date,
                ], [
                    'time_out' => null,
                    'time_out_remark' => 'No Time Out',
                    'time_in' => null,
                    'time_in_remark' => 'No Time In',
                ]);
            }

            $academicLogs = Academic::with('studentDetail')
                ->whereDate('academic_date', $request->date)
                ->get();
        }

        $batches = StudentDetail::distinct()->pluck('batch')->sort();
        $groups = StudentDetail::distinct()->pluck('group')->sort();

        return view('user-educator.academicmonitor', [
            'academicLogs' => $academicLogs,
            'isPastLogs' => true,
            'selectedMonth' => $request->month ?? '',
            'selectedDate' => $request->date ?? '',
            'batches' => $batches,
            'groups' => $groups,
        ]);
    }

    /**
     * Reset logs for the previous day.
     */
    public function resetDailyLogs()
    {
        $yesterday = now()->subDay()->format('Y-m-d');
        Log::info('Resetting daily logs for yesterday.', [
            'date' => $yesterday,
        ]);

        $allStudents = StudentDetail::all();

        foreach ($allStudents as $student) {
            Academic::firstOrCreate([
                'student_id' => $student->student_id,
                'academic_date' => $yesterday,
            ], [
                'time_out' => null,
                'time_out_remark' => 'No Time Out',
                'time_in' => null,
                'time_in_remark' => 'No Time In',
            ]);
        }
    }

    /**
     * Update educator consideration in an academic log.
     */
    public function updateConsideration(Request $request, $id)
    {
        try {
            $request->validate([
                'educator_consideration' => 'required|in:Excused,Not Excused',
                'consideration_type' => 'required|in:time_in,time_out'
            ]);

            $academic = Academic::findOrFail($id);
            $considerationType = $request->consideration_type;

            if ($considerationType === 'time_out') {
                // Validate time out consideration
                if (!$academic->time_out) {
                    Log::warning('Cannot set time out consideration: Student has not logged time out', [
                        'academic_id' => $id,
                        'student_id' => $academic->student_id
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot set consideration: Student must log time out first'
                    ]);
                }

                if ($academic->time_out_remark !== 'Late') {
                    Log::warning('Cannot set time out consideration: Student is not late for time out', [
                        'academic_id' => $id,
                        'student_id' => $academic->student_id,
                        'time_out_remark' => $academic->time_out_remark
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot set consideration: Student must be late to set any consideration'
                    ]);
                }

                $updateField = 'time_out_consideration';
                $logMessage = 'Updated academic time out consideration';
            } else {
                // Validate time in consideration (existing logic)
                if (!$academic->time_in) {
                    Log::warning('Cannot set time in consideration: Student has not logged time in', [
                        'academic_id' => $id,
                        'student_id' => $academic->student_id
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot set consideration: Student must log time in first'
                    ]);
                }

                if ($academic->time_in_remark !== 'Late') {
                    Log::warning('Cannot set time in consideration: Student is not late for time in', [
                        'academic_id' => $id,
                        'student_id' => $academic->student_id,
                        'time_in_remark' => $academic->time_in_remark
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot set consideration: Student must be late to set any consideration'
                    ]);
                }

                $updateField = 'educator_consideration';
                $logMessage = 'Updated academic time in consideration';
            }

            DB::beginTransaction();

            try {
                $academic->update([
                    $updateField => $request->educator_consideration,
                    'updated_by' => session('user.user_fname') . ' ' . session('user.user_lname'),
                    'updated_at' => now()
                ]);

                DB::commit();
                Log::info($logMessage, [
                    'academic_id' => $id,
                    'student_id' => $academic->student_id,
                    'consideration_type' => $considerationType,
                    'consideration' => $request->educator_consideration
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Consideration updated successfully.'
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('Failed to update academic consideration', [
                'error' => $e->getMessage(),
                'academic_id' => $id,
                'consideration_type' => $request->consideration_type ?? 'unknown'
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to update consideration: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get the most current active schedule for a student
     * This method ensures we always get the latest schedule that should be followed
     */
    private function getCurrentActiveSchedule($student, $today)
    {
        // Disable query log temporarily to ensure fresh queries
        DB::disableQueryLog();

        Log::info('Fetching current active schedule', [
            'student_id' => $student->student_id,
            'batch' => $student->batch,
            'group' => $student->group,
            'day' => $today,
            'query_time' => now()->format('Y-m-d H:i:s.u')
        ]);

        // First priority: Check for irregular schedule (student-specific)
        $irregularSchedule = Schedule::where([
            ['student_id', $student->student_id],
            ['day_of_week', $today],
            ['schedule_type', 'academic'] // Only look for academic irregular schedules
        ])->where(function ($query) {
            $query->whereNull('valid_until') // Permanent schedule
                ->orWhere('valid_until', '>=', Carbon::today()); // Or valid today/future
        })
        ->orderBy('updated_at', 'desc') // Most recently updated first
        ->orderBy('created_at', 'desc')  // Then most recently created
        ->first();

        if ($irregularSchedule) {
            Log::info('Using irregular schedule for student', [
                'student_id' => $student->student_id,
                'schedule_id' => $irregularSchedule->schedule_id,
                'time_out' => $irregularSchedule->getRawTimeOut(),
                'time_in' => $irregularSchedule->getRawTimeIn(),
                'updated_at' => $irregularSchedule->updated_at,
                'valid_until' => $irregularSchedule->valid_until,
                'query_time' => now()->format('Y-m-d H:i:s.u')
            ]);

            // Re-enable query log
            DB::enableQueryLog();

            return ['schedule' => $irregularSchedule, 'type' => 'irregular'];
        }

        // Second priority: Check for batch schedule (group-based)
        $batchSchedule = Schedule::where([
            ['batch', $student->batch],
            ['pn_group', $student->group],
            ['day_of_week', $today],
            ['schedule_type', 'academic'] // Only look for academic batch schedules
        ])->where(function ($query) {
            $query->whereNull('valid_until') // Permanent schedule
                ->orWhere('valid_until', '>=', Carbon::today()); // Or valid today/future
        })
        ->orderBy('updated_at', 'desc') // Most recently updated first
        ->orderBy('created_at', 'desc')  // Then most recently created
        ->first();

        if ($batchSchedule) {
            Log::info('Using batch schedule for student', [
                'student_id' => $student->student_id,
                'batch' => $student->batch,
                'group' => $student->group,
                'schedule_id' => $batchSchedule->schedule_id,
                'time_out' => $batchSchedule->getRawTimeOut(),
                'time_in' => $batchSchedule->getRawTimeIn(),
                'updated_at' => $batchSchedule->updated_at,
                'valid_until' => $batchSchedule->valid_until,
                'query_time' => now()->format('Y-m-d H:i:s.u')
            ]);

            // Re-enable query log
            DB::enableQueryLog();

            return ['schedule' => $batchSchedule, 'type' => 'batch'];
        }

        Log::warning('No active schedule found for student', [
            'student_id' => $student->student_id,
            'batch' => $student->batch,
            'group' => $student->group,
            'day' => $today,
            'query_time' => now()->format('Y-m-d H:i:s.u')
        ]);

        // Re-enable query log
        DB::enableQueryLog();

        return ['schedule' => null, 'type' => 'none'];
    }

    /**
     * Debug function to check schedule for a student
     */
    public function debugSchedule(Request $request)
    {
        $studentId = $request->get('student_id');
        if (!$studentId) {
            return response()->json(['error' => 'Please provide student_id parameter']);
        }

        $student = StudentDetail::where('student_id', $studentId)->first();
        if (!$student) {
            return response()->json(['error' => 'Student not found']);
        }

        $today = Carbon::now()->format('l');

        // Check irregular schedule
        $irregularSchedule = Schedule::where([
            ['student_id', $student->student_id],
            ['day_of_week', $today],
            ['schedule_type', 'academic'] // Only look for academic irregular schedules
        ])->where(function ($query) {
            $query->whereNull('valid_until')
                ->orWhereDate('valid_until', '>=', Carbon::today());
        })->first();

        // Check batch schedule
        $batchSchedule = Schedule::where([
            ['batch', $student->batch],
            ['pn_group', $student->group],
            ['day_of_week', $today],
            ['schedule_type', 'academic'] // Only look for academic batch schedules
        ])->where(function ($query) {
            $query->whereNull('valid_until')
                ->orWhereDate('valid_until', '>=', Carbon::today());
        })->first();

        $currentTime = Carbon::parse(now());

        return response()->json([
            'student_info' => [
                'student_id' => $student->student_id,
                'batch' => $student->batch,
                'group' => $student->group,
                'name' => $student->user->name ?? 'N/A'
            ],
            'current_info' => [
                'current_time' => $currentTime->format('Y-m-d H:i:s'),
                'current_day' => $today,
                'today_date' => Carbon::today()->format('Y-m-d')
            ],
            'irregular_schedule' => $irregularSchedule ? [
                'schedule_id' => $irregularSchedule->schedule_id,
                'time_out' => $irregularSchedule->time_out,
                'time_in' => $irregularSchedule->time_in,
                'day_of_week' => $irregularSchedule->day_of_week,
                'valid_until' => $irregularSchedule->valid_until,
                'can_logout_now' => $currentTime <= Carbon::parse($irregularSchedule->time_out)
            ] : null,
            'batch_schedule' => $batchSchedule ? [
                'schedule_id' => $batchSchedule->schedule_id,
                'time_out' => $batchSchedule->time_out,
                'time_in' => $batchSchedule->time_in,
                'day_of_week' => $batchSchedule->day_of_week,
                'batch' => $batchSchedule->batch,
                'group' => $batchSchedule->pn_group,
                'valid_until' => $batchSchedule->valid_until,
                'can_logout_now' => $currentTime <= Carbon::parse($batchSchedule->time_out)
            ] : null,
            'schedule_used' => $irregularSchedule ? 'irregular' : ($batchSchedule ? 'batch' : 'none')
        ]);
    }
}
