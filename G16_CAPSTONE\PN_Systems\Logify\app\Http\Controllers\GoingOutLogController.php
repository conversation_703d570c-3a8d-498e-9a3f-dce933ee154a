<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PNUser;
use App\Models\StudentDetail;
use Illuminate\Support\Facades\Auth;
use App\Models\Going_out;
use App\Models\Schedule;
use App\Models\NotificationView;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class GoingOutLogController extends Controller
{
    /**
     * Handle going-out log creation for the current user.
     */

    public function logoutForm()
    {
        Log::info('Accessed going out logout form.', [
            'user_id' => Auth::id(),
        ]);
        $loginLog = false;
        $logoutLog = true;
        // dd($loginLog, $logoutLog);
        return view('user-student.goingOutLog', ['loginLog' => $loginLog, 'logoutLog' => $logoutLog]);
    }

    public function loginForm()
    {
        Log::info('Accessed going out login form.', [
            'user_id' => Auth::id(),
        ]);
        $loginLog = true;
        $logoutLog = false;
        return view('user-student.goingOutLog', ['loginLog' => $loginLog, 'logoutLog' => $logoutLog]);
    }

    /**
     * Store a new going-out log (logout).
     */
    public function logTimeOut(Request $request)
    {
        // dd($request->all());
        $request->validate([
            'student_id' => 'required|exists:student_details,student_id',
            'destination' => 'required|string',
            'purpose' => 'required|string',
        ]);

        $student = StudentDetail::where('student_id', $request->student_id)->first();

        if (!$student) {
            Log::warning('Student not found for going out logTimeOut.', [
                'student_id' => $request->student_id,
            ]);
            return redirect()->route('goingOutLogForms.show')->with('error', 'Student not found.');
        }

        $today = now()->format('l');

        // Clear any potential model cache and force fresh query
        Schedule::clearBootedModels();

        // First check for individual going-out schedule for this specific student
        $schedule = Schedule::where([
                ['student_id', $student->student_id],
                ['day_of_week', $today],
                ['schedule_type', 'going_out'] // Only look for going-out schedules
            ])->where(function ($query) {
                $query->whereNull('valid_until') // Permanent schedule
                    ->orWhere('valid_until', '>=', Carbon::today()); // Or valid today/future
            })
            ->orderBy('updated_at', 'desc') // Most recently updated first
            ->orderBy('created_at', 'desc')  // Then most recently created
            ->first();

        // If no individual schedule found, fall back to general going-out schedule
        if (!$schedule) {
            $schedule = Schedule::where([
                    ['gender', $student->user->gender],
                    ['day_of_week', $today],
                    ['student_id', null], // General schedules have null student_id
                    ['schedule_type', 'going_out'] // Only look for going-out schedules
                ])->where(function ($query) {
                    $query->whereNull('valid_until') // Permanent schedule
                        ->orWhere('valid_until', '>=', Carbon::today()); // Or valid today/future
                })
                ->orderBy('updated_at', 'desc') // Most recently updated first
                ->orderBy('created_at', 'desc')  // Then most recently created
                ->first();
        }

        if (!$schedule) {
            Log::warning('Student attempted going out logout with no schedule', [
                'student_id' => $student->student_id,
                'gender' => $student->user->gender,
                'day' => $today,
            ]);
            return redirect()->route('goingOutLogForms.show')
                ->with('error', 'No schedule set for today! Please contact your educator for assistance.');
        }

        // CORRECTED LOGIC: time_out is the START time, time_in is the END time
        $currentTime = Carbon::now();
        $currentDate = $currentTime->toDateString();

        // Parse schedule times with today's date for proper comparison
        $scheduleStartTime = Carbon::parse($currentDate . ' ' . $schedule->getRawTimeOut());  // When students can start going out
        $scheduleEndTime = Carbon::parse($currentDate . ' ' . $schedule->getRawTimeIn());  // When students must return

        // Check if it's too early to logout (before scheduled start time)
        if ($currentTime->lt($scheduleStartTime)) {
            Log::warning('Student attempted going out logout before schedule started', [
                'student_id' => $student->student_id,
                'current_time' => $currentTime->format('H:i:s'),
                'schedule_start_time' => $scheduleStartTime->format('H:i:s'),
                'raw_schedule_time_out' => $schedule->getRawTimeOut(),
                'raw_schedule_time_in' => $schedule->getRawTimeIn(),
                'current_full_time' => $currentTime->format('Y-m-d H:i:s'),
                'schedule_start_full_time' => $scheduleStartTime->format('Y-m-d H:i:s')
            ]);

            return redirect()->route('goingOutLogForms.show')
                ->with('error', 'Going out period has not started yet! Going out starts at ' . $scheduleStartTime->format('g:i A') . '.');
        }

        // Check if it's too late to logout (after scheduled end time)
        if ($currentTime->gt($scheduleEndTime)) {
            Log::warning('Student attempted going out logout after schedule ended', [
                'student_id' => $student->student_id,
                'current_time' => $currentTime->format('H:i:s'),
                'schedule_end_time' => $scheduleEndTime->format('H:i:s'),
                'raw_schedule_time_out' => $schedule->getRawTimeOut(),
                'raw_schedule_time_in' => $schedule->getRawTimeIn(),
                'current_full_time' => $currentTime->format('Y-m-d H:i:s'),
                'schedule_end_full_time' => $scheduleEndTime->format('Y-m-d H:i:s')
            ]);

            return redirect()->route('goingOutLogForms.show')
                ->with('error', 'Going out period has ended! Going out ended at ' . $scheduleEndTime->format('g:i A') . '.');
        }

        // Check if it's Sunday (single session only) or weekday (multiple sessions allowed)
        $isSunday = now()->isSunday();
        $todayDate = now()->toDateString();

        if ($isSunday) {
            // Sunday: Only one session allowed (existing logic)
            $goingOut = Going_out::where('student_id', $request->student_id)
                ->whereDate('going_out_date', $todayDate)
                ->first();

            // If no record exists on Sunday, create one (in case daily populate didn't run)
            if (!$goingOut) {
                $goingOut = Going_out::create([
                    'student_id' => $request->student_id,
                    'going_out_date' => $todayDate,
                    'session_number' => 1,
                    'session_status' => 'active',
                ]);
            }

  // Check if monitor already logged out this student (priority check)
            if ($goingOut->monitor_logged_out) {
                return redirect()->route('goingOutLogForms.show')->with('error', 'Monitor has already logged you out. You cannot log out again.');
            }

            if ($goingOut->time_out) {
                Log::info('Time out already logged for going out on Sunday.', [
                    'student_id' => $request->student_id,
                    'going_out_date' => $todayDate,
                ]);
                return redirect()->route('goingOutLogForms.show')->with('error', 'You already log out for this date.');
            }
        } else {
            // Monday-Saturday: Multiple sessions allowed, but must complete previous session first
            if (!Going_out::canStartNewSession($request->student_id, $todayDate)) {
                $activeSession = Going_out::getCurrentActiveSession($request->student_id, $todayDate);
                if ($activeSession && !$activeSession->time_in) {
                    return redirect()->route('goingOutLogForms.show')->with('error', 'You must log in from your current going out session before starting a new one.');
                }
            }
        }

        // Calculate remark based on schedule with fixed timing (no grace period for going out)
        $createdBy = $student->user->user_fname . ' ' . $student->user->user_lname;

        // Since we reach here, student is logging out during valid schedule period
        // Always mark as "On Time" since students can only log out during scheduled time
        $remark = 'On Time';

        if ($isSunday) {
            // Sunday: Use existing updateOrCreate logic
            Going_out::updateOrCreate(
                [
                    'student_id' => $request->student_id,
                    'going_out_date' => $todayDate,
                ],
                [
                    'session_number' => 1,
                    'session_status' => 'active',
                    'destination' => $request->destination,
                    'purpose' => $request->purpose,
                    'time_out' => now(),
                    'time_out_remark' => $remark,
                    'created_by' => $createdBy,
                    'created_at' => now()
                ]
            );
        } else {
            // Monday-Saturday: Create new session
            $sessionNumber = Going_out::getNextSessionNumber($request->student_id, $todayDate);

            Going_out::create([
                'student_id' => $request->student_id,
                'going_out_date' => $todayDate,
                'session_number' => $sessionNumber,
                'session_status' => 'active',
                'destination' => $request->destination,
                'purpose' => $request->purpose,
                'time_out' => now(),
                'time_out_remark' => $remark,
                'created_by' => $createdBy,
                'created_at' => now()
            ]);
        }

        // Get the latest going out record for success message
        $latestGoingOut = Going_out::where('student_id', $request->student_id)
            ->whereDate('going_out_date', $todayDate)
            ->orderBy('session_number', 'desc')
            ->first();

        $log_remark = $latestGoingOut ? $latestGoingOut->time_out_remark : $remark;

        return redirect()->route('goingOutLogForms.show')->with('success', 'Time-out recorded successfully. You are ' . $log_remark . '.');
    }

    /**
     * Store a new going-out log (login).
     */
    public function logTimeIn(Request $request)
    {
        // dd($request);
        $request->validate([
            'student_id' => 'required|exists:student_details,student_id',
        ]);

        $student = StudentDetail::where('student_id', $request->student_id)->first();

        if (!$student) {
            Log::warning('Student not found for going out logTimeIn.', [
                'student_id' => $request->student_id,
            ]);
            return redirect()->route('goingOutLogForms.show')->with('error', 'Student not found.');
        }

      $today = Carbon::now()->format('l');

        // Clear any potential model cache and force fresh query
        Schedule::clearBootedModels();

        // First check for individual going-out schedule for this specific student
        $schedule = Schedule::where([
                ['student_id', $student->student_id],
                ['day_of_week', $today],
                ['schedule_type', 'going_out'] // Only look for going-out schedules
            ])->where(function ($query) {
                $query->whereNull('valid_until') // Permanent schedule
                    ->orWhere('valid_until', '>=', Carbon::today()); // Or valid today/future
            })
            ->orderBy('updated_at', 'desc') // Most recently updated first
            ->orderBy('created_at', 'desc')  // Then most recently created
            ->first();

        // If no individual schedule found, fall back to general going-out schedule
        if (!$schedule) {
            $schedule = Schedule::where([
                    ['gender', $student->user->gender],
                    ['day_of_week', $today],
                    ['student_id', null], // General schedules have null student_id
                    ['schedule_type', 'going_out'] // Only look for going-out schedules
                ])->where(function ($query) {
                    $query->whereNull('valid_until') // Permanent schedule
                        ->orWhere('valid_until', '>=', Carbon::today()); // Or valid today/future
                })
                ->orderBy('updated_at', 'desc') // Most recently updated first
                ->orderBy('created_at', 'desc')  // Then most recently created
                ->first();
        }

        if (!$schedule) {
            Log::warning('Student attempted going out login with no schedule', [
                'student_id' => $student->student_id,
                'gender' => $student->user->gender,
                'day' => $today,
            ]);
            return redirect()->route('goingOutLogForms.show')
                ->with('error', 'No schedule set for today! Please contact your educator for assistance.');
        }

        // Going out login: No time restrictions - students can log in anytime after logout
        $todayDate = now()->toDateString();
        $isSunday = now()->isSunday();

        if ($isSunday) {
            // Sunday: Single session logic
            $goingOut = Going_out::where('student_id', $request->student_id)
                ->whereDate('going_out_date', $todayDate)
                ->first();

            if (!$goingOut) {
                Log::warning('Going out log not found for time in on Sunday.', [
                    'student_id' => $request->student_id,
                    'going_out_date' => $todayDate,
                ]);
                return redirect()->route('goingOutLogForms.show')->with('error', 'The Logs does not reset yet. Wait until 12:00 AM.');
            }

            if (!$goingOut->time_out) {
                Log::info('Time out not yet logged for this date on Sunday.', [
                    'student_id' => $request->student_id,
                    'going_out_date' => $todayDate,
                ]);
                return redirect()->route('goingOutLogForms.show')->with('error', 'You have not log out for this date.');
            }

            // Check if monitor already logged in this student (priority check)
            if ($goingOut->monitor_logged_in) {
                return redirect()->route('goingOutLogForms.show')->with('error', 'Monitor has already logged you in. You cannot log in again.');
            }

            if ($goingOut->time_in) {
                Log::info('Time in already logged for going out on Sunday.', [
                    'student_id' => $request->student_id,
                    'going_out_date' => $todayDate,
                ]);
                return redirect()->route('goingOutLogForms.show')->with('error', 'You already log in for this date.');
            }
        } else {
            // Monday-Saturday: Multiple sessions - find the current active session
            $goingOut = Going_out::getCurrentActiveSession($request->student_id, $todayDate);

            if (!$goingOut) {
                Log::warning('No active going out session found for time in.', [
                    'student_id' => $request->student_id,
                    'going_out_date' => $todayDate,
                ]);
                return redirect()->route('goingOutLogForms.show')->with('error', 'You have not logged out yet or no active session found.');
            }

            if (!$goingOut->time_out) {
                Log::info('Time out not yet logged for current session.', [
                    'student_id' => $request->student_id,
                    'going_out_date' => $todayDate,
                    'session_number' => $goingOut->session_number,
                ]);
                return redirect()->route('goingOutLogForms.show')->with('error', 'You have not log out for this session.');
            }

            // Check if monitor already logged in this student (priority check)
            if ($goingOut->monitor_logged_in) {
                return redirect()->route('goingOutLogForms.show')->with('error', 'Monitor has already logged you in. You cannot log in again.');
            }

            if ($goingOut->time_in) {
                Log::info('Time in already logged for current going out session.', [
                    'student_id' => $request->student_id,
                    'going_out_date' => $todayDate,
                    'session_number' => $goingOut->session_number,
                ]);
                return redirect()->route('goingOutLogForms.show')->with('error', 'You already log in for this session.');
            }
        }
        if ($goingOut) {
            // Calculate remark based on schedule with fixed timing (no grace period for going out)
            $scheduleEndTime = Carbon::parse($schedule->getRawTimeIn());
            $updatedBy = $student->user->user_fname . ' ' . $student->user->user_lname;
            $currentTime = Carbon::parse(now());

            $remark = 'On Time'; // Default
            if ($schedule) {
                // Fixed timing logic for going out (no grace period)
                // On Time: before or at scheduled end time
                // Late: after scheduled end time
                if ($currentTime->greaterThan($scheduleEndTime)) {
                    $remark = 'Late';
                } else {
                    $remark = 'On Time';
                }
            }

            $goingOut->update([
                'time_in' => now(),
                'time_in_remark' => $remark,
                'session_status' => 'completed',
                'updated_by' => $updatedBy,
                'updated_at' => now()
            ]);
        }

        // Get the remark from the updated record
        $log_remark = $goingOut->time_in_remark ?: $remark;

        // dd($request);
        return redirect()->route('goingOutLogForms.show')->with('success', 'Time-in logged successfully. You are ' . $log_remark .'.');
    }

    /**
     * Monitor going-out logs for the current day.
     */
    public function monitor(Request $request)
    {
        // Mark going out notifications as viewed when accessing the monitor page
        NotificationView::markAsViewed('goingout');

        $query = Going_out::query();

        if ($request->has('date') && !empty($request->date)) {
            $query->whereDate('going_out_date', $request->date);
        } else {
            $query->whereDate('going_out_date', now()->format('Y-m-d'));
        }

        // Filter by batch if present
        if ($request->has('batch') && !empty($request->batch)) {
            $query->whereHas('studentDetail', function ($q) use ($request) {
                $q->where('batch', $request->batch);
            });
        }

        // Filter by group if present
        if ($request->has('group') && !empty($request->group)) {
            $query->whereHas('studentDetail', function ($q) use ($request) {
                $q->where('group', $request->group);
            });
        }

        $selectedDate = $request->date ?? now()->format('Y-m-d');

        // Order by latest activity (most recent time_out or time_in operations first)
        // Also order by session number to show multiple sessions properly
        $todayLogs = $query->with(['studentDetail'])
            ->orderBy('session_number', 'desc')
            ->get()
            ->sortByDesc(function ($log) {
                // Get the latest timestamp between time_out and time_in
                $timeOutTimestamp = $log->time_out ? strtotime($log->going_out_date . ' ' . $log->time_out) : 0;
                $timeInTimestamp = $log->time_in ? strtotime($log->going_out_date . ' ' . $log->time_in) : 0;
                return max($timeOutTimestamp, $timeInTimestamp);
            })
            ->values();

        $batches = StudentDetail::distinct()->pluck('batch')->sort();
        $groups = StudentDetail::distinct()->pluck('group')->sort();

        Log::info('Going out viewed.', [
            'goingOutLogs' => $todayLogs,
            'batches' => $batches,
            'groups' => $groups,
            'selectedDate' => $request->date ?? now()->format('Y-m-d'),
        ]);

        return view('user-educator.goingoutmonitor', [
            'goingOutLogs' => $todayLogs,
            'batches' => $batches,
            'groups' => $groups,
            'selectedDate' => $selectedDate,
        ]);
    }

    /**
     * Monitor past going-out logs with filtering capabilities.
     */
    public function pastLogs(Request $request)
    {
        Log::info('Educator accessed past going out logs.', [
            'user_id' => Auth::id(),
            'month' => $request->month ?? null,
            'date' => $request->date ?? null,
        ]);
        $query = Going_out::with(['studentDetail'])
            ->orderBy('going_out_date', 'desc')
            ->orderBy('time_out', 'desc');

        if ($request->has('month') && !empty($request->month)) {
            $month = date('m', strtotime($request->month));
            $year = date('Y', strtotime($request->month));
            $query->whereMonth('going_out_date', $month)
                ->whereYear('going_out_date', $year);
        }

        if ($request->has('date') && !empty($request->date)) {
            $query->whereDate('going_out_date', $request->date);
        }

        $goingOutLogs = $query->get();

        if ($goingOutLogs->isEmpty() && $request->has('date') && !empty($request->date)) {
            Log::info('No logs found for selected date, creating empty going out logs.', [
                'date' => $request->date,
            ]);
            $allStudents = StudentDetail::all();
            $selectedDate = Carbon::parse($request->date);
            $isSunday = $selectedDate->isSunday();

            foreach ($allStudents as $student) {
                if ($isSunday) {
                    // Sunday: Create only one session
                    Going_out::firstOrCreate([
                        'student_id' => $student->student_id,
                        'going_out_date' => $request->date,
                        'session_number' => 1,
                    ], [
                        'session_status' => 'active',
                        'time_out' => null,
                        'time_out_remark' => 'No Time Out',
                        'time_in' => null,
                        'time_in_remark' => 'No Time In',
                    ]);
                } else {
                    // Monday-Saturday: Create one empty session for display purposes only
                    Going_out::firstOrCreate([
                        'student_id' => $student->student_id,
                        'going_out_date' => $request->date,
                        'session_number' => 1,
                    ], [
                        'session_status' => 'active',
                        'time_out' => null,
                        'time_out_remark' => 'No Time Out',
                        'time_in' => null,
                        'time_in_remark' => 'No Time In',
                    ]);
                }
            }

            $goingOutLogs = Going_out::with('studentDetail')
                ->whereDate('going_out_date', $request->date)
                ->get();
        }

        $batches = StudentDetail::distinct()->pluck('batch')->sort();
        $groups = StudentDetail::distinct()->pluck('group')->sort();

        return view('user-educator.goingoutmonitor', [
            'goingOutLogs' => $goingOutLogs,
            'isPastLogs' => true,
            'selectedMonth' => $request->month ?? '',
            'selectedDate' => $request->date ?? '',
            'batches' => $batches,
            'groups' => $groups,
        ]);
    }

    /**
     * Update the educator's consideration for a going out log.
     */
    public function updateConsideration(Request $request, $id)
    {
        try {
            $request->validate([
                'educator_consideration' => 'required|in:Excused,Not Excused',
                'consideration_type' => 'required|in:time_in,time_out'
            ]);

            $goingOut = Going_out::findOrFail($id);
            $considerationType = $request->consideration_type;

            if ($considerationType === 'time_out') {
                // Validate time out consideration
                if (!$goingOut->time_out) {
                    Log::warning('Cannot set time out consideration: Student has not logged time out', [
                        'going_out_id' => $id,
                        'student_id' => $goingOut->student_id
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot set consideration: Student must log time out first'
                    ]);
                }

                if (!in_array($goingOut->time_out_remark, ['Late', 'Automatic'])) {
                    Log::warning('Cannot set time out consideration: Student is not late for time out', [
                        'going_out_id' => $id,
                        'student_id' => $goingOut->student_id,
                        'time_out_remark' => $goingOut->time_out_remark
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot set consideration: Student must be late to set any consideration'
                    ]);
                }

                $updateField = 'time_out_consideration';
                $logMessage = 'Updated going out time out consideration';
            } else {
                // Validate time in consideration (existing logic)
                if (!$goingOut->time_in) {
                    Log::warning('Cannot set time in consideration: Student has not logged time in', [
                        'going_out_id' => $id,
                        'student_id' => $goingOut->student_id
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot set consideration: Student must log time in first'
                    ]);
                }

                if ($goingOut->time_in_remark !== 'Late') {
                    Log::warning('Cannot set time in consideration: Student is not late for time in', [
                        'going_out_id' => $id,
                        'student_id' => $goingOut->student_id,
                        'time_in_remark' => $goingOut->time_in_remark
                    ]);
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot set consideration: Student must be late to set any consideration'
                    ]);
                }

                $updateField = 'educator_consideration';
                $logMessage = 'Updated going out time in consideration';
            }

            DB::beginTransaction();

            try {
                $goingOut->update([
                    $updateField => $request->educator_consideration
                ]);

                DB::commit();
                Log::info($logMessage, [
                    'going_out_id' => $id,
                    'student_id' => $goingOut->student_id,
                    'consideration_type' => $considerationType,
                    'consideration' => $request->educator_consideration
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Consideration updated successfully.'
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('Failed to update going out consideration', [
                'error' => $e->getMessage(),
                'going_out_id' => $id,
                'consideration_type' => $request->consideration_type ?? 'unknown'
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to update consideration: ' . $e->getMessage()
            ]);
        }
    }
}
