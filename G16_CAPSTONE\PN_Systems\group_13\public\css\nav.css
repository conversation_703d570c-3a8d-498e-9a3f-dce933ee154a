/* Base Styles */
:root {
    --sidebar-width: 250px;
    --topbar-height: 80px;
    --content-padding: 20px;
    --primary-color: #22bbea;
    --sidebar-bg: #ffffff;
    --content-bg: #f8f9fa;
    --text-color: #333333;
    --hover-bg: #e3f2fd;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: 'Poppins', sans-serif !important;
}

/* Preserve icon fonts */
.fas, .far, .fal, .fab, .fa,
[class*="fa-"],
.material-icons,
.glyphicon {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 5 Free", "Font Awesome 5 Pro", "Material Icons", "Glyphicons Halflings" !important;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Poppins', sans-serif !important;
    background-color: var(--content-bg);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    color: var(--text-color);
    line-height: 1.6;
}

/* Top Bar */
.top-bar {
    height: var(--topbar-height);
    background: var(--primary-color);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 30px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.PN-logo {
    height: 40px;
    width: auto;
}

/* Layout Container */
.layout-container {
    display: flex;
    flex: 1;
    min-height: calc(100vh - var(--topbar-height));
    margin-top: var(--topbar-height);
    position: relative;
    width: 100%;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: var(--sidebar-bg);
    position: fixed;
    top: var(--topbar-height);
    left: 0;
    bottom: 0;
    overflow-y: auto;
    border-right: 2px solid var(--primary-color);
    z-index: 100;
    transition: transform 0.3s ease;
}

.menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu li {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    font-size: 15px;
    cursor: pointer;
    transition: background-color 0.3s;
    color: var(--text-color);
}

.menu li a {
    text-decoration: none;
    color: inherit;
    display: flex;
    align-items: center;
    width: 100%;
}

.menu li img {
    width: 20px;
    height: 20px;
    margin-right: 12px;
    flex-shrink: 0;
}

.menu li:hover {
    background-color: var(--hover-bg);
}

.menu li.active {
    background-color: rgba(34, 187, 234, 0.1);
    border-left: 4px solid var(--primary-color);
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    width: calc(100% - var(--sidebar-width));
    min-height: 100%;
    background-color: var(--content-bg);
    transition: margin-left 0.3s ease;
}

.content {
    padding: var(--content-padding);
    max-width: 100%;
    margin: 0 auto;
    box-sizing: border-box;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .content {
        padding: 15px;
    }
}

@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
        width: 100%;
    }
    
    .menu-toggle {
        display: block;
    }
}

/* Dashboard Specific Styles */
.dashboard-container {
    padding: var(--content-padding);
}

.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.chart-wrapper {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin: 20px auto;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Utility Classes */
.text-center { text-align: center; }
.mt-20 { margin-top: 20px; }
.mb-20 { margin-bottom: 20px; }

/* Hide scrollbar for Chrome, Safari and Opera */
.sidebar::-webkit-scrollbar {
    width: 5px;
}

.sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 5px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #555;
}
