<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Visitor;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\NotificationView;

class VisitorLogController extends Controller
{
    public function monitor(Request $request)
    {
        // Reset visitor notifications when educator accesses the monitor
        NotificationView::markAsViewed('visitor');

        $query = Visitor::query();

        if ($request->has('date') && !empty($request->date)) {
            $query->whereDate('visitor_date', $request->date);
        } else {
            $query->whereDate('visitor_date', now()->format('Y-m-d'));
        }

        $visitors = $query->orderBy('visitor_date', 'desc')
                         ->orderBy('time_in', 'desc')
                         ->get();

        Log::info('Visitor monitor viewed.', [
            'date' => $request->date ?? now()->format('Y-m-d'),
            'count' => $visitors->count(),
        ]);

        return view('user-educator.visitormonitor', [
            'visitors' => $visitors,
            'selectedDate' => $request->date ?? now()->format('Y-m-d'),
        ]);
    }
}
