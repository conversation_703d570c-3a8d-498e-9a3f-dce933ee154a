<x-educatorLayout>
    <!-- Real-time Notification Container -->
    <div id="notification-container" class="fixed z-50 max-w-sm space-y-3 top-4 right-4">
        <!-- Notification cards will be dynamically inserted here -->
    </div>

    <!-- Modal -->
@foreach (['academicSchedule', 'irregularSchedule'] as $scheduleType)
    @php $schedule = $$scheduleType; @endphp
    @if ($schedule->isNotEmpty() && optional($schedule->first())->valid_until && \Carbon\Carbon::parse(optional($schedule->first())->valid_until)->lessThanOrEqualTo(now()->addDays(3)))
        <div id="{{ $scheduleType }}Modal" class="fixed inset-0 z-50 flex items-center justify-center hidden bg-black bg-opacity-40">
            <div class="relative w-full max-w-md p-6 bg-white rounded-lg shadow-lg">
                <button onclick="closeModal('{{ $scheduleType }}Modal')" class="absolute text-2xl text-gray-400 top-2 right-2 hover:text-gray-700">&times;</button>
                <h2 class="mb-4 text-xl font-bold">
                    {{ ucfirst(str_replace('Schedule', '', $scheduleType)) }} Expiry Notice
                </h2>
                <p class="mb-4">
                    The schedule will expire soon (by {{ \Carbon\Carbon::parse(optional($schedule->first())->valid_until)->format('g:i A, M d, Y') }}).
                    Please update or review the schedule.
                </p>
            </div>
        </div>
    @endif
@endforeach

<script>
document.addEventListener('DOMContentLoaded', function() {
    feather.replace();

    const modalIds = ['academicScheduleModal', 'irregularScheduleModal'];
    modalIds.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (modal) modal.classList.remove('hidden');
    });

    refreshAllData();
    setInterval(refreshAllData, 15000);

    // Initialize real-time notifications
    initializeRealtimeNotifications();
});

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('hidden');
        modal.style.display = 'none'; // Ensures it's removed from view
    }
}

// Real-time notification system (synchronized with navigation badges)
let notificationQueue = [];
let isProcessingQueue = false;
let isFirstLoad = true;
let shownNotifications = new Set(); // Track shown notifications to prevent duplicates
let lastAcademicViewed = null;
let lastGoingOutViewed = null;

function initializeRealtimeNotifications() {
    // Check for new activities every 3 seconds (same as navigation badges)
    setInterval(checkForNewActivities, 3000);

    // Clean up old notification tracking every 5 minutes to prevent memory buildup
    setInterval(() => {
        if (shownNotifications.size > 100) {
            console.log('🧹 Cleaning up old notification tracking');
            shownNotifications.clear();
        }
    }, 5 * 60 * 1000);

    console.log('🔔 Real-time notification system initialized (synced with navigation badges)');
}

function checkForNewActivities() {
    const currentTime = new Date().toISOString();

    // console.log('🔍 Checking for new activities...');
    fetch('/educator/recent-activities')
        .then(response => response.json())
        .then(data => {
            // console.log('📡 Recent activities response:', data);
            if (data.success) {
                console.log(`🔍 Checking activities (synced with navigation badges)`);
                console.log(`📊 Found ${data.activities.length} activities`);
                console.log(`📅 Academic last viewed: ${data.academic_last_viewed}`);
                console.log(`📅 Going out last viewed: ${data.goingout_last_viewed}`);
                console.log(`📅 Visitor last viewed: ${data.visitor_last_viewed}`);
                // console.log(`🏁 Is first load: ${isFirstLoad}`);

                if (data.activities.length > 0) {
                    // On first load, don't show notifications for existing activities
                    if (isFirstLoad) {
                         console.log('� Skipping notifications on first load');
                        isFirstLoad = false;
                    } else {
                        console.log(`📢 Processing ${data.activities.length} new activities`);
                            const activityKey = `${activity.type}_${activity.student_id}_${activity.action}_${activity.timestamp}`;
                            if (shownNotifications.has(activityKey)) {
                               console.log(`🔄 Skipping duplicate notification for ${activity.student_name}`);
                                return false;
                            }
                            shownNotifications.add(activityKey);
                            return true;
                        });

                        if (newActivities.length > 0) {
                            console.log(`📢 Adding ${newActivities.length} new activities to queue`);

                            // Add new activities to queue
                            newActivities.forEach(activity => {
                                // console.log(`➕ Adding to queue:`, activity);
                                notificationQueue.push(activity);
                            });

                            // Process queue if not already processing
                            if (!isProcessingQueue) {
                                processNotificationQueue();
                            }
                        // } else {
                        //     console.log('❌ No new activities to show (all filtered out)');
                        // }
                    }
                } else {`
                    // Mark first load as complete even if no activities
                    if (isFirstLoad) {
                        isFirstLoad = false;
                        console.log('✅ First load complete, ready for new notifications');
                    }
                }

                // Update last check time to current time
                lastNotificationCheck = currentTime;

            }
        })
        .catch(error => {
            console.error('❌ Error checking for new activities:', error);
        });
}

function processNotificationQueue() {
    if (notificationQueue.length === 0) {
        isProcessingQueue = false;
        return;
    }

    isProcessingQueue = true;
    const activity = notificationQueue.shift();

    showNotificationCard(activity);

    // Process next notification after 1 second delay
    setTimeout(() => {
        processNotificationQueue();
    }, 1000);
}

function showNotificationCard(activity) {
    const container = document.getElementById('notification-container');
    if (!container) return;

    // Create notification card
    const card = document.createElement('div');
    card.className = 'notification-card transform translate-x-full opacity-0 transition-all duration-300 ease-out';

    // Determine card style based on action and late status
    let bgColor, icon, actionText, typeText, displayName, batchInfo;

    // Check if student is late (red color for late students)
    if (activity.is_late) {
        bgColor = 'bg-gradient-to-r from-red-500 to-red-600';
        icon = '⚠️';
        actionText = 'Late Time In';
        typeText = activity.type === 'academic' ? 'Academic' : (activity.type === 'visitor' ? 'Visitor' : 'Going Out');
    } else {
        // Standard colors: Blue for time in, Orange for time out
        if (activity.action === 'time_in') {
            bgColor = 'bg-gradient-to-r from-blue-500 to-blue-600';
            icon = '🔵';
            actionText = 'Time In';
        } else {
            bgColor = 'bg-gradient-to-r from-orange-500 to-orange-600';
            icon = '🟠';
            actionText = 'Time Out';
        }

        if (activity.type === 'visitor') {
            typeText = 'Visitor';
        } else {
            typeText = activity.type === 'academic' ? 'Academic' : 'Going Out';
        }
    }

    // Set display name and batch info based on activity type
    if (activity.type === 'visitor') {
        displayName = activity.visitor_name;
        batchInfo = 'Visitor Log';
    } else {
        displayName = activity.student_name;
        batchInfo = `Batch ${activity.batch} • ${typeText}`;
    }

    card.innerHTML = `
        <div class="${bgColor} text-white p-4 rounded-lg shadow-lg border border-white/20 backdrop-blur-sm">
            <div class="flex items-start justify-between">
                <div class="flex items-center space-x-3">
                    <div class="text-2xl">${icon}</div>
                    <div>
                        <div class="text-sm font-semibold">${displayName}</div>
                        <div class="text-xs opacity-90">${batchInfo}</div>
                        <div class="text-xs opacity-75">${actionText} at ${activity.time}</div>
                    </div>
                </div>
                <button onclick="removeNotificationCard(this)" class="text-lg leading-none text-white/70 hover:text-white">&times;</button>
            </div>
        </div>
    `;

    container.appendChild(card);

    // Animate in
    setTimeout(() => {
        card.classList.remove('translate-x-full', 'opacity-0');
        card.classList.add('translate-x-0', 'opacity-100');
    }, 100);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        removeNotificationCard(card);
    }, 5000);
}

function removeNotificationCard(element) {
    const card = element.closest ? element.closest('.notification-card') : element;
    if (card) {
        card.classList.add('translate-x-full', 'opacity-0');
        setTimeout(() => {
            if (card.parentNode) {
                card.parentNode.removeChild(card);
            }
        }, 300);
    }
}
</script>

            {{-- Welcome Message --}}
            <div class="mb-10 text-center">
                <p class="text-xl font-bold text-blue-800 sm:text-3xl">
                    Welcome, Educator!
                    <br class="hidden sm:block" />
                    <span class="block mt-2 text-sm font-medium text-gray-600">Ready to monitor today's student activity?</span>
                    <span class="block w-24 h-1 mx-auto mt-3 bg-blue-500 rounded-full"></span>
                </p>
            </div>

            {{-- Quick Stats Container --}}
            <div class="p-10 mb-8 bg-white shadow-md rounded-2xl">
                <div class="relative perspective-1000">
                    {{-- Flip Container --}}
                    <div id="overviewFlipContainer" class="relative w-full transition-transform duration-500 transform-style-3d">
                        {{-- Academic Overview (Front) --}}
                        <div id="academicOverview" class="w-full backface-hidden">
                            <h3 class="mb-8 text-lg font-semibold text-gray-800">
                                <span id="overviewTitle">Today's Academic Overview</span>
                                <span class="ml-2 text-sm font-medium text-gray-600">(Class & Group based Schedule)</span>
                            </h3>
                            <button onclick="flipOverview()" class="absolute text-blue-600 transition-colors top-4 right-4 hover:text-blue-800 flip-button group">
                                <i data-feather="repeat" class="w-5 h-5"></i>
                                <div class="absolute right-0 z-50 invisible px-2 py-1 text-xs text-white bg-gray-900 rounded-md shadow-sm group-hover:visible -top-8 whitespace-nowrap">
                                    Switch Overview Type
                                </div>
                            </button>
                            <div class="grid grid-cols-5 gap-3">
                                <!-- Total Students Card (spans 2 rows, keeps blue background) -->
                                <div class="row-span-2 relative p-6 bg-gradient-to-br from-blue-500 to-blue-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 200px;">
                                    <div class="flex items-center h-full">
                                        <div class="flex-shrink-0 mr-4">
                                            <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                                <i data-feather="users" class="w-8 h-8 text-white"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <p class="mb-2 font-medium text-blue-100 text-sm opacity-90">Total Students</p>
                                            <p class="text-3xl font-bold text-white" id="totalStudentsOverview">0</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Row 1: Log Out and its breakdown -->
                                <!-- Log Out Card -->
                                <div class="relative p-4 bg-gradient-to-br from-orange-500 to-orange-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                                    <div class="flex items-center h-full">
                                        <div class="flex-shrink-0 mr-3">
                                            <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                                <i data-feather="log-out" class="w-6 h-6 text-white"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <p class="mb-1 font-medium text-orange-100 text-xs">Log Out</p>
                                            <p class="text-2xl font-bold text-white" id="academicLogOutTotal">0</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Log Out Early Card -->
                                <div class="relative p-4 bg-gradient-to-br from-green-500 to-green-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                                    <div class="flex items-center h-full">
                                        <div class="flex-shrink-0 mr-3">
                                            <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                                <i data-feather="sunrise" class="w-6 h-6 text-white"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <p class="mb-1 font-medium text-green-100 text-xs">Early</p>
                                            <p class="text-2xl font-bold text-white" id="academicLogOutEarly">0</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Log Out On Time Card -->
                                <div class="relative p-4 bg-gradient-to-br from-blue-500 to-blue-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                                    <div class="flex items-center h-full">
                                        <div class="flex-shrink-0 mr-3">
                                            <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                                <i data-feather="clock" class="w-6 h-6 text-white"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <p class="mb-1 font-medium text-blue-100 text-xs">On Time</p>
                                            <p class="text-2xl font-bold text-white" id="academicLogOutOnTime">0</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Log Out Late Card -->
                                <div class="relative p-4 bg-gradient-to-br from-red-500 to-red-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                                    <div class="flex items-center h-full">
                                        <div class="flex-shrink-0 mr-3">
                                            <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                                <i data-feather="alert-circle" class="w-6 h-6 text-white"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <p class="mb-1 font-medium text-red-100 text-xs">Late</p>
                                            <p class="text-2xl font-bold text-white" id="academicLogOutLate">0</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Row 2: Log In and its breakdown -->
                                <!-- Log In Card -->
                                <div class="relative p-4 bg-gradient-to-br from-indigo-500 to-indigo-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                                    <div class="flex items-center h-full">
                                        <div class="flex-shrink-0 mr-3">
                                            <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                                <i data-feather="log-in" class="w-6 h-6 text-white"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <p class="mb-1 font-medium text-indigo-100 text-xs">Log In</p>
                                            <p class="text-2xl font-bold text-white" id="academicLogInTotal">0</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Log In Early Card -->
                                <div class="relative p-4 bg-gradient-to-br from-green-500 to-green-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                                    <div class="flex items-center h-full">
                                        <div class="flex-shrink-0 mr-3">
                                            <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                                <i data-feather="sunrise" class="w-6 h-6 text-white"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <p class="mb-1 font-medium text-green-100 text-xs">Early</p>
                                            <p class="text-2xl font-bold text-white" id="academicLogInEarly">0</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Log In On Time Card -->
                                <div class="relative p-4 bg-gradient-to-br from-blue-500 to-blue-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                                    <div class="flex items-center h-full">
                                        <div class="flex-shrink-0 mr-3">
                                            <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                                <i data-feather="clock" class="w-6 h-6 text-white"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <p class="mb-1 font-medium text-blue-100 text-xs">On Time</p>
                                            <p class="text-2xl font-bold text-white" id="academicLogInOnTime">0</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Log In Late Card -->
                                <div class="relative p-4 bg-gradient-to-br from-red-500 to-red-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                                    <div class="flex items-center h-full">
                                        <div class="flex-shrink-0 mr-3">
                                            <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                                <i data-feather="alert-circle" class="w-6 h-6 text-white"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <p class="mb-1 font-medium text-red-100 text-xs">Late</p>
                                            <p class="text-2xl font-bold text-white" id="academicLogInLate">0</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {{-- Going Out Overview (Back) --}}
                        <div id="goingOutOverview" class="absolute top-0 left-0 w-full backface-hidden rotate-y-180">
                            <h3 class="mb-8 text-lg font-semibold text-gray-800">
                                <span id="goingOutTitle">Today's Going Out Overview</span>
                                <span class="ml-2 text-sm font-medium text-gray-600">(Gender-based Schedule)</span>
                            </h3>
                            <button onclick="flipOverview()" class="absolute text-blue-600 transition-colors top-4 right-4 hover:text-blue-800 flip-button group">
                                <i data-feather="repeat" class="w-5 h-5"></i>
                                <div class="absolute right-0 z-50 invisible px-2 py-1 text-xs text-white bg-gray-900 rounded-md shadow-sm group-hover:visible -top-8 whitespace-nowrap">
                                    Switch Overview Type
                                </div>
                            </button>
                            <div class="grid grid-cols-5 gap-3">
                                <!-- Total Students Card (spans 2 rows, keeps blue background) -->
                                <div class="row-span-2 relative p-6 bg-gradient-to-br from-blue-500 to-blue-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 200px;">
                                    <div class="flex items-center h-full">
                                        <div class="flex-shrink-0 mr-4">
                                            <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                                <i data-feather="users" class="w-8 h-8 text-white"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <p class="mb-2 font-medium text-blue-100 text-sm opacity-90">Total Students</p>
                                            <p class="text-3xl font-bold text-white" id="goingOutTotalStudents">0</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Row 1: Log Out and On Time -->
                                <!-- Log Out Card -->
                                <div class="col-span-2 relative p-4 bg-gradient-to-br from-orange-500 to-orange-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                                    <div class="flex items-center h-full">
                                        <div class="flex-shrink-0 mr-3">
                                            <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                                <i data-feather="log-out" class="w-6 h-6 text-white"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <p class="mb-1 font-medium text-orange-100 text-xs">Log Out</p>
                                            <p class="text-2xl font-bold text-white" id="goingOutLogOutTotal">0</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Log Out On Time Card -->
                                <div class="col-span-2 relative p-4 bg-gradient-to-br from-green-500 to-green-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                                    <div class="flex items-center h-full">
                                        <div class="flex-shrink-0 mr-3">
                                            <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                                <i data-feather="clock" class="w-6 h-6 text-white"></i>
                                            </div>
                                        </div>
                                        <div class="flex-1">
                                            <p class="mb-1 font-medium text-green-100 text-xs">On Time</p>
                                            <p class="text-2xl font-bold text-white" id="goingOutLogOutOnTime">0</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Row 2: Log In and its breakdown (equal width cards filling remaining space) -->
                                <!-- Log In Card -->
                                <div class="col-span-4 grid grid-cols-3 gap-3">
                                    <div class="relative p-4 bg-gradient-to-br from-indigo-500 to-indigo-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                                        <div class="flex items-center h-full">
                                            <div class="flex-shrink-0 mr-3">
                                                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                                    <i data-feather="log-in" class="w-6 h-6 text-white"></i>
                                                </div>
                                            </div>
                                            <div class="flex-1">
                                                <p class="mb-1 font-medium text-indigo-100 text-xs">Log In</p>
                                                <p class="text-2xl font-bold text-white" id="goingOutLogInTotal">0</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Log In On Time Card -->
                                    <div class="relative p-4 bg-gradient-to-br from-green-500 to-green-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                                        <div class="flex items-center h-full">
                                            <div class="flex-shrink-0 mr-3">
                                                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                                    <i data-feather="clock" class="w-6 h-6 text-white"></i>
                                                </div>
                                            </div>
                                            <div class="flex-1">
                                                <p class="mb-1 font-medium text-green-100 text-xs">On Time</p>
                                                <p class="text-2xl font-bold text-white" id="goingOutLogInOnTime">0</p>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Log In Late Card -->
                                    <div class="relative p-4 bg-gradient-to-br from-red-500 to-red-600 text-white transition-all shadow-md hover:shadow-lg rounded-lg hover:scale-105" style="min-height: 95px;">
                                        <div class="flex items-center h-full">
                                            <div class="flex-shrink-0 mr-3">
                                                <div class="bg-white bg-opacity-20 rounded-lg p-2">
                                                    <i data-feather="alert-circle" class="w-6 h-6 text-white"></i>
                                                </div>
                                            </div>
                                            <div class="flex-1">
                                                <p class="mb-1 font-medium text-red-100 text-xs">Late</p>
                                                <p class="text-2xl font-bold text-white" id="goingOutLogInLate">0</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Academic Chart Filter (Only visible when on Academic side) --}}
            <div id="academicChartFilter" class="mb-6 p-4 bg-white shadow-md rounded-xl">
                <div class="flex items-center justify-between">
                    <h3 class="text-sm font-medium text-gray-600">Academic Analytics View</h3>
                    <div class="flex items-center space-x-3">
                        <label class="text-xs font-medium text-gray-700">Show:</label>
                        <select id="academicChartSelector" class="px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500">
                            <option value="timeinout">Time In & Time Out</option>
                            <option value="absent">Absent Students</option>
                        </select>
                    </div>
                </div>
            </div>

            {{-- Combined Analytics Container - Dynamic layout based on flip state --}}
            <div id="analyticsContainer" class="grid grid-cols-1 gap-6 mb-8 transition-all duration-500">
                {{-- Time In/Out Analytics Container --}}
                <div id="timeInOutContainer" class="p-6 bg-white shadow-md rounded-2xl">
                    <div class="relative perspective-1000">
                        {{-- Flip Container --}}
                        <div id="timeInOutFlipContainer" class="relative w-full transition-transform duration-500 transform-style-3d">
                            {{-- Academic Time In/Out Overview (Front) --}}
                            <div id="academicTimeInOut" class="w-full backface-hidden">

                                <div class="h-80">
                                    <canvas id="academicTimeInOutChart"></canvas>
                                </div>
                            </div>

                            {{-- Going Out Time In/Out Overview (Back) --}}
                            <div id="goingOutTimeInOut" class="absolute top-0 left-0 w-full backface-hidden rotate-y-180">
                                <div class="h-80">
                                    <canvas id="goingOutTimeInOutChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Absent Students Analytics Container (Academic Only) --}}
                <div id="absentStudentsContainer" class="p-6 bg-white shadow-md rounded-2xl" style="display: none;">
                    <h5 class="mb-4 text-sm font-medium text-gray-600 text-center">Absent Students by Batch (Monthly Overview)</h5>
                    <div class="h-80 overflow-x-auto">
                        <div style="width: 1200px; height: 100%;">
                            <canvas id="academicAbsentChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            {{-- Late Students Bar Graph Container --}}
            {{-- <div class="p-8 bg-white shadow-md rounded-2xl">
                <h3 class="mb-6 text-2xl font-semibold text-gray-800">Late Students</h3>
                <div class="h-96">
                    <canvas id="lateStudentsChart"></canvas>
                </div>
            </div> --}}
    @push('scripts')
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.3.0/dist/chart.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/patternomaly@1.3.2/dist/patternomaly.min.js"></script>
    <style>
        .perspective-1000 {
            perspective: 1000px;
        }
        .transform-style-3d {
            transform-style: preserve-3d;
        }
        .backface-hidden {
            backface-visibility: hidden;
        }
        .rotate-y-180 {
            transform: rotateY(180deg);
        }

        /* Custom scrollbar for absent chart */
        .overflow-x-auto::-webkit-scrollbar {
            height: 8px;
        }
        .overflow-x-auto::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }
        .overflow-x-auto::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }
        .overflow-x-auto::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
        .flip-button {
            transition: all 0.3s ease;
        }
        .flip-button:hover {
            transform: scale(1.05);
        }
        .flip-button:active {
            transform: scale(0.95);
        }

        /* Small light gray scrollbars for all elements */
        ::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: #d1d5db;
            border-radius: 3px;
            transition: background 0.2s ease;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }

        ::-webkit-scrollbar-corner {
            background: #f8f9fa;
        }

        /* Firefox scrollbar styling */
        * {
            scrollbar-width: thin;
            scrollbar-color: #d1d5db #f8f9fa;
        }

        /* Smooth scrolling behavior */
        html {
            scroll-behavior: smooth;
        }

        /* Real-time notification card styles */
        .notification-card {
            min-width: 300px;
            max-width: 350px;
            z-index: 1000;
        }

        .notification-card:hover {
            transform: scale(1.02) !important;
        }

        /* Notification container positioning */
        #notification-container {
            pointer-events: none;
        }

        #notification-container .notification-card {
            pointer-events: auto;
        }

        /* Animation for notification cards */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .notification-enter {
            animation: slideInRight 0.3s ease-out;
        }

        .notification-exit {
            animation: slideOutRight 0.3s ease-in;
        }
    </style>
    <script>
        let academicTimeInOutChart = null;
        let goingOutTimeInOutChart = null;
        let academicAbsentChart = null;
        let isFlipped = false;

        // Function to create pattern colors
        function createPatterns(data, color) {
            return data.map(() => pattern.draw('diagonal', color));
        }

        function createDots(data, color) {
            return data.map(() => pattern.draw('dot', color));
        }

        function flipOverview() {
            const container = document.getElementById('overviewFlipContainer');
            const timeInOutContainer = document.getElementById('timeInOutFlipContainer');
            const frontButton = document.querySelector('#academicOverview .flip-button');
            const backButton = document.querySelector('#goingOutOverview .flip-button');
            const analyticsContainer = document.getElementById('analyticsContainer');
            const timeInOutMainContainer = document.getElementById('timeInOutContainer');
            const absentContainer = document.getElementById('absentStudentsContainer');
            const academicFilter = document.getElementById('academicChartFilter');

            isFlipped = !isFlipped;
            container.style.transform = isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)';
            timeInOutContainer.style.transform = isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)';

            // Change layout and filter visibility based on flip state
            if (isFlipped) {
                // Going Out side - hide filter and make chart container full width
                academicFilter.style.display = 'none';
                analyticsContainer.className = 'grid grid-cols-1 gap-6 mb-8 transition-all duration-500';
                timeInOutMainContainer.className = 'p-6 bg-white shadow-md rounded-2xl col-span-1';
                timeInOutMainContainer.style.display = 'block';
                absentContainer.style.display = 'none';
            } else {
                // Academic side - show filter and apply current filter setting
                academicFilter.style.display = 'block';
                // Reset container classes
                timeInOutMainContainer.className = 'p-6 bg-white shadow-md rounded-2xl';
                absentContainer.className = 'p-6 bg-white shadow-md rounded-2xl';
                // Apply current filter
                handleAcademicChartFilter();
            }

            // Update button icons with tooltips
            const buttonContent = `
                <i data-feather="repeat" class="w-5 h-5"></i>
                <div class="absolute right-0 z-50 invisible px-2 py-1 text-xs text-white bg-gray-900 rounded-md shadow-sm group-hover:visible -top-8 whitespace-nowrap">
                    Switch Overview Type
                </div>
            `;

            frontButton.innerHTML = buttonContent;
            backButton.innerHTML = buttonContent;

            // Update data based on flip state
            if (isFlipped) {
                updateGoingOutOverview();
                updateGoingOutTimeInOutChart();
            } else {
                updateTodayOverview();
                updateAcademicTimeInOutChart();
            }

            // Refresh Feather icons
            feather.replace();
        }

        // Function to update Today's Overview cards (Academic)
        function updateTodayOverview() {
            // Fetch academic log in/out data
            fetch('/educator/academic-loginout-data')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error:', data.message);
                        return;
                    }

                    // Update the overview title based on whether it's academic data
                    document.getElementById('overviewTitle').textContent = "Today's Academic Overview";

                    // If it's Sunday, show a message
                    if (new Date().getDay() === 0) {
                        document.getElementById('academicOverview').querySelector('.grid').innerHTML = `
                            <div class="col-span-5 relative p-12 overflow-hidden text-center bg-gray-100 rounded-xl" style="min-height: 320px;">
                                <div class="flex flex-col items-center justify-center h-full">
                                    <i data-feather="calendar" class="w-16 h-16 mb-4 text-orange-500"></i>
                                    <p class="text-2xl font-semibold text-gray-700 mb-2">No Academic Schedule</p>
                                    <p class="text-lg font-medium text-gray-600">on Sundays</p>
                                </div>
                                <div class="absolute bottom-0 left-0 w-full h-2 bg-orange-500 rounded-b-xl"></div>
                            </div>
                        `;
                        feather.replace(); // Re-initialize feather icons
                        return;
                    }

                    // Update Log In card with breakdown
                    document.getElementById('academicLogInTotal').textContent = data.logIn?.total || 0;
                    document.getElementById('academicLogInEarly').textContent = data.logIn?.early || 0;
                    document.getElementById('academicLogInOnTime').textContent = data.logIn?.onTime || 0;
                    document.getElementById('academicLogInLate').textContent = data.logIn?.late || 0;

                    // Update Log Out card with breakdown
                    document.getElementById('academicLogOutTotal').textContent = data.logOut?.total || 0;
                    document.getElementById('academicLogOutEarly').textContent = data.logOut?.early || 0;
                    document.getElementById('academicLogOutOnTime').textContent = data.logOut?.onTime || 0;
                    document.getElementById('academicLogOutLate').textContent = data.logOut?.late || 0;
                })
                .catch(error => {
                    console.error('Error fetching academic log in/out data:', error);
                    // Show error state in the UI
                    document.getElementById('academicLogInTotal').textContent = '—';
                    document.getElementById('academicLogInEarly').textContent = '—';
                    document.getElementById('academicLogInOnTime').textContent = '—';
                    document.getElementById('academicLogInLate').textContent = '—';
                    document.getElementById('academicLogOutTotal').textContent = '—';
                    document.getElementById('academicLogOutEarly').textContent = '—';
                    document.getElementById('academicLogOutOnTime').textContent = '—';
                    document.getElementById('academicLogOutLate').textContent = '—';
                });

            // Fetch total students data
            fetch('/educator/student-data')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Student data response:', data); // Debug log
                    if (data.error) {
                        console.error('Server error:', data.message);
                        document.getElementById('totalStudentsOverview').textContent = '—';
                        return;
                    }

                    if (!Array.isArray(data) || data.length === 0) {
                        console.log('No student data available');
                        document.getElementById('totalStudentsOverview').textContent = '—';
                        return;
                    }

                    const studentCounts = data.map(item => item.total_students);
                    const totalStudents = studentCounts.reduce((sum, count) => sum + count, 0);
                    console.log('Total students calculated:', totalStudents); // Debug log
                    document.getElementById('totalStudentsOverview').textContent = totalStudents;
                })
                .catch(error => {
                    console.error('Error fetching student data:', error);
                    document.getElementById('totalStudentsOverview').textContent = '—';
                });
        }

        // Function to update Going Out Overview
        function updateGoingOutOverview() {
            // Check if it's Sunday - Going Out still operates on Sundays
            // No special Sunday handling needed for Going Out Overview

            // Fetch going out log in/out data
            fetch('/educator/goingout-loginout-data')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('Error:', data.message);
                        return;
                    }

                    // Update Log In card with breakdown (total, on time, and late)
                    document.getElementById('goingOutLogInTotal').textContent = data.logIn?.total || 0;
                    document.getElementById('goingOutLogInOnTime').textContent = data.logIn?.onTime || 0;
                    document.getElementById('goingOutLogInLate').textContent = data.logIn?.late || 0;

                    // Update Log Out card (total and on time)
                    document.getElementById('goingOutLogOutTotal').textContent = data.logOut?.total || 0;
                    document.getElementById('goingOutLogOutOnTime').textContent = data.logOut?.onTime || 0;
                })
                .catch(error => {
                    console.error('Error fetching going out log in/out data:', error);
                    // Show error state in the UI (for existing cards)
                    document.getElementById('goingOutLogInTotal').textContent = '—';
                    document.getElementById('goingOutLogInOnTime').textContent = '—';
                    document.getElementById('goingOutLogInLate').textContent = '—';
                    document.getElementById('goingOutLogOutTotal').textContent = '—';
                    document.getElementById('goingOutLogOutOnTime').textContent = '—';
                });

            // Fetch total students data
            fetch('/educator/student-data')
                .then(response => response.json())
                .then(data => {
                    console.log('Student data response:', data); // Debug log
                    if (!Array.isArray(data) || data.length === 0) {
                        document.getElementById('goingOutTotalStudents').textContent = '—';
                        return;
                    }
                    const studentCounts = data.map(item => item.total_students);
                    const totalStudents = studentCounts.reduce((sum, count) => sum + count, 0);
                    document.getElementById('goingOutTotalStudents').textContent = totalStudents;
                })
                .catch(error => {
                    console.error('Error:', error);
                    document.getElementById('goingOutTotalStudents').textContent = '—';
                });
        }

        // Function to update Academic Time In/Out Chart
        function updateAcademicTimeInOutChart() {
            const chartContainer = document.getElementById('academicTimeInOut').querySelector('.h-80');
            const canvas = document.getElementById('academicTimeInOutChart');

            fetch('/educator/time-inout-by-batch?type=academic')
                .then(response => response.json())
                .then(timeData => {
                    if (timeData.error) {
                        throw new Error(timeData.message || 'Error fetching data');
                    }

                    if (academicTimeInOutChart instanceof Chart) {
                        academicTimeInOutChart.destroy();
                    }

                    if (!timeData || timeData.length === 0) {
                        chartContainer.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">No academic time in/out data available for today</div>';
                        return;
                    }

                    const ctx = canvas.getContext('2d');
                    academicTimeInOutChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: timeData.map(item => `Class ${item.batch}`),
                            datasets: [
                                {
                                    label: 'Log Out Time',
                                    data: timeData.map(item => item.time_out_count),
                                    backgroundColor: ' rgb(255, 255, 255)', // Solid Orange
                                    borderColor: 'rgb(249, 115, 22)',
                                    borderWidth: 2,
                                    borderRadius: 3,
                                    barThickness: 35
                                },
                                {
                                    label: 'Log In Time',
                                    data: timeData.map(item => item.time_in_count),
                                    backgroundColor:  'rgb(255, 255, 255)', // Striped Blue
                                    borderColor: 'rgb(59, 130, 246)',
                                    borderWidth: 2,
                                    borderRadius: 3,
                                    barThickness: 35
                                },
                                {
                                    label: 'Late Students',
                                    data: timeData.map(item => item.late_count),
                                    backgroundColor: 'rgb(255, 255, 255)', // Dotted Red
                                    borderColor: 'rgb(239, 68, 68)',
                                    borderWidth: 2,
                                    borderRadius: 3,
                                    barThickness: 35
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            interaction: {
                                mode: 'index',
                                intersect: false
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        stepSize: 1,
                                        font: {
                                            size: 10,
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#6B7280'
                                    },
                                    grid: {
                                        color: 'rgba(0, 0, 0, 0.05)',
                                        drawBorder: false
                                    },
                                    title: {
                                        display: true,
                                        text: 'Number of Students',
                                        font: {
                                            size: 10,
                                            weight: '600',
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#374151',
                                        padding: { top: 10, bottom: 10 }
                                    }
                                },
                                x: {
                                    ticks: {
                                        font: {
                                            size: 10,
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#6B7280'
                                    },
                                    grid: {
                                        display: false
                                    },
                                    title: {
                                        display: true,
                                        text: 'Class',
                                        font: {
                                            size: 10,
                                            weight: '600',
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#374151',
                                        padding: { top: 10, bottom: 10 }
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    position: 'top',
                                    align: 'end',
                                    labels: {
                                        font: {
                                            size: 12,
                                            weight: '500',
                                            family: "'Inter', sans-serif"
                                        },
                                        padding: 20,
                                        usePointStyle: true,
                                        pointStyle: 'circle',
                                        color: '#374151'
                                    }
                                },
                                title: {
                                    display: true,
                                    text: 'Academic Log In/Out Time and Late Students Distribution by Batch',
                                    font: {
                                        size: 12,
                                        weight: '600',
                                        family: "'Inter', sans-serif"
                                    },
                                    color: '#111827',
                                    padding: {
                                        top: 8,
                                        bottom: 12
                                    }
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(17, 24, 39, 0.9)',
                                    titleFont: {
                                        size: 13,
                                        weight: '600',
                                        family: "'Inter', sans-serif"
                                    },
                                    bodyFont: {
                                        size: 12,
                                        family: "'Inter', sans-serif"
                                    },
                                    padding: 12,
                                    cornerRadius: 8,
                                    callbacks: {
                                        label: function(context) {
                                            return `${context.dataset.label}: ${context.raw} students`;
                                        }
                                    }
                                }
                            },
                            animation: {
                                duration: 750,
                                easing: 'easeInOutQuart'
                            }
                        }
                    });
                })
                .catch(error => {
                    console.error('Error:', error);
                    chartContainer.innerHTML = `
                        <div class="flex flex-col items-center justify-center h-full">
                            <p class="mb-2 font-semibold text-red-500">Error loading academic time in/out data</p>
                            <p class="text-sm text-gray-500">${error.message}</p>
                        </div>
                    `;
                });
        }

        // Function to update Going Out Time In/Out Chart
        function updateGoingOutTimeInOutChart() {
            const chartContainer = document.getElementById('goingOutTimeInOut').querySelector('.h-80');
            const canvas = document.getElementById('goingOutTimeInOutChart');

            fetch('/educator/time-inout-by-batch?type=going_out')
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        throw new Error(data.message || 'Error fetching data');
                    }

                    console.log('Raw time data received:', data); // Debug log

                    if (goingOutTimeInOutChart instanceof Chart) {
                        goingOutTimeInOutChart.destroy();
                    }

                    if (!data || Object.keys(data).length === 0) {
                        chartContainer.innerHTML = '<div class="flex items-center justify-center h-full text-gray-500">No going out time in/out data available for today</div>';
                        return;
                    }

                    const ctx = canvas.getContext('2d');
                    goingOutTimeInOutChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['Male', 'Female'],
                            datasets: [
                                {
                                    label: 'Log Out Time',
                                    data: [data.Male.time_out_count, data.Female.time_out_count],
                                    backgroundColor: 'rgb(255, 255, 255)', // Solid Orange
                                    borderColor: 'rgb(249, 115, 22)',
                                    borderWidth: 2,
                                    borderRadius: 3,
                                    barThickness: 35
                                },
                                {
                                    label: 'Log In Time',
                                    data: [data.Male.time_in_count, data.Female.time_in_count],
                                    backgroundColor: 'rgb(255, 255, 255)', // Striped Blue
                                    borderColor: 'rgb(59, 130, 246)',
                                    borderWidth: 2,
                                    borderRadius: 3,
                                    barThickness: 35
                                },
                                {
                                    label: 'Late Students',
                                    data: [data.Male.late_count, data.Female.late_count],
                                    backgroundColor:  'rgb(255, 255, 255)', // Dotted Red
                                    borderColor: 'rgb(239, 68, 68)',
                                    borderWidth: 2,
                                    borderRadius: 3,
                                    barThickness: 35
                                }
                            ]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            interaction: {
                                mode: 'nearest',
                                intersect: true
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        stepSize: 1,
                                        font: {
                                            size: 12,
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#6B7280'
                                    },
                                    grid: {
                                        color: 'rgba(0, 0, 0, 0.05)',
                                        drawBorder: false
                                    },
                                    title: {
                                        display: true,
                                        text: 'Number of Students',
                                        font: {
                                            size: 12,
                                            weight: '600',
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#374151',
                                        padding: { top: 10, bottom: 10 }
                                    }
                                },
                                x: {
                                    ticks: {
                                        font: {
                                            size: 12,
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#6B7280'
                                    },
                                    grid: {
                                        display: false
                                    },
                                    title: {
                                        display: true,
                                        text: 'Gender',
                                        font: {
                                            size: 11,
                                            weight: '500',
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#374151',
                                        padding: { top: 15, bottom: 5 }
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    position: 'top',
                                    align: 'end',
                                    labels: {
                                        font: {
                                            size: 12,
                                            weight: '500',
                                            family: "'Inter', sans-serif"
                                        },
                                        padding: 20,
                                        usePointStyle: true,
                                        pointStyle: 'circle',
                                        color: '#374151'
                                    }
                                },
                                title: {
                                    display: true,
                                    text: 'Going Out Check In/Out Time and Late Students Distribution by Gender',
                                    font: {
                                        size: 12,
                                        weight: '600',
                                        family: "'Inter', sans-serif"
                                    },
                                    color: '#111827',
                                    padding: {
                                        top: 8,
                                        bottom: 12
                                    }
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(17, 24, 39, 0.95)',
                                    titleFont: {
                                        size: 14,
                                        weight: '700',
                                        family: "'Inter', sans-serif"
                                    },
                                    bodyFont: {
                                        size: 13,
                                        family: "'Inter', sans-serif"
                                    },
                                    padding: 14,
                                    cornerRadius: 8,
                                    callbacks: {
                                        title: function(context) {
                                            // Only show the gender (e.g., 'Male' or 'Female')
                                            return context[0].label;
                                        },
                                        label: function(context) {
                                            const gender = context.label;
                                            const datasetLabel = context.dataset.label;
                                            const value = context.raw;
                                            const genderData = data[gender];
                                            let emoji = '';
                                            let labelText = '';
                                            if (datasetLabel === 'Time Out') { emoji = '🟧'; labelText = 'Check-out time'; }
                                            else if (datasetLabel === 'Time In') { emoji = '🔵'; labelText = 'Check-in time'; }
                                            else if (datasetLabel === 'Late Students') { emoji = '🔴'; labelText = 'Late Students'; }
                                            let lines = [`Total: ${value} students`, 'Batch Breakdown:'];
                                            const sortedBatches = genderData.batches.sort((a, b) => a.batch - b.batch);
                                            sortedBatches.forEach(batch => {
                                                let batchValue = 0;
                                                if (datasetLabel === 'Time Out') batchValue = batch.time_out_count;
                                                else if (datasetLabel === 'Time In') batchValue = batch.time_in_count;
                                                else if (datasetLabel === 'Late Students') batchValue = batch.late_count;
                                                if (batchValue > 0) {
                                                    lines.push(`${emoji} Batch ${batch.batch} ${labelText}: ${batchValue} students`);
                                                }
                                            });
                                            return lines;
                                        }
                                    }
                                }
                            },
                            animation: {
                                duration: 750,
                                easing: 'easeInOutQuart'
                            }
                        }
                    });
                })
                .catch(error => {
                    console.error('Error:', error);
                    chartContainer.innerHTML = `
                        <div class="flex flex-col items-center justify-center h-full">
                            <p class="mb-2 font-semibold text-red-500">Error loading going out time in/out data</p>
                            <p class="text-sm text-gray-500">${error.message}</p>
                        </div>
                    `;
                });
        }

        // Function to update Academic Absent Students Chart
        function updateAcademicAbsentChart() {
            const chartContainer = document.getElementById('academicAbsentChart').parentElement;
            const canvas = document.getElementById('academicAbsentChart');

            fetch('/educator/absent-students-by-batch')
                .then(response => response.json())
                .then(monthlyData => {
                    if (monthlyData.error) {
                        throw new Error(monthlyData.message || 'Error fetching data');
                    }

                    if (academicAbsentChart instanceof Chart) {
                        academicAbsentChart.destroy();
                    }

                    if (!monthlyData || monthlyData.length === 0) {
                        chartContainer.innerHTML = `
                            <h5 class="mb-4 text-sm font-medium text-gray-600 text-center">Absent Students by Batch (Monthly Overview)</h5>
                            <div class="h-80 overflow-x-auto">
                                <div style="width: 1200px; height: 100%; display: flex; align-items: center; justify-content: center;">
                                    <div class="text-center text-gray-500">
                                        <i data-feather="calendar" class="w-10 h-10 mb-3 text-orange-500 mx-auto"></i>
                                        <p class="text-sm">No data available for this year</p>
                                    </div>
                                </div>
                            </div>
                        `;
                        feather.replace();
                        return;
                    }

                    // Restore the canvas if it was replaced
                    if (!document.getElementById('academicAbsentChart')) {
                        chartContainer.innerHTML = `
                            <h5 class="mb-4 text-sm font-medium text-gray-600 text-center">Absent Students by Batch (Monthly Overview)</h5>
                            <div class="h-80 overflow-x-auto">
                                <div style="width: 1200px; height: 100%;">
                                    <canvas id="academicAbsentChart"></canvas>
                                </div>
                            </div>
                        `;
                    }

                    // Prepare data for the chart
                    const monthLabels = monthlyData.map(month => month.month_name);

                    // Get all unique batches
                    const allBatches = [...new Set(monthlyData.flatMap(month =>
                        month.batches.map(batch => batch.batch)
                    ))].sort();

                    // Create datasets for each batch
                    const datasets = allBatches.map((batch, index) => {
                        const colors = [
                            'rgba(220, 38, 38, 0.8)',   // Red
                            'rgba(249, 115, 22, 0.8)',  // Orange
                            'rgba(59, 130, 246, 0.8)',  // Blue
                            'rgba(16, 185, 129, 0.8)',  // Green
                            'rgba(139, 92, 246, 0.8)',  // Purple
                            'rgba(236, 72, 153, 0.8)',  // Pink
                            'rgba(245, 158, 11, 0.8)',  // Yellow
                            'rgba(107, 114, 128, 0.8)'  // Gray
                        ];

                        const borderColors = [
                            'rgb(220, 38, 38)',   // Red
                            'rgb(249, 115, 22)',  // Orange
                            'rgb(59, 130, 246)',  // Blue
                            'rgb(16, 185, 129)',  // Green
                            'rgb(139, 92, 246)',  // Purple
                            'rgb(236, 72, 153)',  // Pink
                            'rgb(245, 158, 11)',  // Yellow
                            'rgb(107, 114, 128)'  // Gray
                        ];

                        const data = monthlyData.map(month => {
                            const batchData = month.batches.find(b => b.batch === batch);
                            return batchData ? batchData.absent_count : 0;
                        });

                        return {
                            label: `Class ${batch}`,
                            data: data,
                            backgroundColor: colors[index % colors.length],
                            borderColor: borderColors[index % borderColors.length],
                            borderWidth: 1,
                            borderRadius: 3,
                            barThickness: 25
                        };
                    });

                    const ctx = document.getElementById('academicAbsentChart').getContext('2d');
                    academicAbsentChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: monthLabels,
                            datasets: datasets
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            layout: {
                                padding: {
                                    right: 20
                                }
                            },
                            interaction: {
                                mode: 'index',
                                intersect: false
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        stepSize: 1,
                                        font: {
                                            size: 12,
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#6B7280'
                                    },
                                    grid: {
                                        color: 'rgba(0, 0, 0, 0.05)',
                                        drawBorder: false
                                    },
                                    title: {
                                        display: true,
                                        text: 'Number of Absent Students',
                                        font: {
                                            size: 12,
                                            weight: '600',
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#374151',
                                        padding: { top: 10, bottom: 10 }
                                    }
                                },
                                x: {
                                    ticks: {
                                        font: {
                                            size: 12,
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#6B7280'
                                    },
                                    grid: {
                                        display: false
                                    },
                                    title: {
                                        display: true,
                                        text: 'Month',
                                        font: {
                                            size: 12,
                                            weight: '600',
                                            family: "'Inter', sans-serif"
                                        },
                                        color: '#374151',
                                        padding: { top: 10, bottom: 10 }
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    position: 'top',
                                    align: 'end',
                                    labels: {
                                        font: {
                                            size: 12,
                                            weight: '500',
                                            family: "'Inter', sans-serif"
                                        },
                                        padding: 20,
                                        usePointStyle: true,
                                        pointStyle: 'circle',
                                        color: '#374151'
                                    }
                                },
                                title: {
                                    display: false // Title is handled by the h3 element
                                },
                                tooltip: {
                                    backgroundColor: 'rgba(17, 24, 39, 0.9)',
                                    titleFont: {
                                        size: 13,
                                        weight: '600',
                                        family: "'Inter', sans-serif"
                                    },
                                    bodyFont: {
                                        size: 12,
                                        family: "'Inter', sans-serif"
                                    },
                                    padding: 12,
                                    cornerRadius: 8,
                                    callbacks: {
                                        title: function(context) {
                                            return context[0].label; // Month name
                                        },
                                        label: function(context) {
                                            const count = context.raw;
                                            if (count === 0) {
                                                return `${context.dataset.label}: No absent students`;
                                            }
                                            return `${context.dataset.label}: ${count} absent student${count > 1 ? 's' : ''}`;
                                        }
                                    }
                                }
                            },
                            animation: {
                                duration: 750,
                                easing: 'easeInOutQuart'
                            }
                        }
                    });
                })
                .catch(error => {
                    console.error('Error:', error);
                    chartContainer.innerHTML = `
                        <h3 class="mb-4 text-base font-semibold text-gray-800">Absent Students by Batch (Monthly Overview)</h3>
                        <div class="h-80 overflow-x-auto">
                            <div style="width: 800px; height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center;">
                                <p class="mb-2 text-sm font-semibold text-red-500">Error loading absent students data</p>
                                <p class="text-xs text-gray-500">${error.message}</p>
                            </div>
                        </div>
                    `;
                });
        }

        // Function to update all data
        async function refreshAllData() {
            try {
                if (isFlipped) {
                    await Promise.all([
                        updateGoingOutOverview(),
                        updateGoingOutTimeInOutChart()
                    ]);
                } else {
                    await Promise.all([
                        updateTodayOverview(),
                        updateAcademicTimeInOutChart()
                    ]);
                }
                // Always update absent chart since it's separate from flip
                await updateAcademicAbsentChart();
            } catch (error) {
                console.error('Error refreshing data:', error);
            }
        }

        // Function to handle academic chart filter
        function handleAcademicChartFilter() {
            const selector = document.getElementById('academicChartSelector');
            const analyticsContainer = document.getElementById('analyticsContainer');
            const timeInOutContainer = document.getElementById('timeInOutContainer');
            const absentContainer = document.getElementById('absentStudentsContainer');

            const selectedValue = selector.value;

            switch(selectedValue) {
                case 'timeinout':
                    // Show Time In/Out chart in full width (default)
                    analyticsContainer.className = 'grid grid-cols-1 gap-6 mb-8 transition-all duration-500';
                    timeInOutContainer.style.display = 'block';
                    timeInOutContainer.className = 'p-6 bg-white shadow-md rounded-2xl col-span-1';
                    absentContainer.style.display = 'none';
                    break;

                case 'absent':
                    // Show Absent chart in full width
                    analyticsContainer.className = 'grid grid-cols-1 gap-6 mb-8 transition-all duration-500';
                    timeInOutContainer.style.display = 'none';
                    absentContainer.style.display = 'block';
                    absentContainer.className = 'p-6 bg-white shadow-md rounded-2xl col-span-1';
                    break;

                default:
                    // Default to Time In/Out chart
                    analyticsContainer.className = 'grid grid-cols-1 gap-6 mb-8 transition-all duration-500';
                    timeInOutContainer.style.display = 'block';
                    timeInOutContainer.className = 'p-6 bg-white shadow-md rounded-2xl col-span-1';
                    absentContainer.style.display = 'none';
                    break;
            }
        }

        // Initialize the dashboard
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Feather icons
            feather.replace();

            // Set up academic chart filter
            const academicChartSelector = document.getElementById('academicChartSelector');
            academicChartSelector.addEventListener('change', handleAcademicChartFilter);

            // Apply initial filter state (default to Time In/Out)
            handleAcademicChartFilter();

            // Initial data load
            refreshAllData();

            // Set up periodic refresh (every 15 seconds)
            setInterval(refreshAllData, 15000);
        });
    </script>
    @endpush



</x-educatorLayout>
