<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use App\Models\Academic;
use App\Models\StudentDetail;
use App\Models\Going_out;
use App\Models\Visitor;
use App\Models\Schedule;

class MonitorController extends Controller
{
    public function getTodayAttendance(Request $request)
    {
        try {
            $today = now()->format('Y-m-d');
            $type = $request->query('type', 'academic'); // Default to academic if no type specified

            if ($type === 'going_out') {
                // Get Going Out attendance data
                $present = Going_out::whereDate('going_out_date', $today)
                    ->whereNotNull('time_out')
                    ->count();

                $onTime = Going_out::whereDate('going_out_date', $today)
                    ->whereNotNull('time_in')
                    ->where('time_in_remark', 'On Time')
                    ->count();

                $late = Going_out::whereDate('going_out_date', $today)
                    ->whereNotNull('time_in')
                    ->where('time_in_remark', 'Late')
                    ->count();
            } else {
                // Get Academic attendance data
                $present = Academic::whereDate('academic_date', $today)
                    ->whereNotNull('time_out')
                    ->count();

                $onTime = Academic::whereDate('academic_date', $today)
                    ->whereNotNull('time_in')
                    ->where('time_in_remark', 'On Time')
                    ->count();

                $late = Academic::whereDate('academic_date', $today)
                    ->whereNotNull('time_in')
                    ->where('time_in_remark', 'Late')
                    ->count();
            }

            Log::info('Monitor fetched today\'s attendance summary.', [
                'date' => $today,
                'type' => $type,
                'present' => $present,
                'onTime' => $onTime,
                'late' => $late,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'present' => $present,
                'onTime' => $onTime,
                'late' => $late
            ]);
        } catch (\Exception $e) {
            Log::error('Monitor error fetching attendance data:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'error' => 'Failed to fetch attendance data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getStudentData()
    {
        try {
            $studentData = DB::table('student_details')
                ->join('pnph_users', 'student_details.user_id', '=', 'pnph_users.user_id')
                ->select('student_details.batch', DB::raw('COUNT(*) as total_students'))
                ->where('pnph_users.user_role', 'student')
                ->where('pnph_users.status', 'active')
                ->groupBy('student_details.batch')
                ->orderBy('student_details.batch')
                ->get();

            Log::info('Monitor fetched student data by batch.', [
                'user_id' => auth()->id(),
                'data' => $studentData,
            ]);

            return response()->json($studentData);
        } catch (\Exception $e) {
            Log::error('Monitor error fetching student data:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'error' => 'Failed to fetch student data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getLateStudentsByBatch()
    {
        try {
            $today = now()->format('Y-m-d');
            $isSunday = now()->isSunday();

            if ($isSunday) {
                // Get late students by gender for Sunday
                $maleLate = Going_out::whereDate('going_out_date', $today)
                    ->whereNotNull('time_in')
                    ->where('time_in_remark', 'Late')
                    ->whereHas('studentDetail.user', function($query) {
                        $query->where('gender', 'M');
                    })
                    ->count();

                $femaleLate = Going_out::whereDate('going_out_date', $today)
                    ->whereNotNull('time_in')
                    ->where('time_in_remark', 'Late')
                    ->whereHas('studentDetail.user', function($query) {
                        $query->where('gender', 'F');
                    })
                    ->count();

                return response()->json([
                    'male_late' => $maleLate,
                    'female_late' => $femaleLate
                ]);
            } else {
                // Get late students by batch for weekdays
                $lateStudentsByBatch = DB::table('academics as a')
                    ->join('student_details as s', 'a.student_id', '=', 's.student_id')
                    ->select('s.batch', DB::raw('COUNT(*) as late_count'))
                    ->whereDate('a.academic_date', $today)
                    ->whereNotNull('a.time_in')
                    ->where('a.time_in_remark', 'Late')
                    ->groupBy('s.batch')
                    ->orderBy('s.batch')
                    ->get();

                if ($lateStudentsByBatch->isEmpty()) {
                    return response()->json([]);
                }

                return response()->json($lateStudentsByBatch);
            }
        } catch (\Exception $e) {
            Log::error('Monitor error fetching late students data:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'error' => 'Failed to fetch late students data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getGoingOutAttendance()
    {
        try {
            $today = now()->format('Y-m-d');

            // Get Going Out attendance data
            $present = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_out')
                ->count();

            $onTime = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'On Time')
                ->count();

            $late = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'Late')
                ->count();

            Log::info('Monitor fetched going out attendance summary.', [
                'date' => $today,
                'present' => $present,
                'onTime' => $onTime,
                'late' => $late,
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'present' => $present,
                'onTime' => $onTime,
                'late' => $late
            ]);
        } catch (\Exception $e) {
            Log::error('Monitor error fetching going out attendance data:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'error' => 'Failed to fetch going out attendance data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getAcademicLogInOutData()
    {
        try {
            $today = now()->format('Y-m-d');
            $isSunday = now()->isSunday();

            // If it's Sunday, return empty data since there's no academic schedule
            if ($isSunday) {
                return response()->json([
                    'logIn' => [
                        'total' => 0,
                        'onTime' => 0,
                        'late' => 0,
                        'early' => 0
                    ],
                    'logOut' => [
                        'total' => 0,
                        'onTime' => 0,
                        'late' => 0,
                        'early' => 0
                    ]
                ]);
            }

            // Get Academic log in data
            $logInTotal = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_in')
                ->count();

            $logInOnTime = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'On Time')
                ->count();

            $logInLate = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'Late')
                ->count();

            $logInEarly = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'Early')
                ->count();

            // Get Academic log out data
            $logOutTotal = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_out')
                ->count();

            $logOutOnTime = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_out')
                ->where('time_out_remark', 'On Time')
                ->count();

            $logOutLate = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_out')
                ->where('time_out_remark', 'Late')
                ->count();

            $logOutEarly = Academic::whereDate('academic_date', $today)
                ->whereNotNull('time_out')
                ->where('time_out_remark', 'Early')
                ->count();

            return response()->json([
                'logIn' => [
                    'total' => $logInTotal,
                    'onTime' => $logInOnTime,
                    'late' => $logInLate,
                    'early' => $logInEarly
                ],
                'logOut' => [
                    'total' => $logOutTotal,
                    'onTime' => $logOutOnTime,
                    'late' => $logOutLate,
                    'early' => $logOutEarly
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Monitor error fetching academic log in/out data:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to fetch academic log in/out data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getGoingOutLogInOutData()
    {
        try {
            $today = now()->format('Y-m-d');

            // Get Going Out log in data
            $logInTotal = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_in')
                ->count();

            $logInOnTime = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'On Time')
                ->count();

            $logInLate = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'Late')
                ->count();

            $logInEarly = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_in')
                ->where('time_in_remark', 'Early')
                ->count();

            // Get Going Out log out data
            $logOutTotal = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_out')
                ->count();

            $logOutOnTime = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_out')
                ->where('time_out_remark', 'On Time')
                ->count();

            $logOutLate = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_out')
                ->where('time_out_remark', 'Late')
                ->count();

            $logOutEarly = Going_out::whereDate('going_out_date', $today)
                ->whereNotNull('time_out')
                ->where('time_out_remark', 'Early')
                ->count();

            return response()->json([
                'logIn' => [
                    'total' => $logInTotal,
                    'onTime' => $logInOnTime,
                    'late' => $logInLate,
                    'early' => $logInEarly
                ],
                'logOut' => [
                    'total' => $logOutTotal,
                    'onTime' => $logOutOnTime,
                    'late' => $logOutLate,
                    'early' => $logOutEarly
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Monitor error fetching going out log in/out data:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to fetch going out log in/out data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getAbsentStudentsByBatch()
    {
        try {
            $currentYear = now()->year;
            $currentMonth = now()->month;

            // Get all distinct batches
            $allBatches = DB::table('student_details')
                ->distinct()
                ->pluck('batch')
                ->sort()
                ->values();

            // Get absent students data for all months of the current year (excluding valid absences)
            $absentData = DB::table('academics as a')
                ->join('student_details as s', 'a.student_id', '=', 's.student_id')
                ->select(
                    DB::raw('MONTH(a.academic_date) as month'),
                    's.batch',
                    DB::raw('COUNT(CASE
                        WHEN (a.educator_consideration = "Absent" AND (a.time_in_absent_validation IS NULL OR a.time_in_absent_validation = "not_valid"))
                        OR (a.time_out_consideration = "Absent" AND (a.time_out_absent_validation IS NULL OR a.time_out_absent_validation = "not_valid"))
                        THEN 1 END) as absent_count')
                )
                ->whereYear('a.academic_date', $currentYear)
                ->where(function($query) {
                    $query->where(function($subQuery) {
                        // Time in absent that is not valid
                        $subQuery->where('a.educator_consideration', 'Absent')
                                ->where(function($validationQuery) {
                                    $validationQuery->whereNull('a.time_in_absent_validation')
                                                  ->orWhere('a.time_in_absent_validation', 'not_valid');
                                });
                    })->orWhere(function($subQuery) {
                        // Time out absent that is not valid
                        $subQuery->where('a.time_out_consideration', 'Absent')
                                ->where(function($validationQuery) {
                                    $validationQuery->whereNull('a.time_out_absent_validation')
                                                  ->orWhere('a.time_out_absent_validation', 'not_valid');
                                });
                    });
                })
                ->groupBy(DB::raw('MONTH(a.academic_date)'), 's.batch')
                ->having('absent_count', '>', 0)
                ->orderBy(DB::raw('MONTH(a.academic_date)'))
                ->orderBy('s.batch')
                ->get();

            // Create a complete dataset with all months and all batches
            $monthNames = [
                1 => 'January', 2 => 'February', 3 => 'March', 4 => 'April',
                5 => 'May', 6 => 'June', 7 => 'July', 8 => 'August',
                9 => 'September', 10 => 'October', 11 => 'November', 12 => 'December'
            ];

            $completeData = [];

            // Initialize all 12 months
            for ($month = 1; $month <= 12; $month++) {
                $monthData = [
                    'month' => $month,
                    'month_name' => $monthNames[$month],
                    'batches' => []
                ];

                // Initialize all batches for this month
                foreach ($allBatches as $batch) {
                    $existingData = $absentData->where('month', $month)->where('batch', $batch)->first();
                    $monthData['batches'][] = [
                        'batch' => $batch,
                        'absent_count' => $existingData ? $existingData->absent_count : 0
                    ];
                }

                $completeData[] = $monthData;
            }

            Log::info('Monitor fetched absent students by batch (monthly).', [
                'year' => $currentYear,
                'months_included' => 12, // All 12 months
                'current_month' => $currentMonth,
                'batches_count' => count($allBatches),
                'user_id' => auth()->user()->user_id ?? null,
            ]);

            return response()->json($completeData);
        } catch (\Exception $e) {
            Log::error('Monitor error fetching absent students by batch:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'error' => 'Failed to fetch absent students data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getTimeInOutByBatch(Request $request)
    {
        try {
            $today = now()->format('Y-m-d');
            $type = $request->query('type', 'academic');

            if ($type === 'going_out') {
                // Get going out time in/out data by gender
                $timeData = DB::table('going_outs as g')
                    ->join('student_details as s', 'g.student_id', '=', 's.student_id')
                    ->join('pnph_users as u', 's.user_id', '=', 'u.user_id')
                    ->select(
                        'u.gender',
                        DB::raw('COUNT(CASE WHEN g.time_out IS NOT NULL THEN 1 END) as time_out_count'),
                        DB::raw('COUNT(CASE WHEN g.time_in IS NOT NULL THEN 1 END) as time_in_count'),
                        DB::raw('COUNT(CASE WHEN g.time_in_remark = "Late" THEN 1 END) as late_count')
                    )
                    ->whereDate('g.going_out_date', $today)
                    ->groupBy('u.gender')
                    ->get();

                // Format data for chart
                $formattedData = [
                    'Male' => ['time_out_count' => 0, 'time_in_count' => 0, 'late_count' => 0],
                    'Female' => ['time_out_count' => 0, 'time_in_count' => 0, 'late_count' => 0]
                ];

                foreach ($timeData as $data) {
                    $gender = $data->gender === 'M' ? 'Male' : 'Female';
                    $formattedData[$gender] = [
                        'time_out_count' => $data->time_out_count,
                        'time_in_count' => $data->time_in_count,
                        'late_count' => $data->late_count
                    ];
                }

                return response()->json($formattedData);
            } else {
                // Get academic time in/out data by batch
                $timeData = DB::table('academics as a')
                    ->join('student_details as s', 'a.student_id', '=', 's.student_id')
                    ->select(
                        's.batch',
                        DB::raw('COUNT(CASE WHEN a.time_out IS NOT NULL THEN 1 END) as time_out_count'),
                        DB::raw('COUNT(CASE WHEN a.time_in IS NOT NULL THEN 1 END) as time_in_count'),
                        DB::raw('COUNT(CASE WHEN a.time_in_remark = "Late" THEN 1 END) as late_count')
                    )
                    ->whereDate('a.academic_date', $today)
                    ->groupBy('s.batch')
                    ->orderBy('s.batch')
                    ->get();

                return response()->json($timeData);
            }
        } catch (\Exception $e) {
            Log::error('Monitor error fetching time in/out data:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
            ]);

            return response()->json([
                'error' => 'Failed to fetch time in/out data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function academicLogs(Request $request)
    {
        try {
            $query = Academic::with(['studentDetail.user']);

            // Apply date filter - use provided date or default to today
            if ($request->filled('date')) {
                $query->whereDate('academic_date', $request->date);
            } else {
                $query->whereDate('academic_date', now()->format('Y-m-d'));
            }

            // Apply filters if provided
            if ($request->filled('batch')) {
                $query->whereHas('studentDetail', function($q) use ($request) {
                    $q->where('batch', $request->batch);
                });
            }

            if ($request->filled('group')) {
                $query->whereHas('studentDetail', function($q) use ($request) {
                    $q->where('group', $request->group);
                });
            }

            // Order by latest activity (most recent time_out or time_in operations first)
            $logs = $query->get()
                ->sortByDesc(function ($log) {
                    // Get the latest timestamp between time_out and time_in
                    $timeOutTimestamp = $log->time_out ? strtotime($log->academic_date . ' ' . $log->time_out) : 0;
                    $timeInTimestamp = $log->time_in ? strtotime($log->academic_date . ' ' . $log->time_in) : 0;
                    return max($timeOutTimestamp, $timeInTimestamp);
                })
                ->values();

            // Get available batches and groups for filters
            $batches = StudentDetail::distinct()->pluck('batch')->sort();
            $groups = StudentDetail::distinct()->pluck('group')->sort();

            // Get the selected date for the view
            $selectedDate = $request->date ?? now()->format('Y-m-d');

            return view('user-monitor.academic-logs', compact('logs', 'batches', 'groups', 'selectedDate'));
        } catch (\Exception $e) {
            Log::error('Monitor error loading academic logs:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
            ]);

            return back()->with('error', 'Failed to load academic logs: ' . $e->getMessage());
        }
    }

    public function goingoutLogs(Request $request)
    {
        try {
            $query = Going_out::with(['studentDetail.user']);

            // Apply date filter - use provided date or default to today
            if ($request->filled('date')) {
                $query->whereDate('going_out_date', $request->date);
            } else {
                $query->whereDate('going_out_date', now()->format('Y-m-d'));
            }

            // Apply filters if provided
            if ($request->filled('batch')) {
                $query->whereHas('studentDetail', function($q) use ($request) {
                    $q->where('batch', $request->batch);
                });
            }

            if ($request->filled('group')) {
                $query->whereHas('studentDetail', function($q) use ($request) {
                    $q->where('group', $request->group);
                });
            }

            // Order by latest activity (most recent time_out or time_in operations first)
           // Also order by session number to show multiple sessions properly
            $logs = $query->orderBy('session_number', 'desc')
                ->get()
                ->sortByDesc(function ($log) {
                    // Get the latest timestamp between time_out and time_in
                    $timeOutTimestamp = $log->time_out ? strtotime($log->going_out_date . ' ' . $log->time_out) : 0;
                    $timeInTimestamp = $log->time_in ? strtotime($log->going_out_date . ' ' . $log->time_in) : 0;
                    return max($timeOutTimestamp, $timeInTimestamp);
                })
                ->values();

            // Get available batches and groups for filters
            $batches = StudentDetail::distinct()->pluck('batch')->sort();
            $groups = StudentDetail::distinct()->pluck('group')->sort();

            // Get the selected date for the view
            $selectedDate = $request->date ?? now()->format('Y-m-d');
             $selectedDateCarbon = \Carbon\Carbon::parse($selectedDate);
            $isSunday = $selectedDateCarbon->isSunday();

            // If no logs found, create empty records for monitors to work with
            if ($logs->isEmpty()) {
                $allStudents = \App\Models\StudentDetail::all();

                foreach ($allStudents as $student) {
                    if ($isSunday) {
                        // Sunday: Create only one session
                        Going_out::firstOrCreate([
                            'student_id' => $student->student_id,
                            'going_out_date' => $selectedDate,
                            'session_number' => 1,
                        ], [
                            'session_status' => 'active',
                            'time_out' => null,
                            'time_out_remark' => null,
                            'time_in' => null,
                            'time_in_remark' => null,
                        ]);
                    } else {
                        // Monday-Saturday: Create one empty session for monitor interface
                        Going_out::firstOrCreate([
                            'student_id' => $student->student_id,
                            'going_out_date' => $selectedDate,
                            'session_number' => 1,
                        ], [
                            'session_status' => 'active',
                            'time_out' => null,
                            'time_out_remark' => null,
                            'time_in' => null,
                            'time_in_remark' => null,
                        ]);
                    }
                }

                // Reload logs after creating empty records
                $logs = Going_out::with(['studentDetail.user'])
                    ->whereDate('going_out_date', $selectedDate)
                    ->orderBy('session_number', 'desc')
                    ->get()
                    ->sortByDesc(function ($log) {
                        $timeOutTimestamp = $log->time_out ? strtotime($log->going_out_date . ' ' . $log->time_out) : 0;
                        $timeInTimestamp = $log->time_in ? strtotime($log->going_out_date . ' ' . $log->time_in) : 0;
                        return max($timeOutTimestamp, $timeInTimestamp);
                    })
                    ->values();
            }


            return view('user-monitor.goingout-logs', compact('logs', 'batches', 'groups', 'selectedDate'));
        } catch (\Exception $e) {
            Log::error('Monitor error loading going out logs:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
            ]);

            return back()->with('error', 'Failed to load going out logs: ' . $e->getMessage());
        }
    }

    public function visitorLogs(Request $request)
    {
        try {
            $query = Visitor::with('monitor')
                ->whereDate('visitor_date', now()->format('Y-m-d'))
                ->orderBy('created_at', 'desc');

            if ($request->filled('date')) {
                $query->whereDate('visitor_date', $request->date);
            }

            $logs = $query->get();

            // Since visitors don't have student relationships, we'll provide empty arrays for filters
            $batches = collect();
            $groups = collect();

            // Get the selected date for the view
            $selectedDate = $request->date ?? now()->format('Y-m-d');

            return view('user-monitor.visitor-logs', compact('logs', 'batches', 'groups', 'selectedDate'));
        } catch (\Exception $e) {
            Log::error('Monitor error loading visitor logs:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return back()->with('error', 'Failed to load visitor logs: ' . $e->getMessage());
        }
    }

    public function updateAcademicConsideration(Request $request, $id)
    {
        try {
            $request->validate([
                'consideration' => 'required|in:Excused,Not Excused,Absent',
                'reason' => 'required|string|max:500',
                'consideration_type' => 'required|in:time_in,time_out'
            ]);

            $academic = Academic::findOrFail($id);
            $considerationType = $request->consideration_type;

            $monitorId = session('user.user_id');

            // Debug logging
            Log::info('Monitor consideration update', [
                'monitor_id' => $monitorId,
                'session_user' => session('user'),
                'consideration_type' => $considerationType,
                'consideration' => $request->consideration,
                'reason' => $request->reason
            ]);

            if ($considerationType === 'time_out') {
                $updateFields = [
                    'time_out_consideration' => $request->consideration,
                    'time_out_reason' => $request->reason,
                    'updated_by' => session('user.user_fname') . ' ' . session('user.user_lname'),
                    'updated_at' => now()
                ];
                $logMessage = 'Updated academic time out consideration';

                // If Absent is selected, also set the time_in consideration
                if ($request->consideration === 'Absent') {
                    $updateFields['educator_consideration'] = 'Absent';
                    $updateFields['time_in_reason'] = $request->reason;

                    $logMessage = 'Updated academic time out consideration and auto-synced time in (Absent)';
                }
            } else {
                $updateFields = [
                    'educator_consideration' => $request->consideration,
                    'time_in_reason' => $request->reason,
                    'updated_by' => session('user.user_fname') . ' ' . session('user.user_lname'),
                    'updated_at' => now()
                ];
                $logMessage = 'Updated academic time in consideration';

                // If Absent is selected, also set the time_out consideration
                if ($request->consideration === 'Absent') {
                    $updateFields['time_out_consideration'] = 'Absent';
                    $updateFields['time_out_reason'] = $request->reason;

                    $logMessage = 'Updated academic time in consideration and auto-synced time out (Absent)';
                }
            }

            DB::beginTransaction();

            try {
                $academic->update($updateFields);

                DB::commit();
                Log::info($logMessage, [
                    'academic_id' => $id,
                    'student_id' => $academic->student_id,
                    'consideration_type' => $considerationType,
                    'consideration' => $request->consideration,
                    'reason' => $request->reason
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Consideration updated successfully.'
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('Failed to update academic consideration', [
                'error' => $e->getMessage(),
                'academic_id' => $id,
                'consideration_type' => $request->consideration_type ?? 'unknown'
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to update consideration: ' . $e->getMessage()
            ]);
        }
    }

    public function updateAbsentValidation(Request $request, $id)
    {
        try {
            Log::info('updateAbsentValidation called', [
                'id' => $id,
                'request_data' => $request->all(),
                'user' => Auth::user() ? Auth::user()->user_id : 'not authenticated'
            ]);

            $request->validate([
                'validation' => 'required|in:valid,not_valid',
                'consideration_type' => 'required|in:time_in,time_out'
            ]);

            $academic = Academic::findOrFail($id);
            $considerationType = $request->consideration_type;

            // Check if the consideration is actually "Absent"
            if ($considerationType === 'time_out' && $academic->time_out_consideration !== 'Absent') {
                return response()->json([
                    'success' => false,
                    'message' => 'Can only validate absent considerations.'
                ]);
            }

            if ($considerationType === 'time_in' && $academic->educator_consideration !== 'Absent') {
                return response()->json([
                    'success' => false,
                    'message' => 'Can only validate absent considerations.'
                ]);
            }

            $updateField = $considerationType === 'time_out' ? 'time_out_absent_validation' : 'time_in_absent_validation';
            $otherField = $considerationType === 'time_out' ? 'time_in_absent_validation' : 'time_out_absent_validation';

            DB::beginTransaction();

            try {
                // Update the current validation field
                $updateData = [
                    $updateField => $request->validation,
                    'updated_by' => session('user.user_fname') . ' ' . session('user.user_lname'),
                    'updated_at' => now()
                ];

                // Auto-sync: If the other consideration is also "Absent", update its validation too
                $otherConsideration = $considerationType === 'time_out' ? $academic->educator_consideration : $academic->time_out_consideration;
                $autoSynced = false;
                if ($otherConsideration === 'Absent') {
                    $updateData[$otherField] = $request->validation;
                    $autoSynced = true;
                }

                $academic->update($updateData);

                DB::commit();
                Log::info('Updated absent validation', [
                    'academic_id' => $id,
                    'student_id' => $academic->student_id,
                    'consideration_type' => $considerationType,
                    'validation' => $request->validation,
                    'auto_synced' => $autoSynced
                ]);

                $message = $autoSynced
                    ? 'Absent validation updated successfully. Both log in and log out validations have been synced.'
                    : 'Absent validation updated successfully.';

                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'auto_synced' => $autoSynced
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('Failed to update absent validation', [
                'error' => $e->getMessage(),
                'academic_id' => $id,
                'consideration_type' => $request->consideration_type ?? 'unknown'
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to update validation: ' . $e->getMessage()
            ]);
        }
    }

    public function updateGoingoutConsideration(Request $request, $id)
    {
        try {
            $request->validate([
                'consideration' => 'required|in:Excused,Not Excused,Not going out',
                'reason' => 'required|string|max:500',
                'consideration_type' => 'required|in:time_in,time_out'
            ]);

            $goingOut = Going_out::findOrFail($id);
            $considerationType = $request->consideration_type;

            $monitorId = Auth::user() ? Auth::user()->user_id : null;

            // Debug logging
            Log::info('Going out monitor consideration update', [
                'monitor_id' => $monitorId,
                'auth_user' => Auth::user(),
                'consideration_type' => $considerationType,
                'consideration' => $request->consideration,
                'reason' => $request->reason
            ]);

            if ($considerationType === 'time_out') {
                $updateFields = [
                    'time_out_consideration' => $request->consideration,
                    'time_out_reason' => $request->reason,
                    'updated_by' => session('user.user_fname') . ' ' . session('user.user_lname'),
                    'updated_at' => now()
                ];
                $logMessage = 'Updated going out time out consideration';

                // If Not going out is selected, also set the time_in consideration
                if ($request->consideration === 'Not going out') {
                    $updateFields['educator_consideration'] = 'Not going out';
                    $updateFields['time_in_reason'] = $request->reason;

                    $logMessage = 'Updated going out time out consideration and auto-synced time in (Not going out)';
                }
            } else {
                $updateFields = [
                    'educator_consideration' => $request->consideration,
                    'time_in_reason' => $request->reason,
                    'updated_by' => session('user.user_fname') . ' ' . session('user.user_lname'),
                    'updated_at' => now()
                ];
                $logMessage = 'Updated going out time in consideration';

                // If Not going out is selected, also set the time_out consideration
                if ($request->consideration === 'Not going out') {
                    $updateFields['time_out_consideration'] = 'Not going out';
                    $updateFields['time_out_reason'] = $request->reason;

                    $logMessage = 'Updated going out time in consideration and auto-synced time out (Not going out)';
                }
            }

            DB::beginTransaction();

            try {
                $goingOut->update($updateFields);

                DB::commit();
                Log::info($logMessage, [
                    'going_out_id' => $id,
                    'student_id' => $goingOut->student_id,
                    'consideration_type' => $considerationType,
                    'consideration' => $request->consideration,
                    'reason' => $request->reason
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Consideration updated successfully.'
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('Failed to update going out consideration', [
                'error' => $e->getMessage(),
                'going_out_id' => $id,
                'consideration_type' => $request->consideration_type ?? 'unknown'
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to update consideration: ' . $e->getMessage()
            ]);
        }
    }

    public function updateVisitorConsideration(Request $request, $id)
    {
        try {
            $request->validate([
                'consideration' => 'required|in:Excused,Not Excused,Absent',
                'reason' => 'required|string|max:500'
            ]);

            $visitor = Visitor::findOrFail($id);

            DB::beginTransaction();

            try {
                $visitor->update([
                    'consideration' => $request->consideration,
                    'reason' => $request->reason,
                    'monitor_id' => Auth::user() ? Auth::user()->user_id : null
                ]);

                DB::commit();
                Log::info('Updated visitor consideration', [
                    'visitor_id' => $id,
                    'student_id' => $visitor->student_id,
                    'consideration' => $request->consideration,
                    'reason' => $request->reason
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Consideration updated successfully.'
                ]);
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            Log::error('Failed to update visitor consideration', [
                'error' => $e->getMessage(),
                'visitor_id' => $id
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to update consideration: ' . $e->getMessage()
            ]);
        }
    }

    public function performAcademicLogout(Request $request, $id)
    {
        try {
            $academic = Academic::findOrFail($id);

            // Check if already logged out
            if ($academic->time_out) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student has already logged out.'
                ]);
            }

            $currentTime = Carbon::parse(now());
            $today = $currentTime->format('l'); // Day name (Monday, Tuesday, etc.)

            // Get schedule for this student using the same logic as student logging
            $student = $academic->studentDetail;
            $scheduleResult = $this->getCurrentActiveSchedule($student, $today);
            $schedule = $scheduleResult['schedule'];
            $scheduleType = $scheduleResult['type'];

            // Check if schedule exists for today
            if (!$schedule) {
                Log::warning('Monitor attempted academic logout with no schedule', [
                    'student_id' => $academic->student_id,
                    'batch' => $student->batch,
                    'group' => $student->group,
                    'day' => $today,
                    'monitor_id' => Auth::user() ? Auth::user()->user_id : null
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'No schedule set for today! Cannot perform logout operation.'
                ]);
            }

            // Check if it's too late to logout (after scheduled end time - NO GRACE PERIOD)
            $scheduleEndTime = \Carbon\Carbon::parse($schedule->time_in); // When academic period ends

            if ($currentTime > $scheduleEndTime) {
                Log::warning('Monitor attempted academic logout after schedule ended', [
                    'student_id' => $academic->student_id,
                    'current_time' => $currentTime->format('H:i:s'),
                    'schedule_end_time' => $scheduleEndTime->format('H:i:s'),
                    'monitor_id' => Auth::user() ? Auth::user()->user_id : null
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Time out period has ended! Please contact your educator for assistance.'
                ]);
            }

            // Academic logs: Determine remark based on schedule with 15-min grace period
            $scheduleStartTime = Carbon::parse($schedule->time_out); // When students should start logging out

            // 15-minute grace period logic
            $remark = 'On Time';

            $gracePeriodMinutes = $schedule->grace_period_logout_minutes;

            if ($gracePeriodMinutes !== null) {
                if ($currentTime->lessThan($scheduleStartTime->copy()->subMinutes($gracePeriodMinutes))) {
                    $remark = 'Early';
                } elseif ($currentTime->greaterThan($scheduleStartTime)) {
                    $remark = 'Late';
                } else {
                    $remark = 'On Time';
                }
            } else {
                if ($currentTime->lessThan($scheduleStartTime)) {
                    $remark = 'Early';
                } elseif ($currentTime->greaterThan($scheduleStartTime)) {
                    $remark = 'Late';
                } else {
                    $remark = 'On Time';
                }
            }

            $created_by = session('user.user_fname') . ' ' . session('user.user_lname');

            // Update the academic log
            $academic->update([
                'time_out' => $currentTime->format('H:i:s'),
                'time_out_remark' => $remark,
                'created_by' => $created_by,
                'created_at' => now()
            ]);

            Log::info('Monitor performed academic logout for student', [
                'academic_id' => $id,
                'student_id' => $academic->student_id,
                'time_out' => $currentTime->format('H:i:s'),
                'remark' => $remark,
                'schedule_type' => $scheduleType,
                'schedule_id' => $schedule->schedule_id,
                'schedule_time_out' => $schedule->time_out,
                'grace_period_logout' => $schedule->grace_period_logout_minutes,
                'monitor_id' => Auth::user() ? Auth::user()->user_id : null
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Student logged out successfully.',
                'time_out' => $currentTime->format('g:i A'),
                'remark' => $remark,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to perform academic logout', [
                'error' => $e->getMessage(),
                'academic_id' => $id
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to log out student: ' . $e->getMessage()
            ]);
        }
    }

    public function performAcademicLogin(Request $request, $id)
    {
        try {
            $academic = Academic::findOrFail($id);

            // Check if not logged out yet
            if (!$academic->time_out) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student must log out first before logging in.'
                ]);
            }

            // Check if already logged in
            if ($academic->time_in) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student has already logged in.'
                ]);
            }

            $currentTime = Carbon::parse(now());
            $today = $currentTime->format('l'); // Day name (Monday, Tuesday, etc.)

            // Get schedule for this student using the same logic as student logging
            $student = $academic->studentDetail;
            $scheduleResult = $this->getCurrentActiveSchedule($student, $today);
            $schedule = $scheduleResult['schedule'];
            $scheduleType = $scheduleResult['type'];

            // Check if schedule exists for today
            if (!$schedule) {
                Log::warning('Monitor attempted academic login with no schedule', [
                    'student_id' => $academic->student_id,
                    'batch' => $student->batch,
                    'group' => $student->group,
                    'day' => $today,
                    'monitor_id' => Auth::user() ? Auth::user()->user_id : null
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'No schedule set for today! Cannot perform login operation.'
                ]);
            }

            // Academic logs: Allow login anytime, determine remark based on schedule with dynamic grace period
            $scheduleEndTime = Carbon::parse($schedule->time_in); // When students should return

            // Get login grace period from schedule (no default - null means no grace period)
            $gracePeriodMinutes = $schedule->grace_period_login_minutes;

            // Dynamic grace period logic
            $remark = 'On Time'; // Default
            if ($gracePeriodMinutes !== null) {
                if ($currentTime->lessThan($scheduleEndTime)) {
                    $remark = 'Early';
                } elseif ($currentTime->greaterThan($scheduleEndTime->copy()->addMinutes($gracePeriodMinutes))) {
                    $remark = 'Late';
                } else {
                    $remark = 'On Time'; // Within grace period
                }
            } else {
                // No grace period - exact timing
                if ($currentTime->lessThan($scheduleEndTime)) {
                    $remark = 'Early';
                } elseif ($currentTime->greaterThan($scheduleEndTime)) {
                    $remark = 'Late';
                } else {
                    $remark = 'On Time'; // Exact time
                }
            }

            $updated_by = session('user.user_fname') . ' ' . session('user.user_lname');

            // Update the academic log
            $academic->update([
                'time_in' => $currentTime->format('H:i:s'),
                'time_in_remark' => $remark,
                'updated_by' => $updated_by,
                'updated_at' => now()
            ]);

            Log::info('Monitor performed academic login for student', [
                'academic_id' => $id,
                'student_id' => $academic->student_id,
                'time_in' => $currentTime->format('H:i:s'),
                'remark' => $remark,
                'schedule_type' => $scheduleType,
                'schedule_id' => $schedule->schedule_id,
                'schedule_time_in' => $schedule->time_in,
                'grace_period_login' => $schedule->grace_period_login_minutes,
                'monitor_id' => Auth::user() ? Auth::user()->user_id : null
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Student logged in successfully.',
                'time_in' => $currentTime->format('g:i A'),
                'remark' => $remark
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to perform academic login', [
                'error' => $e->getMessage(),
                'academic_id' => $id
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to log in student: ' . $e->getMessage()
            ]);
        }
    }

    public function performGoingoutLogout(Request $request, $id)
    {
        try {
            // Validate destination and purpose
            $request->validate([
                'destination' => 'required|string|max:100',
                'purpose' => 'required|string|max:100'
            ]);

            $goingOut = Going_out::findOrFail($id);

            // Check if already logged out
            if ($goingOut->time_out) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student has already logged out.'
                ]);
            }

            $currentTime = now();
            $today = $currentTime->format('l'); // Day name (Monday, Tuesday, etc.)

            // Get schedule for this student - check individual going-out schedule first
            $student = $goingOut->studentDetail;

            // First check for individual going-out schedule for this specific student
            $schedule = DB::table('schedules')
                ->where('student_id', $student->student_id)
                ->where('day_of_week', $today)
                ->where('schedule_type', 'going_out') // Only look for going-out schedules
                ->where(function ($query) {
                    $query->whereNull('valid_until')
                        ->orWhereDate('valid_until', '>=', now()->toDateString());
                })
                ->orderBy('updated_at', 'desc')
                ->first();

            // If no individual schedule found, fall back to general going-out schedule
            if (!$schedule) {
                $schedule = DB::table('schedules')
                    ->where('gender', $student->user->gender)
                    ->where('day_of_week', $today)
                    ->whereNull('student_id') // General schedules have null student_id
                    ->where('schedule_type', 'going_out') // Only look for going-out schedules
                    ->where(function ($query) {
                        $query->whereNull('valid_until')
                            ->orWhereDate('valid_until', '>=', now()->toDateString());
                    })
                    ->orderBy('updated_at', 'desc')
                    ->first();
            }

            // Check if schedule exists for today
            if (!$schedule) {
                Log::warning('Monitor attempted going out logout with no schedule', [
                    'student_id' => $goingOut->student_id,
                    'gender' => $student->user->gender,
                    'day' => $today,
                    'monitor_id' => Auth::user() ? Auth::user()->user_id : null
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'No schedule set for today! Cannot perform logout operation.'
                ]);
            }

            // Apply same time restrictions as student-side
            $scheduleStartTime = \Carbon\Carbon::parse($schedule->time_out);
            $scheduleEndTime = \Carbon\Carbon::parse($schedule->time_in);

            // Check if it's not scheduled time yet
            if ($currentTime->format('H:i:s') < $scheduleStartTime->format('H:i:s')) {
                Log::info('Monitor attempted going out logout before scheduled time', [
                    'student_id' => $goingOut->student_id,
                    'current_time' => $currentTime->format('H:i:s'),
                    'schedule_start_time' => $scheduleStartTime->format('H:i:s'),
                    'monitor_id' => Auth::user() ? Auth::user()->user_id : null
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Not scheduled time yet! Going out starts at ' . $scheduleStartTime->format('g:i A') . '.'
                ]);
            }

            // Check if it's too late to logout (after scheduled end time - NO GRACE PERIOD)
            if ($currentTime->format('H:i:s') > $scheduleEndTime->format('H:i:s')) {
                Log::warning('Monitor attempted going out logout after schedule ended', [
                    'student_id' => $goingOut->student_id,
                    'current_time' => $currentTime->format('H:i:s'),
                    'schedule_end_time' => $scheduleEndTime->format('H:i:s'),
                    'monitor_id' => Auth::user() ? Auth::user()->user_id : null
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Time out period has ended! Please contact your educator for assistance.'
                ]);
            }

            // Determine remark based on schedule with strict timing (no grace period for going out)
            $scheduleStartTime = \Carbon\Carbon::parse($currentTime->toDateString() . ' ' . $schedule->time_out);
            $scheduleEndTime = \Carbon\Carbon::parse($currentTime->toDateString() . ' ' . $schedule->time_in);

            // Check if it's too early to logout (before scheduled start time)
            if ($currentTime->lessThan($scheduleStartTime)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Going out period has not started yet! Going out starts at ' . $scheduleStartTime->format('g:i A') . '.'
                ]);
            }

            // Check if it's too late to logout (after scheduled end time)
            if ($currentTime->greaterThan($scheduleEndTime)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Going out period has ended! Going out ended at ' . $scheduleEndTime->format('g:i A') . '.'
                ]);
            }

            // If we reach here, monitor is logging out during valid schedule period
            $remark = 'On Time'; // Always "On Time" since only allowed during scheduled period

            $created_by = session('user.user_fname') . ' ' . session('user.user_lname');

            // Update the going out log with destination and purpose
            $goingOut->update([
                'destination' => $request->destination,
                'purpose' => $request->purpose,
                'time_out' => $currentTime->format('H:i:s'),
                'time_out_remark' => $remark,
                'created_by' => $created_by,
                'created_at' => now()
            ]);

            Log::info('Monitor performed going out logout for student', [
                'going_out_id' => $id,
                'student_id' => $goingOut->student_id,
                'destination' => $request->destination,
                'purpose' => $request->purpose,
                'time_out' => $currentTime->format('H:i:s'),
                'remark' => $remark,
                'monitor_id' => Auth::user() ? Auth::user()->user_id : null
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Student logged out successfully.',
                'time_out' => $currentTime->format('g:i A'),
                'remark' => $remark
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to perform going out logout', [
                'error' => $e->getMessage(),
                'going_out_id' => $id
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to log out student: ' . $e->getMessage()
            ]);
        }
    }

    public function performGoingoutLogin(Request $request, $id)
    {
        try {
            $goingOut = Going_out::findOrFail($id);

            // Check if not logged out yet
            if (!$goingOut->time_out) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student must log out first before logging in.'
                ]);
            }

            // Check if already logged in
            if ($goingOut->time_in) {
                return response()->json([
                    'success' => false,
                    'message' => 'Student has already logged in.'
                ]);
            }

            $currentTime = now();
            $today = $currentTime->format('l'); // Day name (Monday, Tuesday, etc.)

            // Get schedule for this student - check individual going-out schedule first
            $student = $goingOut->studentDetail;

            // First check for individual going-out schedule for this specific student
            $schedule = DB::table('schedules')
                ->where('student_id', $student->student_id)
                ->where('day_of_week', $today)
                ->where('schedule_type', 'going_out') // Only look for going-out schedules
                ->where(function ($query) {
                    $query->whereNull('valid_until')
                        ->orWhereDate('valid_until', '>=', now()->toDateString());
                })
                ->orderBy('updated_at', 'desc')
                ->first();

            // If no individual schedule found, fall back to general going-out schedule
            if (!$schedule) {
                $schedule = DB::table('schedules')
                    ->where('gender', $student->user->gender)
                    ->where('day_of_week', $today)
                    ->whereNull('student_id') // General schedules have null student_id
                    ->where('schedule_type', 'going_out') // Only look for going-out schedules
                    ->where(function ($query) {
                        $query->whereNull('valid_until')
                            ->orWhereDate('valid_until', '>=', now()->toDateString());
                    })
                    ->orderBy('updated_at', 'desc')
                    ->first();
            }

            // Going out login: No time restrictions - can log in anytime after logout
            // Determine remark based on schedule with simple timing (no grace period)
            $remark = 'On Time'; // Default
            if ($schedule) {
               $scheduleEndTime = Carbon::parse($schedule->time_in);

                // Simple timing logic for going out login (no grace period)
                // On Time: before or at scheduled end time
                // Late: after scheduled end time
                if ($currentTime->greaterThan($scheduleEndTime)) {
                    $remark = 'Late';
                } else {
                    $remark = 'On Time';
                }
            }

            $updated_by = session('user.user_fname') . ' ' . session('user.user_lname');

            // Update the going out log and mark session as completed
            $goingOut->update([
                'time_in' => $currentTime->format('H:i:s'),
                'time_in_remark' => $remark,
                'session_status' => 'completed',
                'updated_by' => $updated_by,
                'updated_at' => now()
            ]);

            Log::info('Monitor performed going out login for student', [
                'going_out_id' => $id,
                'student_id' => $goingOut->student_id,
                'time_in' => $currentTime->format('H:i:s'),
                'remark' => $remark,
                'monitor_id' => Auth::user() ? Auth::user()->user_id : null
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Student logged in successfully.',
                'time_in' => $currentTime->format('g:i A'),
                'remark' => $remark
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to perform going out login', [
                'error' => $e->getMessage(),
                'going_out_id' => $id
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to log in student: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get the most current active schedule for a student
     * This method ensures we always get the latest schedule that should be followed
     * Same logic as AcademicLogController to ensure consistency
     */
    private function getCurrentActiveSchedule($student, $today)
    {
        Log::info('Monitor fetching current active schedule', [
            'student_id' => $student->student_id,
            'batch' => $student->batch,
            'group' => $student->group,
            'day' => $today,
            'query_time' => now()->format('Y-m-d H:i:s.u')
        ]);

        // First priority: Check for irregular schedule (student-specific)
        $irregularSchedule = Schedule::where([
            ['student_id', $student->student_id],
            ['day_of_week', $today],
            ['schedule_type', 'academic'] // Only look for academic irregular schedules
        ])->where(function ($query) {
            $query->whereNull('valid_until') // Permanent schedule
                ->orWhere('valid_until', '>=', Carbon::today()); // Or valid today/future
        })
        ->orderBy('updated_at', 'desc') // Most recently updated first
        ->orderBy('created_at', 'desc')  // Then most recently created
        ->first();

        if ($irregularSchedule) {
            Log::info('Monitor using irregular schedule for student', [
                'student_id' => $student->student_id,
                'schedule_id' => $irregularSchedule->schedule_id,
                'time_out' => $irregularSchedule->time_out,
                'time_in' => $irregularSchedule->time_in,
                'updated_at' => $irregularSchedule->updated_at,
                'valid_until' => $irregularSchedule->valid_until,
                'query_time' => now()->format('Y-m-d H:i:s.u')
            ]);

            return ['schedule' => $irregularSchedule, 'type' => 'irregular'];
        }

        // Second priority: Check for batch schedule (group-based)
        $batchSchedule = Schedule::where([
            ['batch', $student->batch],
            ['pn_group', $student->group],
            ['day_of_week', $today],
            ['schedule_type', 'academic'] // Only look for academic batch schedules
        ])->where(function ($query) {
            $query->whereNull('valid_until') // Permanent schedule
                ->orWhere('valid_until', '>=', Carbon::today()); // Or valid today/future
        })
        ->orderBy('updated_at', 'desc') // Most recently updated first
        ->orderBy('created_at', 'desc')  // Then most recently created
        ->first();

        if ($batchSchedule) {
            Log::info('Monitor using batch schedule for student', [
                'student_id' => $student->student_id,
                'batch' => $student->batch,
                'group' => $student->group,
                'schedule_id' => $batchSchedule->schedule_id,
                'time_out' => $batchSchedule->time_out,
                'time_in' => $batchSchedule->time_in,
                'updated_at' => $batchSchedule->updated_at,
                'valid_until' => $batchSchedule->valid_until,
                'query_time' => now()->format('Y-m-d H:i:s.u')
            ]);

            return ['schedule' => $batchSchedule, 'type' => 'batch'];
        }

        Log::warning('Monitor found no active schedule for student', [
            'student_id' => $student->student_id,
            'batch' => $student->batch,
            'group' => $student->group,
            'day' => $today,
            'query_time' => now()->format('Y-m-d H:i:s.u')
        ]);

        return ['schedule' => null, 'type' => 'none'];
    }
}
